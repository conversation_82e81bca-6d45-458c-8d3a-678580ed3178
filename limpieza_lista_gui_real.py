    
    def update_handlers_config(self):
        """Actualizar handlers con configuración v2.3"""
        if not self.config:
            return
        
        try:
            # Cargar ubicaciones
            locations = self.config.get_enabled_locations()
            self.init_handlers(locations)
            
            # Cargar extensiones v2.3
            self.load_extension_config()
            self.update_extension_info()
            
            self.logger.info(f"Configuración v2.3 cargada: {len(locations)} ubicaciones")
            
        except Exception as e:
            self.logger.error(f"Error cargando configuración: {e}")
    
    def init_handlers(self, locations):
        """Inicializar handlers v2.3"""
        self.handlers = {}
        
        for location_id, location_config in locations.items():
            try:
                network_path = location_config.get('network_path', '')
                
                if 'qnap' in location_id.lower():
                    # Handler QNAP v2.3
                    self.handlers[location_id] = QnapHandler(location_id, self.config)
                else:
                    # Handler PC v2.3
                    self.handlers[location_id] = <PERSON>Handler(location_id, self.config)
                
                self.logger.debug(f"Handler v2.3 creado: {location_id}")
                
            except Exception as e:
                self.logger.error(f"Error creando handler {location_id}: {e}")
    
    def log_to_ui(self, message):
        """Log a UI"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_line = f"[{timestamp}] {message}\n"
            
            self.logs_text.config(state='normal')
            self.logs_text.insert(tk.END, log_line)
            self.logs_text.see(tk.END)
            self.logs_text.config(state='disabled')
            
        except Exception as e:
            print(f"Error logging: {e}")
    
    def on_closing(self):
        """Manejar cierre"""
        if self.running:
            if messagebox.askokcancel("Salir", "¿Detener proceso y salir?"):
                self.running = False
                self.root.destroy()
        else:
            self.root.destroy()

def main():
    """Función principal v2.3"""
    print("🚀 Gestor de Archivos - Limpieza v2.3")
    print("=" * 40)
    print("✨ Características v2.3:")
    print("   - Extensiones configurables")
    print("   - Búsqueda recursiva") 
    print("   - Validación avanzada")
    print("   - Handlers optimizados")
    print()
    
    try:
        app = CleanupByListGUI()
        app.root.protocol("WM_DELETE_WINDOW", app.on_closing)
        
        print("🎯 Aplicación v2.3 iniciada")
        app.root.mainloop()
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--version":
            print("Gestor de Archivos Corporativo")
            print("Limpieza por Lista v2.3")
            print("Julio 2025 - Extensiones Configurables")
            sys.exit(0)
        elif sys.argv[1] == "--help":
            print("Uso: python limpieza_lista_gui_real.py [opciones]")
            print()
            print("Opciones:")
            print("  --version  Mostrar versión")
            print("  --help     Mostrar ayuda")
            print()
            sys.exit(0)
    
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Cerrado por usuario")
        sys.exit(0)
    except Exception as e:
        print(f"💥 Error fatal: {e}")
        sys.exit(1)
            return
        
        try:
            # Recrear handlers con config v2.3
            for location_id in self.handlers:
                old_handler = self.handlers[location_id]
                
                if hasattr(old_handler, 'device_type') and old_handler.device_type == "QNAP":
                    self.handlers[location_id] = QnapHandler(location_id, self.config)
                else:
                    self.handlers[location_id] = PCHandler(location_id, self.config)
                
                self.logger.info(f"Handler {location_id} actualizado v2.3")
            
        except Exception as e:
            self.logger.error(f"Error actualizando handlers: {e}")
    
    # ===========================================
    # MÉTODOS PRINCIPALES
    # ===========================================
    
    def browse_file(self):
        """Examinar archivo de lista"""
        filename = filedialog.askopenfilename(
            title="Seleccionar archivo de lista",
            filetypes=[
                ("Archivos de texto", "*.txt"),
                ("Todos los archivos", "*.*")
            ]
        )
        
        if filename:
            self.archivo_lista.set(filename)
            self.status_var.set(f"Archivo seleccionado: {os.path.basename(filename)}")
            self.logger.info(f"Archivo seleccionado: {filename}")
    
    def start_cleanup(self):
        """Iniciar limpieza v2.3"""
        if not self.licensed:
            messagebox.showerror("Error", "Licencia requerida")
            return
        
        filename = self.archivo_lista.get()
        if not filename or not os.path.exists(filename):
            messagebox.showwarning("Advertencia", "Selecciona archivo válido")
            return
        
        if not self.handlers:
            messagebox.showwarning("Advertencia", "No hay ubicaciones configuradas")
            return
        
        # Confirmación con info v2.3
        mode_text = "PRUEBA" if self.dry_run.get() else "REAL"
        recursive_text = "con recursión" if self.recursive_search.get() else "sin recursión"
        
        ext_info = ""
        if self.config:
            try:
                ext_config = self.config.get_file_extensions_config()
                ext_info = f"\n\nExtensiones: {ext_config['mode']}"
                if ext_config['mode'] == 'whitelist':
                    ext_info += f" - Solo: {ext_config['whitelist']}"
                elif ext_config['mode'] == 'blacklist':
                    ext_info += f" - Excepto: {ext_config['blacklist']}"
            except:
                pass
        
        if not messagebox.askyesno("Confirmar v2.3", 
            f"Iniciar limpieza en modo {mode_text} {recursive_text}?\n\n" +
            f"Archivo: {os.path.basename(filename)}\n" +
            f"Ubicaciones: {len(self.handlers)}{ext_info}"):
            return
        
        # Iniciar proceso
        self.running = True
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        
        # Thread de limpieza v2.3
        self.cleanup_thread = threading.Thread(
            target=self._cleanup_thread_v23, 
            args=(filename,), 
            daemon=True
        )
        self.cleanup_thread.start()
        
        self.logger.info(f"Iniciando limpieza v2.3 - {mode_text} - {recursive_text}")
    
    def stop_cleanup(self):
        """Detener limpieza"""
        self.running = False
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.status_var.set("Proceso detenido")
        self.logger.info("Limpieza detenida por usuario")
    
    def _cleanup_thread_v23(self, filename):
        """Thread principal de limpieza v2.3"""
        stats = {
            'total_files': 0,
            'found_files': 0,
            'deleted_files': 0,
            'blocked_by_extension': 0,  # v2.3
            'failed_files': 0,
            'not_found_files': 0,
            'errors': [],
            'deleted_list': [],
            'blocked_list': []  # v2.3
        }
        
        try:
            # Leer archivo
            self.root.after(0, lambda: self.log_to_ui("📁 Leyendo archivo v2.3..."))
            
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            files_to_process = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    files_to_process.append(line)
            
            stats['total_files'] = len(files_to_process)
            
            self.root.after(0, lambda: self.log_to_ui(
                f"📊 Total archivos: {len(files_to_process)}"))
            
            # v2.3 - Análisis previo de extensiones
            if self.config:
                try:
                    blocked_count = 0
                    for file_item in files_to_process:
                        if not self.config.is_file_extension_allowed(file_item):
                            blocked_count += 1
                    
                    self.root.after(0, lambda: self.log_to_ui(
                        f"🎯 Análisis v2.3: ~{blocked_count} archivos serán bloqueados por extensión"))
                except:
                    pass
            
            # Procesar ubicaciones
            for location_id, handler in self.handlers.items():
                if not self.running:
                    break
                
                self.root.after(0, lambda loc=location_id: 
                    self.log_to_ui(f"🌐 Procesando: {loc}"))
                
                try:
                    if not handler.connect():
                        error_msg = f"Error conectando a {location_id}"
                        stats['errors'].append(error_msg)
                        self.root.after(0, lambda msg=error_msg: 
                            self.log_to_ui(f"❌ {msg}"))
                        continue
                    
                    # v2.3 - Usar bulk_delete_files con validación
                    if hasattr(handler, 'bulk_delete_files'):
                        self.root.after(0, lambda: 
                            self.log_to_ui("🔄 Usando procesamiento v2.3..."))
                        
                        location_stats = handler.bulk_delete_files(
                            files_to_process, 
                            "", 
                            self.recursive_search.get()
                        )
                        
                        # Consolidar stats
                        stats['found_files'] += location_stats.get('found_files', 0)
                        stats['deleted_files'] += location_stats.get('deleted_files', 0)
                        stats['blocked_by_extension'] += location_stats.get('blocked_by_extension', 0)
                        stats['failed_files'] += location_stats.get('failed_files', 0)
                        stats['errors'].extend(location_stats.get('errors', []))
                        stats['deleted_list'].extend(location_stats.get('deleted_list', []))
                        stats['blocked_list'].extend(location_stats.get('blocked_list', []))
                        
                        # Log resultado
                        self.root.after(0, lambda loc=location_id, ls=location_stats: 
                            self.log_to_ui(
                                f"📊 {loc}: {ls.get('deleted_files', 0)} eliminados, " +
                                f"{ls.get('blocked_by_extension', 0)} bloqueados"))
                    
                    else:
                        # Fallback para handlers sin v2.3
                        self.root.after(0, lambda: 
                            self.log_to_ui("⚠️ Handler legacy detectado"))
                
                except Exception as e:
                    error_msg = f"Error en {location_id}: {e}"
                    stats['errors'].append(error_msg)
                    self.root.after(0, lambda msg=error_msg: 
                        self.log_to_ui(f"❌ {msg}"))
            
            # Calcular no encontrados
            stats['not_found_files'] = (stats['total_files'] - 
                                       stats['found_files'] - 
                                       stats['blocked_by_extension'])
            
        except Exception as e:
            error_msg = f"Error crítico: {e}"
            stats['errors'].append(error_msg)
            self.root.after(0, lambda msg=error_msg: 
                self.log_to_ui(f"💥 {msg}"))
        
        finally:
            # Finalizar
            self.root.after(0, lambda: self._finalize_cleanup_v23(stats))
    
    def _finalize_cleanup_v23(self, stats):
        """Finalizar limpieza v2.3"""
        self.running = False
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        
        # Reporte final v2.3
        self.log_to_ui("\n" + "="*50)
        self.log_to_ui("📊 REPORTE FINAL v2.3")
        self.log_to_ui("="*50)
        
        self.log_to_ui(f"📁 Total archivos: {stats['total_files']}")
        self.log_to_ui(f"✅ Encontrados: {stats['found_files']}")
        self.log_to_ui(f"🗑️ Eliminados: {stats['deleted_files']}")
        self.log_to_ui(f"🎯 Bloqueados por extensión: {stats['blocked_by_extension']}")
        self.log_to_ui(f"❌ Errores: {stats['failed_files']}")
        self.log_to_ui(f"❓ No encontrados: {stats['not_found_files']}")
        
        # Eficiencia
        if stats['found_files'] > 0:
            efficiency = (stats['deleted_files'] / stats['found_files']) * 100
            self.log_to_ui(f"📈 Eficiencia: {efficiency:.1f}%")
        
        # Errores críticos
        if stats['errors']:
            self.log_to_ui(f"\n⚠️ Errores ({len(stats['errors'])}):")
            for error in stats['errors'][:5]:
                self.log_to_ui(f"   - {error}")
            if len(stats['errors']) > 5:
                self.log_to_ui(f"   ... y {len(stats['errors']) - 5} más")
        
        # v2.3 - Archivos bloqueados
        if stats['blocked_list']:
            self.log_to_ui(f"\n🚫 Bloqueados por extensión ({len(stats['blocked_list'])}):")
            for blocked in stats['blocked_list'][:5]:
                self.log_to_ui(f"   - {blocked}")
            if len(stats['blocked_list']) > 5:
                self.log_to_ui(f"   ... y {len(stats['blocked_list']) - 5} más")
        
        # Status final
        mode_text = "simulación" if self.dry_run.get() else "real"
        self.status_var.set(
            f"Completado ({mode_text}): {stats['deleted_files']} eliminados, " +
            f"{stats['blocked_by_extension']} bloqueados"
        )
        
        self.logger.info(
            f"Limpieza v2.3 completada - Eliminados: {stats['deleted_files']}, " +
            f"Bloqueados: {stats['blocked_by_extension']}"
        )
        
        # Diálogo final
        self._show_completion_dialog_v23(stats)
    
    def _show_completion_dialog_v23(self, stats):
        """Diálogo de finalización v2.3"""
        try:
            if stats['blocked_by_extension'] > stats['deleted_files']:
                title = "⚠️ Muchos Archivos Bloqueados"
                msg_type = "warning"
            elif stats['deleted_files'] > 0:
                title = "✅ Limpieza Exitosa"
                msg_type = "success"
            else:
                title = "ℹ️ Limpieza Completada"
                msg_type = "info"
            
            message = f"Proceso v2.3 completado:\n\n"
            message += f"✅ Eliminados: {stats['deleted_files']}\n"
            message += f"🎯 Bloqueados por extensión: {stats['blocked_by_extension']}\n"
            message += f"❌ Errores: {stats['failed_files']}\n"
            message += f"❓ No encontrados: {stats['not_found_files']}\n\n"
            
            if stats['blocked_by_extension'] > 0:
                message += "💡 Tip: Revisa configuración de extensiones si hay muchos bloqueados."
            
            if msg_type == "warning":
                messagebox.showwarning(title, message)
            elif msg_type == "success":
                messagebox.showinfo(title, message)
            else:
                messagebox.showinfo(title, message)
        
        except Exception as e:
            self.logger.error(f"Error en diálogo final: {e}")
    
    # ===========================================
    # MÉTODOS AUXILIARES
    # ===========================================
    
    def check_license(self):
        """Verificar licencia"""
        try:
            license_manager = GestorLicenseManager()
            
            if license_manager.verify_license("limpieza"):
                self.licensed = True
                self.status_var.set("Sistema licenciado v2.3 - Listo")
                self.logger.info("Licencia verificada")
            else:
                self.licensed = False
                result = show_license_dialog(self.root, "limpieza")
                
                if result:
                    self.licensed = True
                    self.status_var.set("Licencia activada v2.3")
                else:
                    self.status_var.set("Licencia requerida")
                    
        except Exception as e:
            self.logger.warning(f"Error verificando licencia: {e}")
            self.licensed = True  # Permitir en caso de error
    
    def load_default_config(self):
        """Cargar configuración por defecto"""
        if not self.config:
            return
        
        try:
            # Cargar ubicaciones
            locations = self.config.get_enabled_locations()
            self.init_handlers(locations)
            
            # Cargar extensiones v2.3
            self.load_extension_config()
            self.update_extension_info()
            
            self.logger.info(f"Configuración v2.3 cargada: {len(locations)} ubicaciones")
            
        except Exception as e:
            self.logger.error(f"Error cargando configuración: {e}")
    
    def init_handlers(self, locations):
        """Inicializar handlers v2.3"""
        self.handlers = {}
        
        for location_id, location_config in locations.items():
            try:
                network_path = location_config.get('network_path', '')
                
                if 'qnap' in location_id.lower():
                    # Handler QNAP v2.3
                    self.handlers[location_id] = QnapHandler(location_id, self.config)
                else:
                    # Handler PC v2.3
                    self.handlers[location_id] = PCHandler(location_id, self.config)
                
                self.logger.debug(f"Handler v2.3 creado: {location_id}")
                
            except Exception as e:
                self.logger.error(f"Error creando handler {location_id}: {e}")
    
    def log_to_ui(self, message):
        """Log a UI"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_line = f"[{timestamp}] {message}\n"
            
            self.logs_text.config(state='normal')
            self.logs_text.insert(tk.END, log_line)
            self.logs_text.see(tk.END)
            self.logs_text.config(state='disabled')
            
        except Exception as e:
            print(f"Error logging: {e}")
    
    def on_closing(self):
        """Manejar cierre"""
        if self.running:
            if messagebox.askokcancel("Salir", "¿Detener proceso y salir?"):
                self.running = False
                self.root.destroy()
        else:
            self.root.destroy()

def main():
    """Función principal v2.3"""
    print("🚀 Gestor de Archivos - Limpieza v2.3")
    print("=" * 40)
    print("✨ Características v2.3:")
    print("   - Extensiones configurables")
    print("   - Búsqueda recursiva") 
    print("   - Validación avanzada")
    print("   - Handlers optimizados")
    print()
    
    try:
        app = CleanupByListGUI()
        app.root.protocol("WM_DELETE_WINDOW", app.on_closing)
        
        print("🎯 Aplicación v2.3 iniciada")
        app.root.mainloop()
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--version":
            print("Gestor de Archivos Corporativo")
            print("Limpieza por Lista v2.3")
            print("Julio 2025 - Extensiones Configurables")
            sys.exit(0)
        elif sys.argv[1] == "--help":
            print("Uso: python limpieza_lista_gui_real.py [opciones]")
            print()
            print("Opciones:")
            print("  --version  Mostrar versión")
            print("  --help     Mostrar ayuda")
            print()
            sys.exit(0)
    
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Cerrado por usuario")
        sys.exit(0)
    except Exception as e:
        print(f"💥 Error fatal: {e}")
        sys.exit(1).status_var.set("Licencia activada v2.3")
                else:
                    self.status_var.set("Licencia requerida")
                    
        except Exception as e:
            self.logger.warning(f"Error verificando licencia: {e}")
            self.licensed = True  # Permitir en caso de error
    
    def load_default_config(self):
        """Cargar configuración por defecto"""
        if not self.config:
            return
        
        try:
            # Cargar ubicaciones
            locations = self.config.get_enabled_locations()
            self.init_handlers(locations)
            
            # Cargar extensiones v2.3
            self.load_extension_config()
            self.update_extension_info()
            
            self.logger.info(f"Configuración v2.3 cargada: {len(locations)} ubicaciones")
            
        except Exception as e:
            self.logger.error(f"Error cargando configuración: {e}")
    
    def init_handlers(self, locations):
        """Inicializar handlers v2.3"""
        self.handlers = {}
        
        for location_id, location_config in locations.items():
            try:
                network_path = location_config.get('network_path', '')
                
                if 'qnap' in location_id.lower():
                    # Handler QNAP v2.3
                    self.handlers[location_id] = QnapHandler(location_id, self.config)
                else:
                    # Handler PC v2.3
                    self.handlers[location_id] = PCHandler(location_id, self.config)
                
                self.logger.debug(f"Handler v2.3 creado: {location_id}")
                
            except Exception as e:
                self.logger.error(f"Error creando handler {location_id}: {e}")
    
    def log_to_ui(self, message):
        """Log a UI"""
        try:
            if not hasattr(self, 'logs_text'):
                # Crear área de logs si no existe
                self.logs_text = tk.Text(self.root, height=10, state='disabled')
                self.logs_text.pack(fill='both', expand=True, padx=10, pady=5)
            
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_line = f"[{timestamp}] {message}\n"
            
            self.logs_text.config(state='normal')
            self.logs_text.insert(tk.END, log_line)
            self.logs_text.see(tk.END)
            self.logs_text.config(state='disabled')
            
        except Exception as e:
            print(f"Error logging: {e}")
    
    def on_closing(self):
        """Manejar cierre"""
        if self.running:
            if messagebox.askokcancel("Salir", "¿Detener proceso y salir?"):
                self.running = False
                self.root.destroy()
        else:
            self.root.destroy()

# ===========================================
# FUNCIONES PRINCIPALES
# ===========================================

def main():
    """Función principal v2.3"""
    print("🚀 Gestor de Archivos - Limpieza v2.3")
    print("=" * 40)
    print("✨ Características v2.3:")
    print("   - Extensiones configurables")
    print("   - Búsqueda recursiva") 
    print("   - Validación avanzada")
    print("   - Handlers optimizados")
    print()
    
    try:
        app = CleanupByListGUI()
        app.root.protocol("WM_DELETE_WINDOW", app.on_closing)
        
        print("🎯 Aplicación v2.3 iniciada")
        app.root.mainloop()
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--version":
            print("Gestor de Archivos Corporativo")
            print("Limpieza por Lista v2.3")
            print("Julio 2025 - Extensiones Configurables")
            sys.exit(0)
        elif sys.argv[1] == "--help":
            print("Uso: python limpieza_lista_gui_real.py [opciones]")
            print()
            print("Opciones:")
            print("  --version  Mostrar versión")
            print("  --help     Mostrar ayuda")
            print()
            sys.exit(0)
    
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ Cerrado por usuario")
        sys.exit(0)
    except Exception as e:
        print(f"💥 Error fatal: {e}")
        sys.exit(1)
