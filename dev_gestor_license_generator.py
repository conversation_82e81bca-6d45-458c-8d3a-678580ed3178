"""
Generador de Licencias Gestor de Archivos - SOLO DESARROLLADOR
============================================================

Protegido con contraseña - Solo para generar hashes de licencia
"""

import tkinter as tk
from tkinter import ttk, messagebox
import hashlib
from datetime import datetime, timedelta

class GestorLicenseGenerator:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Gestor License Generator - DEVELOPER ONLY")
        self.root.geometry("650x450")
        self.root.resizable(False, False)
        
        # Secret keys por aplicación
        self.secret_keys = {
            "validacion": "GESTOR_VALIDACION_2025_SEC",
            "limpieza": "GESTOR_LIMPIEZA_2025_SEC"
        }
        
        self.dev_password = "igson123"
        self.authenticated = False
        
        self.setup_auth_ui()
    
    def setup_auth_ui(self):
        """Pantalla de autenticación"""
        auth_frame = ttk.Frame(self.root, padding="20")
        auth_frame.pack(fill='both', expand=True)
        
        ttk.Label(auth_frame, text="GESTOR LICENSE GENERATOR", 
                 font=("Arial", 16, "bold")).pack(pady=10)
        ttk.Label(auth_frame, text="DEVELOPER ACCESS ONLY", 
                 font=("Arial", 12), foreground="red").pack(pady=5)
        
        ttk.Label(auth_frame, text="Password:").pack(pady=10)
        self.password_var = tk.StringVar()
        password_entry = ttk.Entry(auth_frame, textvariable=self.password_var, 
                                 show="*", width=25, font=("Arial", 12))
        password_entry.pack(pady=5)
        password_entry.bind('<Return>', lambda e: self.authenticate())
        
        ttk.Button(auth_frame, text="Access Generator", 
                  command=self.authenticate).pack(pady=15)
        
        password_entry.focus()
    
    def authenticate(self):
        """Verificar contraseña"""
        if self.password_var.get() == self.dev_password:
            self.authenticated = True
            for widget in self.root.winfo_children():
                widget.destroy()
            self.setup_generator_ui()
        else:
            messagebox.showerror("Access Denied", "Invalid developer password")
            self.password_var.set("")
    
    def setup_generator_ui(self):
        """Interfaz del generador"""
        self.root.geometry("750x550")
        
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.pack(fill='both', expand=True)
        
        # Título
        ttk.Label(main_frame, text="Gestor License Hash Generator", 
                 font=("Arial", 14, "bold")).grid(row=0, column=0, columnspan=2, pady=15)
        
        # App selection
        ttk.Label(main_frame, text="Application:").grid(row=1, column=0, sticky='w', pady=8)
        self.app_var = tk.StringVar(value="validacion")
        app_combo = ttk.Combobox(main_frame, textvariable=self.app_var, 
                                values=["validacion", "limpieza"], 
                                state="readonly", width=22)
        app_combo.grid(row=1, column=1, sticky='w', pady=8)
        
        # Hardware ID
        ttk.Label(main_frame, text="Hardware ID:").grid(row=2, column=0, sticky='w', pady=8)
        self.hardware_var = tk.StringVar()
        hardware_entry = ttk.Entry(main_frame, textvariable=self.hardware_var, width=35, font=("Courier", 9))
        hardware_entry.grid(row=2, column=1, pady=8)
        
        # Days valid
        ttk.Label(main_frame, text="Valid Days:").grid(row=3, column=0, sticky='w', pady=8)
        self.days_var = tk.StringVar(value="365")
        days_entry = ttk.Entry(main_frame, textvariable=self.days_var, width=10)
        days_entry.grid(row=3, column=1, sticky='w', pady=8)
        
        # Generate button
        ttk.Button(main_frame, text="Generate License Hash", 
                  command=self.generate_hash).grid(row=4, column=0, columnspan=2, pady=20)
        
        # Results area (hidden initially)
        self.result_frame = ttk.LabelFrame(main_frame, text="Generated License", padding="10")
        
        self.generated_hash = ""
        self.generated_date = ""
    
    def generate_hash(self):
        """Genera hash de licencia"""
        try:
            app_name = self.app_var.get()
            hardware_id = self.hardware_var.get().strip().upper()
            
            if not hardware_id:
                messagebox.showerror("Error", "Hardware ID required")
                return
            
            if len(hardware_id) != 16:
                messagebox.showerror("Error", "Hardware ID should be 16 characters")
                return
            
            days = int(self.days_var.get())
            license_type = "STANDARD"
            
            # Calcular fecha de expiración
            expiry_date = (datetime.now() + timedelta(days=days)).strftime("%Y-%m-%d")
            
            # Obtener secret key
            secret_key = self.secret_keys.get(app_name)
            if not secret_key:
                messagebox.showerror("Error", f"No secret key for app: {app_name}")
                return
            
            # Generar hash
            combined = f"{hardware_id}{expiry_date}{license_type}{secret_key}"
            license_hash = hashlib.sha256(combined.encode()).hexdigest()
            
            self.generated_hash = license_hash
            self.generated_date = expiry_date
            
            # Mostrar resultados
            self.show_results(app_name, hardware_id, license_hash, expiry_date, days)
            
        except ValueError:
            messagebox.showerror("Error", "Invalid number of days")
        except Exception as e:
            messagebox.showerror("Error", f"Error generating hash: {e}")
    
    def show_results(self, app_name, hardware_id, license_hash, expiry_date, days):
        """Muestra resultados en la misma ventana"""
        # Mostrar frame de resultados
        self.result_frame.grid(row=5, column=0, columnspan=2, pady=20, sticky='ew')
        
        # Limpiar contenido anterior
        for widget in self.result_frame.winfo_children():
            widget.destroy()
        
        # Info general
        info_text = f"App: {app_name} | Hardware: {hardware_id} | Valid: {days} days until {expiry_date}"
        ttk.Label(self.result_frame, text=info_text, font=("Arial", 9)).pack(pady=(0,10))
        
        # Hash copiable
        ttk.Label(self.result_frame, text="License Hash:", font=("Arial", 10, "bold")).pack(anchor='w')
        
        hash_frame = tk.Frame(self.result_frame)
        hash_frame.pack(fill='x', pady=5)
        
        hash_var = tk.StringVar(value=license_hash)
        hash_entry = tk.Entry(hash_frame, textvariable=hash_var, font=("Courier", 8), 
                             state='readonly', width=85)
        hash_entry.pack(side='left', fill='x', expand=True, padx=(0,5))
        
        def copy_hash():
            self.root.clipboard_clear()
            self.root.clipboard_append(license_hash)
            copy_hash_btn.config(text="✓ Copied")
            self.root.after(2000, lambda: copy_hash_btn.config(text="Copy Hash"))
        
        copy_hash_btn = tk.Button(hash_frame, text="Copy Hash", command=copy_hash, 
                                 bg='lightblue', width=12)
        copy_hash_btn.pack(side='right')
        
        # Fecha copiable
        ttk.Label(self.result_frame, text="Expiry Date:", font=("Arial", 10, "bold")).pack(anchor='w', pady=(10,0))
        
        date_frame = tk.Frame(self.result_frame)
        date_frame.pack(fill='x', pady=5)
        
        date_var = tk.StringVar(value=expiry_date)
        date_entry = tk.Entry(date_frame, textvariable=date_var, font=("Courier", 10), 
                             state='readonly', width=20)
        date_entry.pack(side='left', padx=(0,5))
        
        def copy_date():
            self.root.clipboard_clear()
            self.root.clipboard_append(expiry_date)
            copy_date_btn.config(text="✓ Copied")
            self.root.after(2000, lambda: copy_date_btn.config(text="Copy Date"))
        
        copy_date_btn = tk.Button(date_frame, text="Copy Date", command=copy_date, 
                                 bg='lightgreen', width=10)
        copy_date_btn.pack(side='left')
        
        # Instrucciones
        instructions = ("Instructions:\n"
                       "1. Send Hardware ID to customer\n"
                       "2. Customer sends back Hardware ID\n"
                       "3. Send License Hash and Expiry Date to customer")
        
        instructions_label = ttk.Label(self.result_frame, text=instructions, font=("Arial", 9), 
                                      foreground="blue", wraplength=500, justify="left")
        instructions_label.pack(pady=(20,0), anchor='w')
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    print("🔐 Gestor License Generator - Developer Tool")
    print("Password: igson123")
    print("=" * 50)
    
    app = GestorLicenseGenerator()
    app.run()
