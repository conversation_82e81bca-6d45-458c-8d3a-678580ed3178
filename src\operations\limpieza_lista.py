"""
Gestor de Archivos Corporativo - Limpieza por Lista
=================================================

Segunda operación completa: Lee lista de archivos desde TXT y los elimina
en múltiples ubicaciones (2 Qnaps + 4 PCs) de forma sincronizada
"""

import os
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Set, Union
import logging
from datetime import datetime

# Imports del sistema
import sys
sys.path.append(str(Path(__file__).parent.parent))
from core.config_manager import config
from core.logger import get_logger, log_operation_start, log_operation_end, log_file_operation
from core.utils import read_text_file, validate_file_list, format_file_size, ProgressTracker
from handlers.qnap_handler import QnapHandler
from handlers.pc_handler import PCHandler

class LimpiezaLista:
    """
    Operación de limpieza masiva basada en lista de archivos
    
    Funcionalidad:
    1. Lee archivo TXT con lista de archivos a eliminar
    2. Conecta a todas las ubicaciones configuradas (Qnaps + PCs)
    3. Busca y elimina archivos encontrados
    4. Genera reporte detallado por ubicación
    """
    
    def __init__(self):
        self.logger = get_logger('limpieza')
        self.operation_name = "Limpieza por Lista"
        
        # Cargar configuración de la operación
        self.config = config.get('operations.limpieza_lista', {})
        
        if not self.config.get('enabled', False):
            raise ValueError("Operación de limpieza por lista está deshabilitada")
        
        # Configuración de operación
        self.ubicaciones_limpieza = self.config.get('ubicaciones_limpieza', [])
        self.carpetas_objetivo = self.config.get('carpetas_objetivo', {})
        
        # Validar configuración
        self._validate_configuration()
        
        # Inicializar handlers por tipo
        self.handlers: Dict[str, Union[QnapHandler, PCHandler]] = {}
        self._initialize_handlers()
        
        # Estadísticas de operación
        self.stats = {
            'archivo_lista': None,
            'total_archivos_lista': 0,
            'archivos_validos': 0,
            'archivos_invalidos': 0,
            'ubicaciones_conectadas': 0,
            'ubicaciones_fallidas': 0,
            'total_archivos_encontrados': 0,
            'total_archivos_eliminados': 0,
            'total_archivos_fallidos': 0,
            'estadisticas_por_ubicacion': {},
            'archivos_no_encontrados': [],
            'errores_generales': [],
            'tiempo_inicio': None,
            'tiempo_fin': None,
            'duracion_total': 0
        }
        
        self.logger.info(f"Inicializada operación: {self.operation_name}")
        self.logger.info(f"Ubicaciones configuradas: {self.ubicaciones_limpieza}")
        self.logger.info(f"Carpetas objetivo: {len(self.carpetas_objetivo)}")
    
    def _validate_configuration(self):
        """Valida la configuración de la operación"""
        errors = []
        
        if not self.ubicaciones_limpieza:
            errors.append("ubicaciones_limpieza está vacío")
        
        if not self.carpetas_objetivo:
            errors.append("carpetas_objetivo está vacío")
        
        # Verificar que las ubicaciones están habilitadas
        locations = config.get_enabled_locations()
        for ubicacion_id in self.ubicaciones_limpieza:
            if ubicacion_id not in locations:
                errors.append(f"Ubicación {ubicacion_id} no está habilitada o no existe")
        
        # Verificar que cada ubicación tiene carpeta objetivo
        for ubicacion_id in self.ubicaciones_limpieza:
            if ubicacion_id not in self.carpetas_objetivo:
                errors.append(f"Ubicación {ubicacion_id} no tiene carpeta_objetivo configurada")
        
        if errors:
            error_msg = "Errores de configuración: " + "; ".join(errors)
            self.logger.error(error_msg)
            raise ValueError(error_msg)
    
    def _initialize_handlers(self):
        """Inicializa handlers apropiados para cada ubicación"""
        for ubicacion_id in self.ubicaciones_limpieza:
            try:
                # Determinar tipo de handler basado en el ID
                if 'qnap' in ubicacion_id.lower():
                    handler = QnapHandler(ubicacion_id)
                elif 'pc' in ubicacion_id.lower():
                    handler = PCHandler(ubicacion_id)
                else:
                    # Por defecto, usar PC handler
                    handler = PCHandler(ubicacion_id)
                    self.logger.warning(f"Tipo de ubicación no reconocido para {ubicacion_id}, usando PC handler")
                
                self.handlers[ubicacion_id] = handler
                
                # Inicializar estadísticas por ubicación
                self.stats['estadisticas_por_ubicacion'][ubicacion_id] = {
                    'tipo_handler': handler.device_type,
                    'conectado': False,
                    'archivos_encontrados': 0,
                    'archivos_eliminados': 0,
                    'archivos_fallidos': 0,
                    'errores': [],
                    'tiempo_conexion': 0,
                    'tiempo_procesamiento': 0
                }
                
                self.logger.info(f"Handler inicializado: {ubicacion_id} → {handler.device_type}")
                
            except Exception as e:
                error_msg = f"Error inicializando handler para {ubicacion_id}: {e}"
                self.logger.error(error_msg)
                self.stats['errores_generales'].append(error_msg)
    
    def cargar_lista_archivos(self, archivo_lista: Union[str, Path]) -> List[str]:
        """
        Carga y valida lista de archivos desde archivo TXT
        
        Args:
            archivo_lista: Ruta al archivo con la lista
        
        Returns:
            List[str]: Lista de archivos válidos
        """
        archivo_path = Path(archivo_lista) if isinstance(archivo_lista, str) else archivo_lista
        self.stats['archivo_lista'] = str(archivo_path)
        
        self.logger.info(f"Cargando lista de archivos desde: {archivo_path}")
        
        if not archivo_path.exists():
            raise FileNotFoundError(f"Archivo de lista no encontrado: {archivo_path}")
        
        # Leer archivo
        lineas = read_text_file(archivo_path)
        if lineas is None:
            raise ValueError(f"Error leyendo archivo de lista: {archivo_path}")
        
        self.stats['total_archivos_lista'] = len(lineas)
        self.logger.info(f"Líneas leídas del archivo: {len(lineas)}")
        
        # Validar lista de archivos
        validacion = validate_file_list(lineas)
        
        self.stats['archivos_validos'] = len(validacion['valid'])
        self.stats['archivos_invalidos'] = len(validacion['invalid_names'])
        
        # Log resultados de validación
        self.logger.info(f"Archivos válidos: {self.stats['archivos_validos']}")
        self.logger.info(f"Archivos inválidos: {self.stats['archivos_invalidos']}")
        
        if validacion['invalid_names']:
            self.logger.warning("Archivos con nombres inválidos:")
            for archivo_invalido in validacion['invalid_names'][:10]:  # Primeros 10
                self.logger.warning(f"  - {archivo_invalido}")
        
        # Log algunos ejemplos de archivos válidos
        if validacion['valid']:
            self.logger.info("Ejemplos de archivos a procesar:")
            for archivo in validacion['valid'][:5]:  # Primeros 5
                self.logger.info(f"  - {archivo}")
        
        return validacion['valid']
    
    def conectar_ubicaciones(self) -> Dict[str, Tuple[bool, str]]:
        """
        Conecta a todas las ubicaciones configuradas
        
        Returns:
            Dict[str, Tuple[bool, str]]: Resultado de conexión por ubicación
        """
        conexiones = {}
        
        self.logger.info("=== CONECTANDO A UBICACIONES ===")
        
        for ubicacion_id, handler in self.handlers.items():
            try:
                self.logger.info(f"Conectando a {ubicacion_id} ({handler.device_type})...")
                
                start_time = time.time()
                success, message = handler.connect()
                connection_time = time.time() - start_time
                
                conexiones[ubicacion_id] = (success, message)
                
                # Actualizar estadísticas
                ubicacion_stats = self.stats['estadisticas_por_ubicacion'][ubicacion_id]
                ubicacion_stats['conectado'] = success
                ubicacion_stats['tiempo_conexion'] = connection_time
                
                if success:
                    self.stats['ubicaciones_conectadas'] += 1
                    self.logger.info(f"✓ Conectado a {ubicacion_id}: {message} ({connection_time:.2f}s)")
                else:
                    self.stats['ubicaciones_fallidas'] += 1
                    ubicacion_stats['errores'].append(f"Conexión fallida: {message}")
                    self.logger.warning(f"✗ Falló conexión a {ubicacion_id}: {message}")
                    
            except Exception as e:
                error_msg = f"Excepción conectando a {ubicacion_id}: {e}"
                self.logger.error(error_msg)
                conexiones[ubicacion_id] = (False, error_msg)
                self.stats['ubicaciones_fallidas'] += 1
                self.stats['estadisticas_por_ubicacion'][ubicacion_id]['errores'].append(error_msg)
        
        # Resumen de conexiones
        self.logger.info(f"Conexiones completadas: {self.stats['ubicaciones_conectadas']}/{len(self.handlers)}")
        
        if self.stats['ubicaciones_conectadas'] == 0:
            raise Exception("No se pudo conectar a ninguna ubicación")
        
        return conexiones
    
    def desconectar_ubicaciones(self) -> Dict[str, Tuple[bool, str]]:
        """Desconecta todas las ubicaciones"""
        desconexiones = {}
        
        self.logger.info("=== DESCONECTANDO UBICACIONES ===")
        
        for ubicacion_id, handler in self.handlers.items():
            try:
                success, message = handler.disconnect()
                desconexiones[ubicacion_id] = (success, message)
                
                if success:
                    self.logger.info(f"✓ Desconectado de {ubicacion_id}")
                else:
                    self.logger.warning(f"✗ Error desconectando de {ubicacion_id}: {message}")
                    
            except Exception as e:
                error_msg = f"Excepción desconectando de {ubicacion_id}: {e}"
                self.logger.error(error_msg)
                desconexiones[ubicacion_id] = (False, error_msg)
        
        return desconexiones
    
    def procesar_ubicacion(self, ubicacion_id: str, lista_archivos: List[str]) -> Dict:
        """
        Procesa una ubicación específica: busca y elimina archivos de la lista
        
        Args:
            ubicacion_id: ID de la ubicación
            lista_archivos: Lista de archivos a eliminar
        
        Returns:
            Dict: Estadísticas del procesamiento
        """
        handler = self.handlers[ubicacion_id]
        ubicacion_stats = self.stats['estadisticas_por_ubicacion'][ubicacion_id]
        carpeta_objetivo = self.carpetas_objetivo.get(ubicacion_id, "")
        
        self.logger.info(f"Procesando ubicación: {ubicacion_id}")
        self.logger.info(f"  Handler: {handler.device_type}")
        self.logger.info(f"  Carpeta objetivo: {carpeta_objetivo}")
        self.logger.info(f"  Archivos a procesar: {len(lista_archivos)}")
        
        if not ubicacion_stats['conectado']:
            error_msg = f"Ubicación {ubicacion_id} no está conectada"
            self.logger.error(error_msg)
            ubicacion_stats['errores'].append(error_msg)
            return ubicacion_stats
        
        start_time = time.time()
        
        try:
            # Usar operación masiva del handler apropiado
            if isinstance(handler, QnapHandler):
                resultado = handler.bulk_delete_files(lista_archivos, carpeta_objetivo)
            elif isinstance(handler, PCHandler):
                resultado = handler.bulk_delete_files(lista_archivos, carpeta_objetivo, recycle_bin=True)
            else:
                raise ValueError(f"Tipo de handler no soportado: {type(handler)}")
            
            # Actualizar estadísticas
            ubicacion_stats['archivos_encontrados'] = resultado['found_files']
            ubicacion_stats['archivos_eliminados'] = resultado['deleted_files']
            ubicacion_stats['archivos_fallidos'] = resultado['failed_files']
            ubicacion_stats['errores'].extend(resultado['errors'])
            
            # Actualizar totales globales
            self.stats['total_archivos_encontrados'] += resultado['found_files']
            self.stats['total_archivos_eliminados'] += resultado['deleted_files']
            self.stats['total_archivos_fallidos'] += resultado['failed_files']
            
            # Archivos no encontrados (solo para el primer procesamiento)
            if ubicacion_id == self.ubicaciones_limpieza[0]:  # Primera ubicación
                self.stats['archivos_no_encontrados'].extend(resultado['not_found_list'])
            
            # Log resumen por ubicación
            self.logger.info(f"Resultado para {ubicacion_id}:")
            self.logger.info(f"  📄 Encontrados: {resultado['found_files']}")
            self.logger.info(f"  🗑️  Eliminados: {resultado['deleted_files']}")
            self.logger.info(f"  ❌ Fallidos: {resultado['failed_files']}")
            self.logger.info(f"  🔍 No encontrados: {len(resultado['not_found_list'])}")
            
            # Log algunos archivos eliminados
            if resultado['deleted_list']:
                self.logger.info(f"  Ejemplos eliminados:")
                for archivo in resultado['deleted_list'][:3]:
                    self.logger.info(f"    ✓ {archivo}")
            
            # Log errores si los hay
            if resultado['errors']:
                self.logger.warning(f"  Errores en {ubicacion_id}:")
                for error in resultado['errors'][:3]:
                    self.logger.warning(f"    ✗ {error}")
            
        except Exception as e:
            error_msg = f"Error procesando {ubicacion_id}: {e}"
            self.logger.error(error_msg)
            ubicacion_stats['errores'].append(error_msg)
            self.stats['errores_generales'].append(error_msg)
        
        finally:
            ubicacion_stats['tiempo_procesamiento'] = time.time() - start_time
        
        return ubicacion_stats
    
    def ejecutar(self, archivo_lista: Union[str, Path], dry_run: bool = False) -> Dict:
        """
        Ejecuta la operación completa de limpieza por lista
        
        Args:
            archivo_lista: Ruta al archivo con la lista de archivos
            dry_run: Si True, solo simula sin eliminar archivos
        
        Returns:
            Dict: Estadísticas completas de la operación
        """
        self.stats['tiempo_inicio'] = time.time()
        
        # Log inicio de operación
        operation_details = f"Lista: {archivo_lista}, Ubicaciones: {len(self.ubicaciones_limpieza)}"
        if dry_run:
            operation_details += " (DRY RUN)"
        
        log_operation_start('limpieza', self.operation_name, operation_details)
        
        try:
            # 1. Cargar lista de archivos
            self.logger.info("=== FASE 1: CARGA DE LISTA ===")
            lista_archivos = self.cargar_lista_archivos(archivo_lista)
            
            if not lista_archivos:
                self.logger.warning("No hay archivos válidos para procesar")
                return self.stats
            
            # 2. Conectar a ubicaciones
            self.logger.info("=== FASE 2: CONEXIÓN A UBICACIONES ===")
            conexiones = self.conectar_ubicaciones()
            
            # 3. Procesar cada ubicación
            self.logger.info("=== FASE 3: PROCESAMIENTO POR UBICACIÓN ===")
            
            # Progress tracker para todas las ubicaciones
            ubicaciones_conectadas = [uid for uid, (connected, _) in conexiones.items() if connected]
            progress = ProgressTracker(len(ubicaciones_conectadas), "Procesando ubicaciones")
            
            for ubicacion_id in ubicaciones_conectadas:
                if dry_run:
                    self.logger.info(f"DRY RUN: Procesaría {ubicacion_id} con {len(lista_archivos)} archivos")
                    # Simular estadísticas
                    self.stats['estadisticas_por_ubicacion'][ubicacion_id].update({
                        'archivos_encontrados': 0,
                        'archivos_eliminados': 0,
                        'archivos_fallidos': 0
                    })
                else:
                    self.procesar_ubicacion(ubicacion_id, lista_archivos)
                
                progress.update()
            
            # 4. Resumen de resultados
            self.logger.info("=== FASE 4: RESUMEN DE RESULTADOS ===")
            self._log_summary()
            
        except Exception as e:
            error_msg = f"Error en ejecución de operación: {e}"
            self.logger.error(error_msg)
            self.stats['errores_generales'].append(error_msg)
        
        finally:
            # 5. Desconectar ubicaciones
            self.logger.info("=== FASE 5: DESCONEXIÓN ===")
            self.desconectar_ubicaciones()
            
            # Finalizar estadísticas
            self.stats['tiempo_fin'] = time.time()
            self.stats['duracion_total'] = self.stats['tiempo_fin'] - self.stats['tiempo_inicio']
            
            # Log final de operación
            operation_success = (
                len(self.stats['errores_generales']) == 0 and
                self.stats['total_archivos_eliminados'] > 0
            )
            log_operation_end('limpieza', self.operation_name, operation_success, self.stats, self.stats['duracion_total'])
        
        return self.stats
    
    def _log_summary(self):
        """Log resumen detallado de la operación"""
        self.logger.info("📊 ESTADÍSTICAS DE LIMPIEZA:")
        self.logger.info(f"  📄 Total archivos en lista: {self.stats['total_archivos_lista']}")
        self.logger.info(f"  ✅ Archivos válidos: {self.stats['archivos_validos']}")
        self.logger.info(f"  ❌ Archivos inválidos: {self.stats['archivos_invalidos']}")
        self.logger.info(f"  🔗 Ubicaciones conectadas: {self.stats['ubicaciones_conectadas']}")
        self.logger.info(f"  💥 Ubicaciones fallidas: {self.stats['ubicaciones_fallidas']}")
        self.logger.info(f"  🔍 Total archivos encontrados: {self.stats['total_archivos_encontrados']}")
        self.logger.info(f"  🗑️  Total archivos eliminados: {self.stats['total_archivos_eliminados']}")
        self.logger.info(f"  ⚠️  Total archivos fallidos: {self.stats['total_archivos_fallidos']}")
        
        # Estadísticas por ubicación
        self.logger.info("  📍 Estadísticas por ubicación:")
        for ubicacion_id, stats in self.stats['estadisticas_por_ubicacion'].items():
            if stats['conectado']:
                self.logger.info(f"    {ubicacion_id} ({stats['tipo_handler']}):")
                self.logger.info(f"      Encontrados: {stats['archivos_encontrados']}")
                self.logger.info(f"      Eliminados: {stats['archivos_eliminados']}")
                self.logger.info(f"      Fallidos: {stats['archivos_fallidos']}")
                self.logger.info(f"      Tiempo: {stats['tiempo_procesamiento']:.2f}s")
        
        # Archivos no encontrados en ninguna ubicación
        if self.stats['archivos_no_encontrados']:
            count = len(self.stats['archivos_no_encontrados'])
            self.logger.info(f"  🔍 Archivos no encontrados en ninguna ubicación: {count}")
            for archivo in self.stats['archivos_no_encontrados'][:5]:
                self.logger.info(f"    - {archivo}")
        
        # Errores generales
        if self.stats['errores_generales']:
            self.logger.info(f"  🚨 Errores generales ({len(self.stats['errores_generales'])}):")
            for error in self.stats['errores_generales'][:3]:
                self.logger.info(f"    - {error}")
    
    def get_stats(self) -> Dict:
        """Obtiene estadísticas actuales de la operación"""
        return self.stats.copy()
    
    def generar_reporte_detallado(self) -> str:
        """Genera reporte detallado en formato texto"""
        if not self.stats['tiempo_fin']:
            return "Operación no completada - no hay estadísticas disponibles"
        
        reporte = []
        reporte.append("=" * 60)
        reporte.append("REPORTE DE LIMPIEZA POR LISTA")
        reporte.append("=" * 60)
        reporte.append(f"Fecha: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        reporte.append(f"Duración total: {self.stats['duracion_total']:.2f} segundos")
        reporte.append(f"Archivo de lista: {self.stats['archivo_lista']}")
        reporte.append("")
        
        # Resumen general
        reporte.append("RESUMEN GENERAL:")
        reporte.append(f"  Archivos en lista: {self.stats['total_archivos_lista']}")
        reporte.append(f"  Archivos válidos: {self.stats['archivos_validos']}")
        reporte.append(f"  Ubicaciones procesadas: {self.stats['ubicaciones_conectadas']}")
        reporte.append(f"  Total eliminados: {self.stats['total_archivos_eliminados']}")
        reporte.append(f"  Total fallidos: {self.stats['total_archivos_fallidos']}")
        reporte.append("")
        
        # Detalle por ubicación
        reporte.append("DETALLE POR UBICACIÓN:")
        for ubicacion_id, stats in self.stats['estadisticas_por_ubicacion'].items():
            reporte.append(f"  {ubicacion_id} ({stats['tipo_handler']}):")
            if stats['conectado']:
                reporte.append(f"    Estado: CONECTADO")
                reporte.append(f"    Encontrados: {stats['archivos_encontrados']}")
                reporte.append(f"    Eliminados: {stats['archivos_eliminados']}")
                reporte.append(f"    Fallidos: {stats['archivos_fallidos']}")
                reporte.append(f"    Tiempo procesamiento: {stats['tiempo_procesamiento']:.2f}s")
            else:
                reporte.append(f"    Estado: NO CONECTADO")
            
            if stats['errores']:
                reporte.append(f"    Errores: {len(stats['errores'])}")
            reporte.append("")
        
        return "\n".join(reporte)

if __name__ == "__main__":
    # Test básico de la operación
    print("📋 Limpieza por Lista - Test")
    print("=" * 35)
    
    # Configurar logging para test
    logging.basicConfig(level=logging.INFO)
    
    try:
        # Crear operación
        limpiador = LimpiezaLista()
        print(f"✓ Operación inicializada")
        print(f"  Ubicaciones: {limpiador.ubicaciones_limpieza}")
        print(f"  Handlers: {len(limpiador.handlers)}")
        
        # Crear archivo de lista temporal para test
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            test_files = ["archivo1.txt", "imagen.jpg", "documento.pdf", "video.mp4"]
            for archivo in test_files:
                f.write(f"{archivo}\n")
            temp_file = f.name
        
        try:
            print(f"\n🧪 Ejecutando en modo DRY RUN...")
            stats = limpiador.ejecutar(temp_file, dry_run=True)
            
            print(f"\n📊 Estadísticas:")
            print(f"  Archivos válidos: {stats['archivos_validos']}")
            print(f"  Ubicaciones conectadas: {stats['ubicaciones_conectadas']}")
            print(f"  Duración: {stats['duracion_total']:.2f}s")
            
            # Mostrar reporte
            print(f"\n📄 Reporte detallado:")
            reporte = limpiador.generar_reporte_detallado()
            print(reporte[:500] + "..." if len(reporte) > 500 else reporte)
            
        finally:
            # Limpiar archivo temporal
            Path(temp_file).unlink(missing_ok=True)
        
        print("✅ Test completado")
        
    except Exception as e:
        print(f"❌ Error en test: {e}")
        import traceback
        traceback.print_exc()
