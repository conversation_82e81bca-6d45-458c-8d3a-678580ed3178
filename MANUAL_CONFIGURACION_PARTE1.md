# Manual de Configuración de Conexiones - PARTE 1
## Gestor de Archivos Corporativo

**Versión:** 2.0  
**Fecha:** Junio 2025  
**Aplicaciones:** Validación de Duplicados & Limpieza por Lista  

---

## 🎯 Introducción

Este manual explica cómo configurar conexiones a dispositivos NAS (QNAP) y PCs de workstation. El sistema utiliza un **método híbrido** con failover automático entre conexiones simbólicas y rutas de red.

### Aplicaciones Incluidas
- **IGSON_Validacion_Duplicados.exe** - Valida archivos duplicados entre origen y QNAPs
- **LimpiezaPorLista.exe** - Elimina archivos específicos de múltiples ubicaciones

---

## 📁 Estructura de Configuración

### Archivos Principales
```
├── config.json              # Configuración principal
├── encryption.key           # Clave de encriptación (auto-generada)
├── gestor_validacion_license.lic  # Licencia Validación
├── gestor_limpieza_license.lic    # Licencia Limpieza
└── logs/                    # Logs por operación y fecha
    ├── validacion_YYYYMMDD.log
    └── limpieza_YYYYMMDD.log
```

### Tipos de Conexión Soportados
- **Symlinks (Enlaces Simbólicos)**: Rutas locales mapeadas (ej: `C:\Links\qnap1`)
- **Network Paths (Rutas de Red)**: Rutas UNC directas (ej: `\\qnap1\share`)
- **Híbrido**: Failover automático entre ambos métodos

---

## 🐧 Configuración de Servidores Samba (Linux/Ubuntu)

### Archivo smb.conf de Ejemplo

```ini
# /etc/samba/smb.conf

[global]
workgroup = WORKGROUP
server string = Ubuntu File Server
netbios name = ubuntu-srv01
security = user
map to guest = bad user
encrypt passwords = yes
smb ports = 445

[shared]
comment = Shared Documents
path = /srv/samba/shared
browseable = yes
read only = no
guest ok = no
valid users = operador, admin
create mask = 0664
directory mask = 0775

[production]
comment = Production Files
path = /var/data/production
browseable = yes
read only = no
valid users = @production-group
create mask = 0660
directory mask = 0770

[backup]
comment = Backup Storage
path = /backup/files
browseable = no
read only = no
valid users = backup-user
admin users = admin
create mask = 0600
directory mask = 0700

[public]
comment = Public Share
path = /srv/samba/public
browseable = yes
read only = yes
guest ok = yes
```

### Configuración en el GUI según smb.conf

#### ✅ Campo Share: Nombre entre corchetes [nombre]

**Regla fundamental:** En el campo **Share** del GUI pones exactamente el nombre que está entre corchetes `[nombre]` en smb.conf.

#### Ejemplo 1: Share [shared] con autenticación
**Archivo samba:**
```ini
[shared]               # ← Este nombre va en el campo Share
path = /srv/samba/shared
valid users = operador, admin
```

**Configuración GUI:**
```
Hostname/IP: ************
Share: shared          # ← Exactamente el nombre del [shared]
Usuario: operador
Contraseña: [password]
Dominio: WORKGROUP
Subcarpeta: [vacío]
```

#### Ejemplo 2: Share [production] con subcarpeta
**Archivo samba:**
```ini
[production]           # ← Este nombre va en el campo Share
path = /var/data/production
valid users = @production-group
```

**Configuración GUI:**
```
Hostname/IP: ubuntu-srv01
Share: production      # ← Exactamente el nombre del [production]
Usuario: prod-user
Contraseña: [password]
Dominio: WORKGROUP
Subcarpeta: reports\monthly
```

#### Ejemplo 3: Share [backup] sin browseable
**Archivo samba:**
```ini
[backup]               # ← Este nombre va en el campo Share
path = /backup/files
browseable = no
valid users = backup-user
```

**Configuración GUI:**
```
Hostname/IP: ************
Share: backup          # ← Exactamente el nombre del [backup]
Usuario: backup-user
Contraseña: [password]
Dominio: [vacío]
Subcarpeta: [vacío]
```

### Reglas de Campos en el GUI

#### Campo Hostname/IP
- **SIN barras**: `************` ✅
- **SIN barras**: `ubuntu-srv01` ✅  
- **CON barras**: `\\************\\` ❌

#### Campo Share
- **Nombre del share**: `shared` ✅
- **Solo el nombre**: `production` ✅
- **SIN barras**: `backup` ✅
- **CON barras**: `\\shared\\` ❌

#### Campo Subcarpeta
- **Ruta relativa**: `reports\monthly` ✅
- **Con barra inicial**: `\temp\files` ✅
- **Sin barras**: `subfolder` ✅
- **Vacío**: `[vacío]` ✅ (raíz del share)

### Ejemplos Combinados Samba ↔ GUI

#### Servidor Ubuntu con múltiples shares
```ini
# smb.conf
[datos]                # ← Share name
path = /srv/data
valid users = operador

[archivos]             # ← Share name
path = /srv/files
valid users = admin, user1
```

**Configuración GUI para PC1:**
```
Hostname/IP: ubuntu-srv01
Share: datos           # ← Nombre del [datos]
Subcarpeta: produccion
```

**Configuración GUI para PC2:**
```
Hostname/IP: ubuntu-srv01
Share: archivos        # ← Nombre del [archivos]
Subcarpeta: trabajo\activos
```

### Matriz de Configuraciones Samba ↔ GUI

| smb.conf | GUI Hostname/IP | GUI Share | GUI Subcarpeta | Resultado Final |
|----------|----------------|-----------|----------------|-----------------|
| `[datos]` en `*************` | `*************` | `datos` | `[vacío]` | `\\*************\datos` |
| `[shared]` en `file-server` | `file-server` | `shared` | `temp` | `\\file-server\shared\temp` |
| `[backup]` en `nas.local` | `nas.local` | `backup` | `daily\logs` | `\\nas.local\backup\daily\logs` |
| `[public]` en `*********` | `*********` | `public` | `downloads` | `\\*********\public\downloads` |

### Casos Especiales

#### Share Oculto (termina en $)
**Samba config:**
```ini
[hidden$]              # ← Share name con $
path = /srv/hidden
browseable = no
```

**GUI config:**
```
Share: hidden$         # ← Incluye el $ en el nombre
```

#### Shares con espacios
**Samba config:**
```ini
[Company Files]        # ← Share name con espacios
path = /srv/company_files
```

**GUI config:**
```
Share: Company Files   # ← Respeta los espacios
```

#### Subcarpetas con espacios
**GUI config:**
```
Subcarpeta: Monthly Reports\2025\January
```

---

## ⚙️ Configuración Básica config.json

### Estructura del config.json

```json
{
  "app_info": {
    "name": "Gestor de Archivos Corporativo",
    "version": "1.0",
    "dev_mode": false
  },
  "connection_settings": {
    "connection_type": "hybrid",
    "timeout_seconds": 30,
    "retry_attempts": 3
  },
  "locations": {
    "qnap1": {
      "name": "QNAP Storage 1",
      "network_path": "\\\\qnap1\\share",
      "symlink_path": "C:\\Links\\qnap1",
      "enabled": true,
      "connection_priority": ["symlink", "network"],
      "credentials": {
        "username": "admin",
        "password_encrypted": "gAAAAABh...",
        "domain": "WORKGROUP"
      }
    }
  }
}
```

### Parámetros de Conexión

| Parámetro | Descripción | Valores |
|-----------|-------------|---------|
| `connection_type` | Método de conexión | `"symlink"`, `"network"`, `"hybrid"` |
| `timeout_seconds` | Timeout por conexión | `15-60` segundos |
| `retry_attempts` | Intentos de reconexión | `1-5` intentos |
| `connection_priority` | Orden de fallback | `["symlink", "network"]` o `["network", "symlink"]` |

---

## 🖥️ Configuración de Dispositivos QNAP

### Configuración Estándar

```json
"qnap1": {
  "name": "QNAP Storage Principal",
  "network_path": "\\\\*************\\share",
  "symlink_path": "Q:\\",
  "enabled": true,
  "connection_priority": ["symlink", "network"],
  "credentials": {
    "username": "admin",
    "password_encrypted": "",
    "domain": "WORKGROUP"
  }
}
```

### Tipos de Configuración QNAP

#### 1. **Por IP Fija**
```json
"network_path": "\\\\*************\\PublicShare"
```

#### 2. **Por Nombre de Host**
```json
"network_path": "\\\\qnap-storage01\\datos"
```

#### 3. **Con Dominio Corporativo**
```json
"credentials": {
  "username": "administrador",
  "password_encrypted": "encrypted_password",
  "domain": "EMPRESA.LOCAL"
}
```

#### 4. **Acceso Público/Guest**
```json
"credentials": {
  "username": "",
  "password_encrypted": "",
  "domain": "WORKGROUP"
}
```

### Configuración de Enlaces Simbólicos

#### Crear Symlinks para QNAP

**Método 1: Script PowerShell (Recomendado)**
```powershell
# Ejecutar como Administrador
New-Item -ItemType SymbolicLink -Path "C:\Links\qnap1" -Target "\\*************\share"
New-Item -ItemType SymbolicLink -Path "C:\Links\qnap2" -Target "\\*************\backup"
```

**Método 2: CMD (Alternativo)**
```cmd
mklink /D "C:\Links\qnap1" "\\*************\share"
mklink /D "C:\Links\qnap2" "\\*************\backup"
```

**Método 3: Mapeo de Unidades de Red**
```json
"symlink_path": "Q:\\"  // Unidad mapeada manualmente
```

---

## 💻 Configuración de PCs de Workstation

### Configuración Windows Server/PC con SMB

#### Compartir Carpeta Windows
```cmd
# Crear share básico
net share datos=C:\SharedData /grant:Everyone,FULL

# Share con usuarios específicos  
net share produccion=D:\Production /grant:"DOMAIN\operador",FULL

# Share oculto (termina en $)
net share backup$=E:\Backup /grant:"Administradores",FULL
```

#### Configuración GUI desde Windows Share
**Share Windows:**
```cmd
net share archivos=C:\CompanyFiles /grant:"operador",CHANGE
```

**Configuración GUI:**
```
Hostname/IP: PC-SERVIDOR01
Share: archivos        # ← Nombre del share creado
Usuario: operador
Contraseña: [password]
Dominio: WORKGROUP
Subcarpeta: documentos\2025
```

### Configuraciones por Escenario

#### 1. **PC con Dominio Corporativo**
```json
"pc1": {
  "network_path": "\\\\pc-workstation-01\\shared",
  "credentials": {
    "username": "admin.sistemas",
    "password_encrypted": "gAAAAABh_encrypted_password",
    "domain": "EMPRESA.LOCAL"
  }
}
```

#### 2. **PC con Workgroup Local**
```json
"pc2": {
  "network_path": "\\\\************\\shared",
  "credentials": {
    "username": "administrador",
    "password_encrypted": "gAAAAABh_encrypted_password",
    "domain": "WORKGROUP"
  }
}
```

#### 3. **PC con Usuario Local**
```json
"pc3": {
  "network_path": "\\\\PC-PRODUCCION\\work",
  "credentials": {
    "username": "PC-PRODUCCION\\operador",
    "password_encrypted": "gAAAAABh_encrypted_password",
    "domain": ""
  }
}
```

---

*CONTINÚA EN PARTE 2...*