# PLAN DE TESTING COMPLETO
## Gestor de Archivos Corporativo

**Fecha:** Junio 2025  
**Proyecto:** Gestor de Archivos Corporativo  
**Versión:** 2.0  
**Entorno:** Raspberry Pi + Windows Workstation  

---

## 🎯 OBJETIVOS DEL TESTING

### Objetivos Principales
- **Validar funcionalidad completa** de ambas aplicaciones
- **Verificar conectividad real** con 6 ubicaciones SMB
- **Probar manejo de errores** y casos edge
- **Confirmar rendimiento** en entorno de producción
- **Validar sistema de licencias** y logging

### Métricas de Éxito
- **100% precisión** en detección de duplicados
- **95% completitud** en eliminación por lista
- **<3 seg/archivo** tiempo de procesamiento
- **0 pérdida de datos** en modo real
- **100% funciones críticas** operativas

---

## 🏗️ CONFIGURACIÓN DEL ENTORNO

### Raspberry Pi - Servidor SMB
```bash
# Configuración de usuarios
qnap1:qnap1pass123@/home/<USER>/shared_files/
qnap2:qnap2pass456@/home/<USER>/shared_files/
pc1:pc1user789@/home/<USER>/shared_files/
pc2:pc2user012@/home/<USER>/shared_files/
pc3:pc3user345@/home/<USER>/shared_files/
pc4:pc4user678@/home/<USER>/shared_files/
```

### Configuración Samba
```ini
[qnap1_share]
path = /home/<USER>/shared_files
valid users = qnap1
read only = no
browsable = yes

[qnap2_share]
path = /home/<USER>/shared_files
valid users = qnap2
read only = no
browsable = yes

[pc1_share]
path = /home/<USER>/shared_files
valid users = pc1
read only = no
browsable = yes

[pc2_share]
path = /home/<USER>/shared_files
valid users = pc2
read only = no
browsable = yes

[pc3_share]
path = /home/<USER>/shared_files
valid users = pc3
read only = no
browsable = yes

[pc4_share]
path = /home/<USER>/shared_files
valid users = pc4
read only = no
browsable = yes
```

### Estructura de Red
```
Windows Workstation (*************)
├── Origen: C:\Testing\archivo_origen\
├── Rechazados: C:\Testing\archivo_rechazados\
└── Conectado a Raspberry Pi (************):
    ├── \\************\qnap1_share\
    ├── \\************\qnap2_share\
    ├── \\************\pc1_share\
    ├── \\************\pc2_share\
    ├── \\************\pc3_share\
    └── \\************\pc4_share\
```

---

## 📊 GENERACIÓN DE DATOS DE PRUEBA

### Script Automatizado
```python
# generate_test_data.py
import os
import random
from datetime import datetime

def create_test_files():
    """Genera archivos DUMMY para testing"""
    
    # Tipos de archivos y tamaños
    file_types = {
        '.pdf': ['documento', 'reporte', 'manual'],
        '.jpg': ['imagen', 'foto', 'screenshot'],
        '.mp4': ['video', 'grabacion', 'presentacion'],
        '.docx': ['archivo', 'texto', 'borrador'],
        '.xlsx': ['hoja', 'calculo', 'datos'],
        '.txt': ['nota', 'log', 'info'],
        '.zip': ['backup', 'comprimido', 'paquete']
    }
    
    # Distribución por ubicación
    distributions = {
        'origen': 25,
        'qnap1': 20,
        'qnap2': 15,
        'pc1': 12,
        'pc2': 10,
        'pc3': 8,
        'pc4': 6
    }
    
    files_data = {}
    
    for location, count in distributions.items():
        files_data[location] = []
        
        for i in range(count):
            ext = random.choice(list(file_types.keys()))
            prefix = random.choice(file_types[ext])
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            filename = f"{prefix}_{i+1}_{timestamp}{ext}"
            content_size = random.randint(1024, 51200)  # 1KB - 50KB
            
            files_data[location].append({
                'name': filename,
                'size': content_size,
                'content': f"Contenido generado para {filename}\n" * (content_size // 50)
            })
    
    return files_data

# Generar archivos de test específicos para casos
def generate_specific_test_cases():
    return {
        'duplicados_qnaps': [
            'documento_importante.pdf',
            'imagen_corporativa.jpg',
            'video_capacitacion.mp4'
        ],
        'duplicados_pcs': [
            'reporte_mensual.docx',
            'datos_ventas.xlsx',
            'backup_config.zip'
        ],
        'duplicados_mixtos': [
            'manual_usuario.pdf',
            'presentacion_anual.pptx'
        ],
        'archivos_unicos': [
            'unique_doc1.txt',
            'unique_img1.png',
            'unique_vid1.avi'
        ]
    }
```

### Matriz de Distribución de Archivos

| Archivo | Origen | QNAP1 | QNAP2 | PC1 | PC2 | PC3 | PC4 | Caso |
|---------|--------|-------|-------|-----|-----|-----|-----|------|
| documento_importante.pdf | ✅ | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ | Dup QNAP |
| reporte_mensual.docx | ✅ | ❌ | ❌ | ✅ | ❌ | ✅ | ❌ | Dup PC |
| manual_usuario.pdf | ✅ | ✅ | ❌ | ❌ | ✅ | ❌ | ❌ | Dup Mixto |
| unique_doc1.txt | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | Único |
| backup_total.zip | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | Todas |

---

## 🔍 CASOS DE PRUEBA - VALIDACIÓN DE DUPLICADOS

### **Caso VD-001: Duplicados en QNAPs Únicamente**
**Objetivo:** Verificar detección cuando archivo existe solo en QNAPs  
**Precondición:** Archivos en origen + duplicados en qnap1/qnap2  
**Datos de entrada:**
- `documento_importante.pdf` → qnap1, qnap2
- `imagen_corporativa.jpg` → qnap1
- `video_capacitacion.mp4` → qnap2

**Pasos:**
1. Ejecutar validación con dry_run=True
2. Verificar detección de 3 duplicados
3. Ejecutar validación con dry_run=False
4. Confirmar movimiento a carpeta rechazados

**Resultado esperado:** 3 archivos movidos a rechazados

### **Caso VD-002: Duplicados en PCs Únicamente**
**Objetivo:** Verificar detección cuando archivo existe solo en PCs  
**Datos de entrada:**
- `reporte_mensual.docx` → pc1, pc3
- `datos_ventas.xlsx` → pc2
- `backup_config.zip` → pc4

**Resultado esperado:** 3 archivos movidos a rechazados

### **Caso VD-003: Duplicados Mixtos (QNAPs + PCs)**
**Objetivo:** Verificar detección cruzada entre tipos de ubicación  
**Datos de entrada:**
- `manual_usuario.pdf` → qnap1, pc2
- `presentacion_anual.pptx` → qnap2, pc1, pc4

**Resultado esperado:** 2 archivos movidos a rechazados

### **Caso VD-004: Archivos Únicos**
**Objetivo:** Verificar que archivos únicos permanecen en origen  
**Datos de entrada:**
- `unique_doc1.txt`, `unique_img1.png`, `unique_vid1.avi`
- No existen en ninguna de las 6 ubicaciones

**Resultado esperado:** 3 archivos permanecen en origen

### **Caso VD-005: Múltiples Duplicados Mismo Archivo**
**Objetivo:** Verificar comportamiento con archivo en múltiples ubicaciones  
**Datos de entrada:**
- `backup_total.zip` → qnap1, qnap2, pc1, pc2, pc3, pc4

**Resultado esperado:** 1 archivo movido a rechazados (primera coincidencia)

### **Caso VD-006: Archivos con Nombres Similares**
**Objetivo:** Verificar coincidencia exacta de nombres  
**Datos de entrada:**
- `reporte_v1.pdf`, `reporte_v2.pdf`, `reporte.pdf`
- Solo `reporte.pdf` existe en qnap1

**Resultado esperado:** Solo `reporte.pdf` movido a rechazados

### **Caso VD-007: Conectividad Parcial**
**Objetivo:** Verificar comportamiento con ubicaciones no accesibles  
**Simulación:** pc3 y pc4 desconectados  
**Datos de entrada:**
- Archivos con duplicados en ubicaciones accesibles e inaccesibles

**Resultado esperado:** Procesa ubicaciones accesibles, reporta errores

### **Caso VD-008: Archivos con Caracteres Especiales**
**Objetivo:** Verificar manejo de nombres de archivo especiales  
**Datos de entrada:**
- `archivo con espacios.pdf`
- `documento-con_guiones.docx`
- `file[with]brackets.txt`

**Resultado esperado:** Manejo correcto de todos los nombres

---

## 🗑️ CASOS DE PRUEBA - LIMPIEZA POR LISTA

### **Caso LP-001: Lista Completa Simple**
**Objetivo:** Verificar eliminación básica en todas las ubicaciones  
**Archivo lista:**
```
documento1.pdf
imagen1.jpg
video1.mp4
archivo1.docx
datos1.xlsx
```
**Distribución:** Cada archivo en 2-3 ubicaciones diferentes  
**Resultado esperado:** 15-18 eliminaciones totales

### **Caso LP-002: Lista con Archivos No Existentes**
**Objetivo:** Verificar manejo de archivos no encontrados  
**Archivo lista:**
```
existe1.pdf
noexiste1.txt
existe2.jpg
noexiste2.docx
existe3.mp4
```
**Resultado esperado:** Solo archivos existentes eliminados, no-existentes reportados

### **Caso LP-003: Lista Malformateada**
**Objetivo:** Verificar filtrado y limpieza de lista  
**Archivo lista:**
```
# Comentario
archivo_valido.pdf

linea_vacia_arriba.txt
archivo con espacios.docx
archivo<invalido>.txt
```
**Resultado esperado:** Solo archivos válidos procesados

### **Caso LP-004: Lista Muy Grande (500+ archivos)**
**Objetivo:** Verificar rendimiento con volumen alto  
**Archivo lista:** 500 archivos distribuidos en 6 ubicaciones  
**Resultado esperado:** Procesamiento completo en <5 minutos

### **Caso LP-005: Ubicaciones Selectivas**
**Objetivo:** Verificar funcionamiento con ubicaciones deshabilitadas  
**Configuración:** Solo qnap1, pc1, pc3 habilitados  
**Resultado esperado:** Solo ubicaciones habilitadas procesadas

### **Caso LP-006: Archivos Duplicados en Lista**
**Objetivo:** Verificar manejo de duplicados en archivo lista  
**Archivo lista:**
```
archivo_duplicado.pdf
documento_normal.txt
archivo_duplicado.pdf
otro_archivo.jpg
archivo_duplicado.pdf
```
**Resultado esperado:** `archivo_duplicado.pdf` eliminado de todas las ubicaciones donde exista

### **Caso LP-007: Fallos de Permisos**
**Objetivo:** Verificar manejo de archivos protegidos  
**Simulación:** Archivos en modo solo lectura  
**Resultado esperado:** Errores reportados, operación continúa

### **Caso LP-008: Eliminación Masiva Simultánea**
**Objetivo:** Verificar threading y concurrencia  
**Archivo lista:** 50 archivos, cada uno en las 6 ubicaciones  
**Resultado esperado:** 300 eliminaciones coordinadas

---

## 🔐 CASOS DE PRUEBA - SISTEMA DE LICENCIAS

### **Caso LIC-001: Aplicación sin Licencia**
**Objetivo:** Verificar bloqueo sin licencia válida  
**Precondición:** Sin archivo de licencia  
**Resultado esperado:** Diálogo de activación obligatorio

### **Caso LIC-002: Licencia Válida**
**Objetivo:** Verificar funcionamiento normal con licencia  
**Precondición:** Licencia válida instalada  
**Resultado esperado:** Acceso completo a funciones

### **Caso LIC-003: Licencia Expirada**
**Objetivo:** Verificar bloqueo con licencia vencida  
**Precondición:** Licencia con fecha pasada  
**Resultado esperado:** Bloqueo y solicitud renovación

### **Caso LIC-004: Licencia Hardware Diferente**
**Objetivo:** Verificar binding de hardware  
**Simulación:** Licencia generada para otro equipo  
**Resultado esperado:** Rechazo y solicitud nueva licencia

---

## 📈 CASOS DE PRUEBA - RENDIMIENTO

### **Caso PERF-001: Volumen Bajo (10 archivos)**
**Métrica objetivo:** <1 seg/archivo  
**Configuración:** 10 archivos × 6 ubicaciones  

### **Caso PERF-002: Volumen Medio (100 archivos)**
**Métrica objetivo:** <2 seg/archivo  
**Configuración:** 100 archivos × 6 ubicaciones  

### **Caso PERF-003: Volumen Alto (500 archivos)**
**Métrica objetivo:** <3 seg/archivo  
**Configuración:** 500 archivos × 6 ubicaciones  

### **Caso PERF-004: Latencia de Red**
**Objetivo:** Verificar comportamiento con latencia alta  
**Simulación:** Añadir delays de red 500ms-2s  

### **Caso PERF-005: Uso de Memoria**
**Objetivo:** Verificar no hay memory leaks  
**Duración:** Operación continua 30 minutos  

---

## 🛠️ PROCEDIMIENTOS DE TESTING

### Preparación del Entorno
1. **Configurar Raspberry Pi**
   ```bash
   sudo apt update
   sudo apt install samba samba-common-bin
   sudo systemctl enable smbd
   ```

2. **Crear usuarios y carpetas**
   ```bash
   for user in qnap1 qnap2 pc1 pc2 pc3 pc4; do
       sudo useradd $user
       sudo mkdir -p /home/<USER>/shared_files
       sudo chown $user:$user /home/<USER>/shared_files
       sudo smbpasswd -a $user
   done
   ```

3. **Generar datos de prueba**
   ```bash
   python generate_test_data.py
   ```

4. **Configurar aplicaciones**
   - Instalar licencias válidas
   - Configurar conexiones SMB
   - Verificar logs habilitados

### Ejecución de Tests

#### Testing Manual
1. **Validación de Duplicados**
   - Ejecutar cada caso VD-001 a VD-008
   - Documentar resultados en matriz
   - Capturar screenshots de resultados

2. **Limpieza por Lista**
   - Ejecutar cada caso LP-001 a LP-008
   - Verificar estadísticas por ubicación
   - Confirmar eliminaciones reales

#### Testing Automatizado
```python
# test_runner.py
def run_validation_tests():
    test_cases = ['VD-001', 'VD-002', 'VD-003', 'VD-004', 'VD-005', 'VD-006', 'VD-007', 'VD-008']
    results = {}
    
    for case in test_cases:
        result = execute_validation_test(case)
        results[case] = result
        
    return results

def run_cleanup_tests():
    test_cases = ['LP-001', 'LP-002', 'LP-003', 'LP-004', 'LP-005', 'LP-006', 'LP-007', 'LP-008']
    results = {}
    
    for case in test_cases:
        result = execute_cleanup_test(case)
        results[case] = result
        
    return results
```

---

## 📋 MATRIZ DE RESULTADOS

### Template de Resultados por Caso
```
CASO: VD-001
FECHA: ___________
EJECUTOR: ___________
RESULTADO: PASS/FAIL
TIEMPO EJECUCIÓN: _____ seg
ARCHIVOS PROCESADOS: _____
DUPLICADOS DETECTADOS: _____
ERRORES: _____
OBSERVACIONES: ___________
```

### Métricas Clave por Aplicación

#### Validación de Duplicados
- **Precisión:** (Duplicados correctos / Total duplicados) × 100
- **Recall:** (Duplicados detectados / Duplicados existentes) × 100
- **Tiempo promedio:** Total tiempo / Total archivos
- **Tasa de error:** Errores / Total operaciones × 100

#### Limpieza por Lista
- **Completitud:** (Archivos eliminados / Archivos encontrados) × 100
- **Eficiencia:** (Ubicaciones exitosas / Total ubicaciones) × 100
- **Throughput:** Archivos procesados / Tiempo total
- **Robustez:** Recuperación exitosa de errores

---

## ⚠️ CASOS EDGE Y MANEJO DE ERRORES

### Casos Edge Críticos
1. **Archivos en uso:** Aplicación trata de eliminar archivo abierto
2. **Permisos insuficientes:** Usuario sin permisos de eliminación
3. **Espacio insuficiente:** Disco lleno al mover a rechazados
4. **Nombres extremos:** Archivos con 255 caracteres de nombre
5. **Caracteres Unicode:** Archivos con caracteres especiales
6. **Archivos corruptos:** Archivos dañados o incompletos

### Simulación de Fallos de Red
```python
def simulate_network_failures():
    scenarios = [
        'timeout_connection',
        'intermittent_disconnection',
        'authentication_failure', 
        'permission_denied',
        'host_unreachable'
    ]
    
    for scenario in scenarios:
        test_with_network_failure(scenario)
```

---

## 📊 REPORTING Y DOCUMENTACIÓN

### Formato de Reporte Final
```markdown
# REPORTE DE TESTING - Gestor de Archivos v2.0

## Resumen Ejecutivo
- Total casos ejecutados: ___
- Casos exitosos: ___
- Casos fallidos: ___
- Cobertura de funcionalidad: ___%
- Tiempo total de testing: ___

## Resultados por Módulo
### Validación de Duplicados
- Precisión: ___%
- Casos críticos: PASS/FAIL
- Rendimiento: ___ seg/archivo

### Limpieza por Lista  
- Completitud: ___%
- Casos críticos: PASS/FAIL
- Rendimiento: ___ archivos/min

## Issues Encontrados
[Lista de bugs y problemas]

## Recomendaciones
[Mejoras sugeridas]
```

### Documentación de Setup
- **Configuración de red detallada**
- **Scripts de generación de datos**
- **Procedimientos de instalación**
- **Troubleshooting guide**

---

## ✅ CHECKLIST DE VALIDACIÓN FINAL

### Pre-Testing
- [ ] Raspberry Pi configurado y operativo
- [ ] 6 usuarios SMB creados y testeados
- [ ] Datos de prueba generados correctamente
- [ ] Aplicaciones con licencias válidas
- [ ] Conectividad de red verificada

### Testing Execution
- [ ] Casos VD-001 a VD-008 ejecutados
- [ ] Casos LP-001 a LP-008 ejecutados
- [ ] Casos LIC-001 a LIC-004 ejecutados
- [ ] Casos PERF-001 a PERF-005 ejecutados
- [ ] Casos edge documentados

### Post-Testing
- [ ] Resultados documentados
- [ ] Bugs reportados y catalogados
- [ ] Métricas de rendimiento calculadas
- [ ] Reporte final generado
- [ ] Recomendaciones documentadas

---

## 🔧 HERRAMIENTAS DE APOYO

### Scripts de Utilidad
```python
# monitor_resources.py - Monitoreo durante testing
# cleanup_test_data.py - Limpieza post-testing
# generate_reports.py - Generación automática de reportes
# network_simulator.py - Simulación de condiciones de red
```

### Herramientas de Monitoreo
- **Wireshark:** Captura de tráfico SMB
- **Resource Monitor:** Uso de CPU/memoria
- **Performance Toolkit:** Métricas de aplicación
- **Log Analyzer:** Análisis automático de logs

---

**Documento preparado por:** Equipo de Desarrollo  
**Revisado por:** QA Team  
**Aprobado por:** Project Manager  

*Este documento debe actualizarse con cada iteración del testing*
