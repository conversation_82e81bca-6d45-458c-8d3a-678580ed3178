"""
Test Sesión 6 - Dashboard y Aplicación Principal
==============================================

Verifica que el dashboard y la aplicación principal funcionen correctamente
"""

import sys
from pathlib import Path
import threading
import time

# Agregar src al path para imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_dashboard_initialization():
    """Test de inicialización del dashboard"""
    print("🖥️ Testing Dashboard Initialization...")
    
    try:
        # Test import
        from ui.dashboard import GestorDashboard
        print("  ✓ Dashboard importado exitosamente")
        
        # Test configuración inicial
        from core.config_manager import config
        app_info = config.get_app_info()
        print(f"  ✓ App info: {app_info.get('name')} v{app_info.get('version')}")
        
        # Test ubicaciones
        locations = config.get_enabled_locations()
        print(f"  ✓ Ubicaciones configuradas: {len(locations)}")
        
        # Intentar crear dashboard (sin mostrar)
        print("  🔍 Creando dashboard...")
        dashboard = GestorDashboard()
        print("  ✓ Dashboard creado exitosamente")
        
        # Verificar componentes principales
        print("  🔍 Verificando componentes...")
        
        # Verificar notebook
        if hasattr(dashboard, 'notebook'):
            tabs_count = len(dashboard.notebook.tabs())
            print(f"    ✓ Notebook con {tabs_count} pestañas")
        
        # Verificar widgets de ubicaciones
        if hasattr(dashboard, 'ubicacion_widgets'):
            widgets_count = len(dashboard.ubicacion_widgets)
            print(f"    ✓ Widgets de ubicaciones: {widgets_count}")
        
        # Verificar variables de estado
        if hasattr(dashboard, 'operaciones_activas'):
            print(f"    ✓ Estado operaciones: {dashboard.operaciones_activas}")
        
        # Cerrar dashboard sin mostrar
        dashboard.root.destroy()
        print("  ✓ Dashboard cerrado correctamente")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error en inicialización del dashboard: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dashboard_components():
    """Test de componentes específicos del dashboard"""
    print("\n🎛️ Testing Dashboard Components...")
    
    try:
        from ui.dashboard import GestorDashboard
        
        # Crear dashboard
        dashboard = GestorDashboard()
        
        # Test de métodos principales
        methods_to_test = [
            'update_status',
            'format_validacion_results',
            'format_limpieza_results',
            'browse_folder',
            'save_validacion_config'
        ]
        
        print("  🔍 Testing métodos principales...")
        for method_name in methods_to_test:
            if hasattr(dashboard, method_name):
                print(f"    ✓ Método '{method_name}' disponible")
            else:
                print(f"    ❌ Método '{method_name}' faltante")
        
        # Test de variables de UI
        ui_vars = [
            'validacion_origen_var',
            'validacion_rechazados_var',
            'validacion_dry_run_var',
            'limpieza_archivo_var',
            'limpieza_dry_run_var',
            'log_category_var'
        ]
        
        print("  🔍 Testing variables de UI...")
        for var_name in ui_vars:
            if hasattr(dashboard, var_name):
                print(f"    ✓ Variable '{var_name}' disponible")
            else:
                print(f"    ❌ Variable '{var_name}' faltante")
        
        # Test de actualización de estado
        print("  🔍 Testing actualización de estado...")
        dashboard.update_status("Test status message")
        current_status = dashboard.status_var.get()
        if current_status == "Test status message":
            print("    ✓ Actualización de estado funcionando")
        else:
            print(f"    ⚠️ Estado esperado vs actual: 'Test status message' vs '{current_status}'")
        
        # Test de formateo de resultados
        print("  🔍 Testing formateo de resultados...")
        
        # Mock stats para validación
        mock_validacion_stats = {
            'archivos_escaneados': 10,
            'archivos_duplicados': 3,
            'archivos_no_duplicados': 7,
            'archivos_movidos': 3,
            'archivos_fallidos': 0,
            'duplicados_por_qnap': {'qnap1': 2, 'qnap2': 1},
            'tiempo_inicio': time.time(),
            'tiempo_fin': time.time() + 5.5,
            'errores': []
        }
        
        formatted_validacion = dashboard.format_validacion_results(mock_validacion_stats, True)
        if "SIMULACIÓN" in formatted_validacion and "archivos escaneados: 10" in formatted_validacion:
            print("    ✓ Formateo de resultados de validación")
        else:
            print("    ❌ Error en formateo de validación")
        
        # Mock stats para limpieza
        mock_limpieza_stats = {
            'total_archivos_lista': 15,
            'archivos_validos': 14,
            'ubicaciones_conectadas': 4,
            'total_archivos_encontrados': 8,
            'total_archivos_eliminados': 7,
            'total_archivos_fallidos': 1,
            'duracion_total': 12.3,
            'estadisticas_por_ubicacion': {
                'qnap1': {
                    'conectado': True,
                    'tipo_handler': 'QNAP',
                    'archivos_encontrados': 3,
                    'archivos_eliminados': 3,
                    'tiempo_procesamiento': 2.1
                }
            },
            'errores_generales': []
        }
        
        formatted_limpieza = dashboard.format_limpieza_results(mock_limpieza_stats, True)
        if "SIMULACIÓN" in formatted_limpieza and "Archivos válidos: 14" in formatted_limpieza:
            print("    ✓ Formateo de resultados de limpieza")
        else:
            print("    ❌ Error en formateo de limpieza")
        
        # Cerrar dashboard
        dashboard.root.destroy()
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error en test de componentes: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_application():
    """Test de la aplicación principal"""
    print("\n🚀 Testing Main Application...")
    
    try:
        # Test import del main
        import main
        print("  ✓ Módulo main importado")
        
        # Test de funciones principales
        if hasattr(main, 'check_dependencies'):
            deps_ok = main.check_dependencies()
            print(f"  ✓ Check dependencies: {'OK' if deps_ok else 'Some missing'}")
        
        if hasattr(main, 'setup_logging'):
            try:
                logger = main.setup_logging()
                print("  ✓ Setup logging exitoso")
            except Exception as e:
                print(f"  ⚠️ Setup logging warning: {e}")
        
        # Test de configuración
        from core.config_manager import config
        validation = config.validate_config()
        print(f"  ✓ Config validation: {len(validation['errors'])} errors, {len(validation['warnings'])} warnings")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error en test de aplicación principal: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_operations():
    """Test de integración con operaciones"""
    print("\n🔗 Testing Integration with Operations...")
    
    try:
        # Test import de operaciones
        from operations.validacion_duplicados import ValidacionDuplicados
        from operations.limpieza_lista import LimpiezaLista
        print("  ✓ Operaciones importadas")
        
        # Test integración con handlers
        from handlers.qnap_handler import QnapHandler
        from handlers.pc_handler import PCHandler
        print("  ✓ Handlers importados")
        
        # Test integración con dashboard
        from ui.dashboard import GestorDashboard
        
        # Crear dashboard temporal
        dashboard = GestorDashboard()
        
        # Verificar que puede acceder a las operaciones
        try:
            # Test configuración de validación
            validacion_config = dashboard.validacion_origen_var.get()
            print(f"  ✓ Configuración validación accesible: {len(validacion_config)} chars")
            
            # Test configuración de limpieza
            limpieza_config = dashboard.limpieza_archivo_var.get()
            print(f"  ✓ Configuración limpieza accesible: {len(limpieza_config)} chars")
            
        except Exception as e:
            print(f"  ⚠️ Warning en configuración UI: {e}")
        
        # Test de handlers
        from core.config_manager import config
        locations = config.get_enabled_locations()
        
        if locations:
            first_location = list(locations.keys())[0]
            
            # Test creación de handler apropiado
            if 'qnap' in first_location.lower():
                handler = QnapHandler(first_location)
                print(f"  ✓ QNAP Handler creado para {first_location}")
            else:
                handler = PCHandler(first_location)
                print(f"  ✓ PC Handler creado para {first_location}")
        
        dashboard.root.destroy()
        return True
        
    except Exception as e:
        print(f"  ❌ Error en test de integración: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_threading():
    """Test de threading en UI"""
    print("\n🧵 Testing UI Threading...")
    
    try:
        from ui.dashboard import GestorDashboard
        
        dashboard = GestorDashboard()
        
        # Variables para test
        test_results = {'completed': False, 'error': None}
        
        def mock_operation():
            """Operación mock para probar threading"""
            try:
                time.sleep(0.1)  # Simular trabajo
                dashboard.root.after(0, lambda: setattr(test_results, 'completed', True))
            except Exception as e:
                test_results['error'] = str(e)
        
        # Test de ejecución en thread
        thread = threading.Thread(target=mock_operation, daemon=True)
        thread.start()
        
        # Esperar un poco
        start_time = time.time()
        while not test_results['completed'] and time.time() - start_time < 1.0:
            dashboard.root.update()
            time.sleep(0.01)
        
        if test_results['completed']:
            print("  ✓ Threading funcionando correctamente")
        elif test_results['error']:
            print(f"  ❌ Error en threading: {test_results['error']}")
        else:
            print("  ⚠️ Threading timeout (posible)")
        
        dashboard.root.destroy()
        return True
        
    except Exception as e:
        print(f"  ❌ Error en test de threading: {e}")
        return False

def main():
    """Ejecuta todos los tests de la Sesión 6"""
    print("🚀 SESIÓN 6 - TEST DE DASHBOARD Y APLICACIÓN PRINCIPAL")
    print("=" * 65)
    
    tests = [
        ("Dashboard Initialization", test_dashboard_initialization),
        ("Dashboard Components", test_dashboard_components),
        ("Main Application", test_main_application),
        ("Integration with Operations", test_integration_operations),
        ("UI Threading", test_ui_threading)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*15} {test_name} {'='*15}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  💥 {test_name} CRASHED: {e}")
            results.append((test_name, False))
    
    # Resumen final
    print("\n" + "=" * 70)
    print("📊 RESUMEN DE TESTS - SESIÓN 6")
    print("=" * 70)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResultado: {passed}/{len(results)} tests pasaron")
    
    if passed == len(results):
        print("\n🎉 ¡SESIÓN 6 COMPLETADA EXITOSAMENTE!")
        print("✅ Dashboard profesional implementado")
        print("✅ Interfaz gráfica completa funcional")
        print("✅ Integración total con todas las operaciones")
        print("✅ Threading y UI responsiva")
        print("✅ Aplicación principal lista para uso")
        print("\n🏆 ¡APLICACIÓN CORPORATIVA COMPLETA!")
        print("🚀 Lista para compilar a .exe y distribuir")
    else:
        print("\n⚠️ Algunos tests fallaron - revisar implementación")
        print("💡 La aplicación puede funcionar parcialmente")

if __name__ == "__main__":
    main()
