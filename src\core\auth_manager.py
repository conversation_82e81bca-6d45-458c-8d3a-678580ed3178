"""
Gestor de Archivos Corporativo - Authentication Manager
=====================================================

Maneja autenticación independiente por ubicación con soporte para dominios
"""

import os
import subprocess
import platform
import time
from pathlib import Path
from typing import Dict, Optional, Tuple, List
import logging
from .config_manager import config
from .utils import extract_hostname_from_path, ping_host

class AuthenticationManager:
    """Gestor de autenticación para ubicaciones remotas"""
    
    def __init__(self):
        self.logger = logging.getLogger('gestor.sistema')
        self.authenticated_sessions: Dict[str, dict] = {}
        self.auth_cache_duration = 300  # 5 minutos
        
    def authenticate_location(self, location_id: str) -> Tuple[bool, str]:
        """
        Autentica una ubicación específica
        
        Args:
            location_id: ID de la ubicación (ej: 'qnap1', 'pc1')
        
        Returns:
            <PERSON><PERSON>[bool, str]: (éxito, mensaje)
        """
        self.logger.info(f"Iniciando autenticación para ubicación: {location_id}")
        
        try:
            # Obtener configuración de la ubicación
            location_config = config.get(f'locations.{location_id}')
            if not location_config:
                return False, f"Ubicación {location_id} no encontrada en configuración"
            
            if not location_config.get('enabled', False):
                return False, f"Ubicación {location_id} está deshabilitada"
            
            # Obtener credenciales
            credentials = config.get_credentials(location_id)
            if not credentials['username']:
                self.logger.warning(f"No hay credenciales configuradas para {location_id}")
                return True, f"Sin credenciales - acceso como usuario actual"
            
            # Intentar autenticación
            network_path = location_config.get('network_path', '')
            if network_path:
                success, message = self._authenticate_network_path(
                    location_id, network_path, credentials
                )
                
                if success:
                    # Guardar sesión autenticada
                    self.authenticated_sessions[location_id] = {
                        'timestamp': time.time(),
                        'network_path': network_path,
                        'username': credentials['username'],
                        'domain': credentials['domain']
                    }
                    self.logger.info(f"Autenticación exitosa: {location_id}")
                
                return success, message
            else:
                return False, f"No hay network_path configurado para {location_id}"
                
        except Exception as e:
            error_msg = f"Error autenticando {location_id}: {e}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def _authenticate_network_path(self, location_id: str, network_path: str, 
                                 credentials: Dict[str, str]) -> Tuple[bool, str]:
        """Autentica ruta de red usando credenciales"""
        try:
            username = credentials['username']
            password = credentials['password']
            domain = credentials.get('domain', '')
            
            if not username or not password:
                return True, "Sin credenciales - usando usuario actual"
            
            # Construir comando de autenticación
            if platform.system().lower() == 'windows':
                # Usar net use para Windows
                cmd = ['net', 'use', network_path, password, f'/user:{username}']
                
                # Agregar dominio si está especificado
                if domain:
                    cmd[-1] = f'{domain}\\{username}'
                
                # Agregar parámetros adicionales
                cmd.extend(['/persistent:no'])
                
                self.logger.debug(f"Ejecutando comando de autenticación para {location_id}")
                
                # Ejecutar comando
                result = subprocess.run(
                    cmd, 
                    capture_output=True, 
                    text=True, 
                    timeout=30
                )
                
                if result.returncode == 0:
                    return True, f"Autenticación exitosa para {network_path}"
                else:
                    error_output = result.stderr.strip()
                    # Manejar errores comunes
                    if 'already in use' in error_output.lower():
                        return True, f"Conexión ya establecida para {network_path}"
                    elif 'access denied' in error_output.lower():
                        return False, f"Acceso denegado - verificar credenciales"
                    elif 'network path not found' in error_output.lower():
                        return False, f"Ruta de red no encontrada: {network_path}"
                    else:
                        return False, f"Error de autenticación: {error_output}"
            else:
                # Para sistemas no Windows, usar smbclient o similar
                return self._authenticate_smb_linux(network_path, credentials)
                
        except subprocess.TimeoutExpired:
            return False, f"Timeout en autenticación para {network_path}"
        except Exception as e:
            return False, f"Error ejecutando autenticación: {e}"
    
    def _authenticate_smb_linux(self, network_path: str, credentials: Dict[str, str]) -> Tuple[bool, str]:
        """Autenticación SMB para sistemas Linux/Unix"""
        try:
            # Implementación básica para Linux (requiere smbclient)
            cmd = [
                'smbclient', 
                network_path.replace('\\', '/'), 
                credentials['password'],
                '-U', f"{credentials['domain']}\\{credentials['username']}" if credentials['domain'] else credentials['username'],
                '-c', 'quit'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                return True, f"Autenticación SMB exitosa para {network_path}"
            else:
                return False, f"Error autenticación SMB: {result.stderr.strip()}"
                
        except FileNotFoundError:
            return False, "smbclient no encontrado - instalar samba-client"
        except Exception as e:
            return False, f"Error autenticación SMB: {e}"
    
    def is_authenticated(self, location_id: str) -> bool:
        """Verifica si una ubicación está autenticada y la sesión es válida"""
        if location_id not in self.authenticated_sessions:
            return False
        
        session = self.authenticated_sessions[location_id]
        elapsed = time.time() - session['timestamp']
        
        if elapsed > self.auth_cache_duration:
            # Sesión expirada
            del self.authenticated_sessions[location_id]
            self.logger.debug(f"Sesión autenticada expirada para {location_id}")
            return False
        
        return True
    
    def disconnect_location(self, location_id: str) -> Tuple[bool, str]:
        """Desconecta una ubicación específica"""
        try:
            if location_id not in self.authenticated_sessions:
                return True, f"No hay sesión activa para {location_id}"
            
            session = self.authenticated_sessions[location_id]
            network_path = session['network_path']
            
            if platform.system().lower() == 'windows':
                # Usar net use /delete
                cmd = ['net', 'use', network_path, '/delete', '/y']
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
                
                if result.returncode == 0:
                    del self.authenticated_sessions[location_id]
                    self.logger.info(f"Desconexión exitosa: {location_id}")
                    return True, f"Desconectado de {network_path}"
                else:
                    # Aún así, limpiar la sesión del cache
                    del self.authenticated_sessions[location_id]
                    return True, f"Sesión limpiada para {location_id}"
            else:
                # Para Linux, simplemente limpiar cache
                del self.authenticated_sessions[location_id]
                return True, f"Sesión limpiada para {location_id}"
                
        except Exception as e:
            # Limpiar sesión en caso de error
            if location_id in self.authenticated_sessions:
                del self.authenticated_sessions[location_id]
            
            error_msg = f"Error desconectando {location_id}: {e}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def disconnect_all(self) -> Dict[str, Tuple[bool, str]]:
        """Desconecta todas las ubicaciones autenticadas"""
        results = {}
        
        # Hacer copia de las claves para evitar modificar dict durante iteración
        location_ids = list(self.authenticated_sessions.keys())
        
        for location_id in location_ids:
            success, message = self.disconnect_location(location_id)
            results[location_id] = (success, message)
        
        self.logger.info(f"Desconectadas {len(location_ids)} ubicaciones")
        return results
    
    def get_authenticated_locations(self) -> List[str]:
        """Obtiene lista de ubicaciones actualmente autenticadas"""
        # Filtrar sesiones expiradas
        current_time = time.time()
        expired_locations = []
        
        for location_id, session in self.authenticated_sessions.items():
            if current_time - session['timestamp'] > self.auth_cache_duration:
                expired_locations.append(location_id)
        
        # Limpiar sesiones expiradas
        for location_id in expired_locations:
            del self.authenticated_sessions[location_id]
        
        return list(self.authenticated_sessions.keys())
    
    def test_authentication(self, location_id: str) -> Tuple[bool, str, Dict]:
        """
        Prueba la autenticación de una ubicación y retorna información detallada
        
        Returns:
            Tuple[bool, str, Dict]: (éxito, mensaje, info_detallada)
        """
        info = {
            'location_id': location_id,
            'timestamp': time.time(),
            'network_available': False,
            'authentication_success': False,
            'response_time_ms': None,
            'error_details': None
        }
        
        try:
            # Obtener configuración
            location_config = config.get(f'locations.{location_id}')
            if not location_config:
                return False, f"Ubicación {location_id} no configurada", info
            
            network_path = location_config.get('network_path', '')
            if not network_path:
                return False, f"No hay network_path para {location_id}", info
            
            # Test de conectividad de red
            hostname = extract_hostname_from_path(network_path)
            if hostname:
                start_time = time.time()
                network_available = ping_host(hostname, timeout=5)
                response_time = (time.time() - start_time) * 1000
                
                info['network_available'] = network_available 
                info['response_time_ms'] = response_time
                
                if not network_available:
                    return False, f"Host {hostname} no accesible", info
            
            # Test de autenticación
            auth_start = time.time()
            auth_success, auth_message = self.authenticate_location(location_id)
            auth_time = (time.time() - auth_start) * 1000
            
            info['authentication_success'] = auth_success
            info['auth_time_ms'] = auth_time
            
            return auth_success, auth_message, info
            
        except Exception as e:
            info['error_details'] = str(e)
            return False, f"Error en test de autenticación: {e}", info
    
    def get_session_info(self, location_id: str) -> Optional[Dict]:
        """Obtiene información de la sesión autenticada"""
        if location_id in self.authenticated_sessions:
            session = self.authenticated_sessions[location_id].copy()
            session['age_seconds'] = time.time() - session['timestamp']
            session['expires_in'] = self.auth_cache_duration - session['age_seconds']
            return session
        return None
    
    def refresh_session(self, location_id: str) -> Tuple[bool, str]:
        """Refresca una sesión autenticada"""
        if location_id in self.authenticated_sessions:
            # Desconectar y reconectar
            self.disconnect_location(location_id)
            return self.authenticate_location(location_id)
        else:
            return self.authenticate_location(location_id)

# Instancia global del gestor de autenticación
auth_manager = AuthenticationManager()

if __name__ == "__main__":
    # Test básico del sistema de autenticación
    print("🔐 Authentication Manager Test")
    print("=" * 40)
    
    # Test con ubicaciones configuradas
    locations = config.get_enabled_locations()
    print(f"Ubicaciones habilitadas: {len(locations)}")
    
    for location_id in list(locations.keys())[:2]:  # Test solo 2 primeras
        print(f"\n🔍 Testing {location_id}...")
        
        success, message, info = auth_manager.test_authentication(location_id)
        status = "✅" if success else "❌"
        print(f"  {status} {message}")
        
        if info.get('response_time_ms'):
            print(f"  📡 Response time: {info['response_time_ms']:.1f}ms")
        
        if success:
            session_info = auth_manager.get_session_info(location_id)
            if session_info:
                print(f"  👤 User: {session_info['username']}")
                print(f"  🕒 Expires in: {session_info['expires_in']:.0f}s")
    
    # Mostrar ubicaciones autenticadas
    authenticated = auth_manager.get_authenticated_locations()
    print(f"\n🔓 Authenticated locations: {len(authenticated)}")
    for loc in authenticated:
        print(f"  - {loc}")
