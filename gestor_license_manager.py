"""
Sistema de Licencias Gestor de Archivos Corporativo
===================================================

Basado en OTS License Manager - Adaptado para Gestor
"""

import hashlib
import platform
import subprocess
import uuid
import json
import os
from datetime import datetime
import logging

class HardwareFingerprint:
    """Genera fingerprint único del hardware"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def get_system_info(self):
        """Obtiene información del sistema"""
        try:
            info = {
                'platform': platform.platform(),
                'processor': platform.processor(),
                'machine': platform.machine(),
                'node': platform.node()
            }
            
            # CPU ID en Windows
            if platform.system() == "Windows":
                try:
                    result = subprocess.run(['wmic', 'cpu', 'get', 'ProcessorId'], 
                                          capture_output=True, text=True)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines:
                            if line.strip() and 'ProcessorId' not in line:
                                info['cpu_id'] = line.strip()
                                break
                except:
                    pass
            
            # MAC Address
            try:
                mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                               for elements in range(0,2*6,2)][::-1])
                info['mac'] = mac
            except:
                pass
            
            return info
        except Exception as e:
            self.logger.error(f"Error getting system info: {e}")
            return {}
    
    def generate_fingerprint(self):
        """Genera fingerprint único"""
        try:
            info = self.get_system_info()
            
            # Crear string combinado
            combined = f"{info.get('processor', '')}{info.get('cpu_id', '')}{info.get('mac', '')}{info.get('machine', '')}"
            
            # Hash SHA256
            fingerprint = hashlib.sha256(combined.encode()).hexdigest()[:16]
            
            return fingerprint.upper()
        except Exception as e:
            self.logger.error(f"Error generating fingerprint: {e}")
            return "UNKNOWN_HARDWARE"

class GestorLicenseManager:
    """Maneja validación de licencias para Gestor de Archivos"""
    
    def __init__(self, app_name="gestor"):
        self.logger = logging.getLogger(__name__)
        self.hardware = HardwareFingerprint()
        self.app_name = app_name
        self.license_file = f"gestor_{app_name}_license.lic"
        
        # Secret keys específicos por aplicación
        self.secret_keys = {
            "gestor": "GESTOR_ARCHIVOS_2025_SECURE",
            "validacion": "GESTOR_VALIDACION_2025_SEC",
            "limpieza": "GESTOR_LIMPIEZA_2025_SEC"
        }
        self.secret_key = self.secret_keys.get(app_name, "GESTOR_DEFAULT_2025_SEC")
    
    def generate_license_hash(self, hardware_id, expiry_date, license_type="STANDARD"):
        """Genera hash de licencia (para validación)"""
        try:
            combined = f"{hardware_id}{expiry_date}{license_type}{self.secret_key}"
            return hashlib.sha256(combined.encode()).hexdigest()
        except Exception as e:
            self.logger.error(f"Error generating license hash: {e}")
            return None
    
    def save_license(self, hardware_id, license_hash, expiry_date, license_type="STANDARD"):
        """Guarda licencia válida proporcionada por el desarrollador"""
        try:
            license_data = {
                "app_name": self.app_name,
                "hardware_id": hardware_id,
                "license_type": license_type,
                "issued_date": datetime.now().strftime("%Y-%m-%d"),
                "expiry_date": expiry_date,
                "hash": license_hash,
                "version": "1.0"
            }
            
            with open(self.license_file, 'w') as f:
                json.dump(license_data, f, indent=2)
            
            return True
        except Exception as e:
            self.logger.error(f"Error saving license: {e}")
            return False
    
    def validate_license(self):
        """Valida licencia existente"""
        try:
            if not os.path.exists(self.license_file):
                return False, "No license file found"
            
            with open(self.license_file, 'r') as f:
                license_data = json.load(f)
            
            # Verificar app
            if license_data.get('app_name') != self.app_name:
                return False, "License not valid for this application"
            
            # Verificar hardware
            current_hardware = self.hardware.generate_fingerprint()
            if license_data.get('hardware_id') != current_hardware:
                return False, "License not valid for this hardware"
            
            # Verificar fecha de expiración
            expiry_date = datetime.strptime(license_data.get('expiry_date'), "%Y-%m-%d")
            if datetime.now() > expiry_date:
                return False, "License has expired"
            
            # Verificar hash
            expected_hash = self.generate_license_hash(
                license_data.get('hardware_id'),
                license_data.get('expiry_date'),
                license_data.get('license_type', 'STANDARD')
            )
            
            if license_data.get('hash') != expected_hash:
                return False, "License hash verification failed"
            
            return True, f"Valid until {license_data.get('expiry_date')}"
            
        except Exception as e:
            self.logger.error(f"Error validating license: {e}")
            return False, f"License validation error: {e}"
    
    def get_license_info(self):
        """Obtiene información de licencia"""
        try:
            if not os.path.exists(self.license_file):
                return None
            
            with open(self.license_file, 'r') as f:
                return json.load(f)
        except:
            return None
    
    def get_hardware_id(self):
        """Obtiene Hardware ID actual"""
        return self.hardware.generate_fingerprint()

def show_license_dialog(parent=None, app_name="gestor"):
    """Muestra diálogo de activación de licencia"""
    import tkinter as tk
    from tkinter import ttk, messagebox
    
    license_manager = GestorLicenseManager(app_name)
    
    # Verificar si ya tiene licencia válida
    valid, message = license_manager.validate_license()
    if valid:
        messagebox.showinfo("License", f"✅ {message}")
        return True
    
    # Mostrar diálogo de activación
    dialog = tk.Toplevel(parent) if parent else tk.Tk()
    dialog.title(f"Activar Licencia - Gestor {app_name.title()}")
    dialog.geometry("550x400")
    dialog.resizable(False, False)
    dialog.grab_set()
    
    frame = ttk.Frame(dialog, padding="20")
    frame.pack(fill='both', expand=True)
    
    # Título
    ttk.Label(frame, text=f"Activación de Licencia", 
             font=("Arial", 14, "bold")).pack(pady=(0,10))
    
    ttk.Label(frame, text=f"Gestor de Archivos - {app_name.title()}", 
             font=("Arial", 10)).pack(pady=(0,20))
    
    # Hardware ID
    hardware_id = license_manager.get_hardware_id()
    ttk.Label(frame, text="Hardware ID (enviar al desarrollador):").pack(anchor='w')
    
    hw_frame = ttk.Frame(frame)
    hw_frame.pack(fill='x', pady=(5,15))
    
    hw_var = tk.StringVar(value=hardware_id)
    hw_entry = ttk.Entry(hw_frame, textvariable=hw_var, state='readonly', font=("Courier", 9))
    hw_entry.pack(side='left', fill='x', expand=True, padx=(0,5))
    
    def copy_hardware():
        dialog.clipboard_clear()
        dialog.clipboard_append(hardware_id)
        copy_btn.config(text="✓")
        dialog.after(1500, lambda: copy_btn.config(text="Copy"))
    
    copy_btn = ttk.Button(hw_frame, text="Copy", command=copy_hardware, width=8)
    copy_btn.pack(side='right')
    
    # Hash de licencia
    ttk.Label(frame, text="Hash de Licencia (proporcionado por desarrollador):").pack(anchor='w', pady=(10,5))
    hash_var = tk.StringVar()
    hash_entry = ttk.Entry(frame, textvariable=hash_var, width=60, font=("Courier", 8))
    hash_entry.pack(fill='x', pady=(0,5))
    
    # Fecha de expiración
    ttk.Label(frame, text="Fecha de Expiración (YYYY-MM-DD):").pack(anchor='w', pady=(10,5))
    date_var = tk.StringVar()
    date_entry = ttk.Entry(frame, textvariable=date_var, width=25)
    date_entry.pack(anchor='w', pady=(0,15))
    
    result = {"activated": False}
    
    def activate_license():
        try:
            license_hash = hash_var.get().strip()
            expiry_date = date_var.get().strip()
            
            if not license_hash or not expiry_date:
                messagebox.showerror("Error", "Todos los campos son requeridos")
                return
            
            # Verificar formato de fecha
            datetime.strptime(expiry_date, "%Y-%m-%d")
            
            # Guardar y validar licencia
            if license_manager.save_license(hardware_id, license_hash, expiry_date):
                valid, message = license_manager.validate_license()
                if valid:
                    messagebox.showinfo("Éxito", f"✅ Licencia activada\n{message}")
                    result["activated"] = True
                    dialog.destroy()
                else:
                    messagebox.showerror("Error", f"❌ {message}")
            else:
                messagebox.showerror("Error", "Error al guardar licencia")
                
        except ValueError:
            messagebox.showerror("Error", "Formato de fecha inválido (use YYYY-MM-DD)")
        except Exception as e:
            messagebox.showerror("Error", f"Error: {e}")
    
    def skip_license():
        messagebox.showerror("Licencia Requerida", "Esta aplicación requiere una licencia válida para funcionar.")
        result["activated"] = False
        dialog.destroy()
    
    # Botones
    button_frame = ttk.Frame(frame)
    button_frame.pack(fill='x', pady=(20,0))
    
    ttk.Button(button_frame, text="Activar Licencia", width=15,
              command=activate_license).pack(side='left', padx=(0,10))
    ttk.Button(button_frame, text="Salir", width=10,
              command=skip_license).pack(side='left')
    
    dialog.protocol("WM_DELETE_WINDOW", skip_license)
    
    if parent:
        dialog.transient(parent)
        dialog.wait_window()
    else:
        dialog.mainloop()
    
    return result["activated"]

if __name__ == "__main__":
    # Test del sistema
    print("🔐 Gestor License Manager - Test")
    print("=" * 40)
    
    # Test para cada aplicación
    for app in ["validacion", "limpieza", "gestor"]:
        print(f"\n{app.upper()}:")
        lm = GestorLicenseManager(app)
        fingerprint = lm.get_hardware_id()
        print(f"Hardware ID: {fingerprint}")
        valid, message = lm.validate_license()
        print(f"License: {'✅' if valid else '❌'} {message}")
    
    # Test GUI
    print("\nTesting license dialog...")
    show_license_dialog(app_name="validacion")
