"""
Test Sesión 1 - Configuración + Utilidades Base
==============================================

Verifica que todos los módulos core funcionen correctamente
"""

import sys
from pathlib import Path

# Agregar src al path para imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_config_manager():
    """Test del gestor de configuración"""
    print("🔧 Testing Config Manager...")
    
    try:
        from core.config_manager import ConfigManager
        
        # Inicializar config manager
        config = ConfigManager()
        
        # Test básico de lectura
        app_name = config.get('app_info.name')
        print(f"  ✓ App Name: {app_name}")
        
        # Test ubicaciones
        locations = config.get_enabled_locations()
        print(f"  ✓ Enabled Locations: {len(locations)}")
        
        # Test credenciales (encriptación/desencriptación)
        config.set_credentials('qnap1', 'test_user', 'test_password', 'TEST_DOMAIN')
        creds = config.get_credentials('qnap1')
        print(f"  ✓ Credentials Test: {creds['username']} / {'*' * len(creds['password'])}")
        
        # Test validación
        validation = config.validate_config()
        print(f"  ✓ Config Validation: {len(validation['errors'])} errors, {len(validation['warnings'])} warnings")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Config Manager Error: {e}")
        return False

def test_utils():
    """Test de utilidades"""
    print("\n🛠️  Testing Utils...")
    
    try:
        from core.utils import (
            get_app_directory, format_file_size, format_duration,
            validate_file_list, get_system_info, ProgressTracker
        )
        
        # Test directorios
        app_dir = get_app_directory()
        print(f"  ✓ App Directory: {app_dir}")
        
        # Test formateo
        size_str = format_file_size(1024*1024*2.5)
        duration_str = format_duration(3661.5)
        print(f"  ✓ Formatting: {size_str}, {duration_str}")
        
        # Test validación de archivos
        test_files = ["archivo1.txt", "archivo2.pdf", "archivo..malo", "archivo<invalido>.txt"]
        validation = validate_file_list(test_files)
        print(f"  ✓ File Validation: {len(validation['valid'])} valid, {len(validation['invalid_names'])} invalid")
        
        # Test información del sistema
        sys_info = get_system_info()
        print(f"  ✓ System Info: {sys_info['system']} - Python {sys_info['python_version']}")
        
        # Test progress tracker
        progress = ProgressTracker(100, "Test Progress")
        progress.update(50)
        progress_info = progress.get_progress_info()
        print(f"  ✓ Progress Tracker: {progress_info['percentage']:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Utils Error: {e}")
        return False

def test_logger():
    """Test del sistema de logging"""
    print("\n📝 Testing Logger System...")
    
    try:
        from core.logger import (
            get_logger, log_operation_start, log_operation_end,
            log_file_operation
        )
        
        # Test loggers por categoría
        sistema_logger = get_logger('sistema')
        limpieza_logger = get_logger('limpieza')
        validacion_logger = get_logger('validacion')
        conectividad_logger = get_logger('conectividad')
        
        print("  ✓ All category loggers created")
        
        # Test operaciones de logging
        log_operation_start('limpieza', 'Test Operation', 'Testing logging system')
        log_file_operation('limpieza', 'DELETE', 'C:\\test\\file1.txt', True)
        log_file_operation('limpieza', 'DELETE', 'C:\\test\\file2.txt', False, 'File not found')
        
        stats = {'processed': 2, 'successful': 1, 'failed': 1}
        log_operation_end('limpieza', 'Test Operation', True, stats, 1.5)
        
        print("  ✓ Operation logging test completed")
        
        # Test estadísticas de logs
        from core.logger import gestor_logger
        log_stats = gestor_logger.get_log_stats()
        print(f"  ✓ Log Statistics: {len(log_stats)} log files")
        
        return True
        
    except Exception as e:
        print(f"  ✗ Logger Error: {e}")
        return False

def test_integration():
    """Test de integración entre módulos"""
    print("\n🔗 Testing Integration...")
    
    try:
        from core.config_manager import config
        from core.logger import get_logger
        from core.utils import get_system_info
        
        # Logger usando configuración
        logger = get_logger('sistema')
        
        # Log información del sistema
        sys_info = get_system_info()
        logger.info(f"Sistema detectado: {sys_info['system']} {sys_info['release']}")
        
        # Log configuración
        dev_mode = config.is_dev_mode()
        logger.info(f"Modo desarrollo: {dev_mode}")
        
        # Log ubicaciones configuradas
        locations = config.get_enabled_locations()
        logger.info(f"Ubicaciones habilitadas: {len(locations)}")
        
        print("  ✓ Integration test completed")
        return True
        
    except Exception as e:
        print(f"  ✗ Integration Error: {e}")
        return False

def main():
    """Ejecuta todos los tests"""
    print("🚀 SESIÓN 1 - TEST DE MÓDULOS CORE")
    print("=" * 50)
    
    tests = [
        ("Config Manager", test_config_manager),
        ("Utils", test_utils),
        ("Logger System", test_logger),
        ("Integration", test_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  💥 {test_name} CRASHED: {e}")
            results.append((test_name, False))
    
    # Resumen
    print("\n" + "=" * 50)
    print("📊 RESUMEN DE TESTS")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResultado: {passed}/{len(results)} tests pasaron")
    
    if passed == len(results):
        print("\n🎉 ¡SESIÓN 1 COMPLETADA EXITOSAMENTE!")
        print("✅ Todos los módulos core funcionan correctamente")
        print("🔄 Listo para Sesión 2: Sistema de conexiones")
    else:
        print("\n⚠️  Algunos tests fallaron - revisar errores antes de continuar")

if __name__ == "__main__":
    main()
