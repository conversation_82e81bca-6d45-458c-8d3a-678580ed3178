"""
Gestor de Archivos Corporativo - Validación de Duplicados
========================================================

Primera operación completa: Revisa archivos en carpeta origen contra Qnaps
y mueve duplicados a carpeta "rechazados" para mantener origen limpio
"""

import os
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Set
import logging
from datetime import datetime

# Imports del sistema
import sys
sys.path.append(str(Path(__file__).parent.parent))
from core.config_manager import config
from core.logger import get_logger, log_operation_start, log_operation_end, log_file_operation
from core.utils import safe_move_file, get_file_hash, format_file_size, ProgressTracker
from handlers.qnap_handler import QnapHandler

class ValidacionDuplicados:
    """
    Operación de validación y limpieza de archivos duplicados
    
    Funcionalidad:
    1. Escanea carpeta origen
    2. Verifica si archivos existen en Qnaps configurados
    3. Mueve duplicados a carpeta "rechazados"
    4. Mantiene carpeta origen siempre limpia
    """
    
    def __init__(self):
        self.logger = get_logger('validacion')
        self.operation_name = "Validación de Duplicados"
        
        # Cargar configuración de la operación
        self.config = config.get('operations.validacion_duplicados', {})
        
        if not self.config.get('enabled', False):
            raise ValueError("Operación de validación de duplicados está deshabilitada")
        
        # Rutas de operación
        self.carpeta_origen = Path(self.config.get('carpeta_origen', ''))
        self.carpeta_rechazados = Path(self.config.get('carpeta_rechazados', ''))
        self.qnaps_a_revisar = self.config.get('qnaps_a_revisar', [])
        
        # Validar configuración
        self._validate_configuration()
        
        # Inicializar handlers de Qnap
        self.qnap_handlers: Dict[str, QnapHandler] = {}
        self._initialize_qnap_handlers()
        
        # Estadísticas de operación
        self.stats = {
            'archivos_escaneados': 0,
            'archivos_duplicados': 0,
            'archivos_movidos': 0,
            'archivos_fallidos': 0,
            'errores': [],
            'duplicados_por_qnap': {},
            'archivos_no_duplicados': 0,
            'tiempo_inicio': None,
            'tiempo_fin': None
        }
        
        self.logger.info(f"Inicializada operación: {self.operation_name}")
        self.logger.info(f"Carpeta origen: {self.carpeta_origen}")
        self.logger.info(f"Carpeta rechazados: {self.carpeta_rechazados}")
        self.logger.info(f"Qnaps a revisar: {self.qnaps_a_revisar}")
    
    def _validate_configuration(self):
        """Valida la configuración de la operación"""
        errors = []
        
        if not self.carpeta_origen:
            errors.append("carpeta_origen no configurada")
        elif not self.carpeta_origen.exists():
            errors.append(f"carpeta_origen no existe: {self.carpeta_origen}")
        
        if not self.carpeta_rechazados:
            errors.append("carpeta_rechazados no configurada")
        
        if not self.qnaps_a_revisar:
            errors.append("qnaps_a_revisar está vacío")
        
        # Verificar que los Qnaps están habilitados
        locations = config.get_enabled_locations()
        for qnap_id in self.qnaps_a_revisar:
            if qnap_id not in locations:
                errors.append(f"Qnap {qnap_id} no está habilitado o no existe")
        
        if errors:
            error_msg = "Errores de configuración: " + "; ".join(errors)
            self.logger.error(error_msg)
            raise ValueError(error_msg)
    
    def _initialize_qnap_handlers(self):
        """Inicializa handlers para los Qnaps configurados"""
        for qnap_id in self.qnaps_a_revisar:
            try:
                handler = QnapHandler(qnap_id)
                self.qnap_handlers[qnap_id] = handler
                self.stats['duplicados_por_qnap'][qnap_id] = 0
                self.logger.info(f"Handler inicializado para {qnap_id}")
            except Exception as e:
                error_msg = f"Error inicializando handler para {qnap_id}: {e}"
                self.logger.error(error_msg)
                self.stats['errores'].append(error_msg)
    
    def conectar_qnaps(self) -> Dict[str, Tuple[bool, str]]:
        """
        Conecta a todos los Qnaps configurados
        
        Returns:
            Dict[str, Tuple[bool, str]]: Resultado de conexión por Qnap
        """
        conexiones = {}
        
        self.logger.info("Iniciando conexiones a Qnaps...")
        
        for qnap_id, handler in self.qnap_handlers.items():
            try:
                self.logger.info(f"Conectando a {qnap_id}...")
                success, message = handler.connect()
                conexiones[qnap_id] = (success, message)
                
                if success:
                    self.logger.info(f"✓ Conectado a {qnap_id}: {message}")
                else:
                    self.logger.warning(f"✗ Falló conexión a {qnap_id}: {message}")
                    
            except Exception as e:
                error_msg = f"Excepción conectando a {qnap_id}: {e}"
                self.logger.error(error_msg)
                conexiones[qnap_id] = (False, error_msg)
        
        # Resumen de conexiones
        conectados = sum(1 for success, _ in conexiones.values() if success)
        self.logger.info(f"Conexiones completadas: {conectados}/{len(conexiones)} exitosas")
        
        return conexiones
    
    def desconectar_qnaps(self) -> Dict[str, Tuple[bool, str]]:
        """Desconecta todos los Qnaps"""
        desconexiones = {}
        
        for qnap_id, handler in self.qnap_handlers.items():
            try:
                success, message = handler.disconnect()
                desconexiones[qnap_id] = (success, message)
                
                if success:
                    self.logger.info(f"✓ Desconectado de {qnap_id}")
                else:
                    self.logger.warning(f"✗ Error desconectando de {qnap_id}: {message}")
                    
            except Exception as e:
                error_msg = f"Excepción desconectando de {qnap_id}: {e}"
                self.logger.error(error_msg)
                desconexiones[qnap_id] = (False, error_msg)
        
        return desconexiones
    
    def escanear_carpeta_origen(self) -> List[Path]:
        """
        Escanea la carpeta origen y obtiene lista de archivos
        
        Returns:
            List[Path]: Lista de archivos encontrados
        """
        archivos = []
        
        try:
            self.logger.info(f"Escaneando carpeta origen: {self.carpeta_origen}")
            
            # Obtener todos los archivos (no directorios)
            for item in self.carpeta_origen.iterdir():
                if item.is_file():
                    archivos.append(item)
            
            self.logger.info(f"Encontrados {len(archivos)} archivos en carpeta origen")
            
            # Log algunos ejemplos
            for i, archivo in enumerate(archivos[:5]):  # Primeros 5
                self.logger.debug(f"  {i+1}. {archivo.name} ({format_file_size(archivo.stat().st_size)})")
            
            if len(archivos) > 5:
                self.logger.debug(f"  ... y {len(archivos) - 5} archivos más")
                
        except Exception as e:
            error_msg = f"Error escaneando carpeta origen: {e}"
            self.logger.error(error_msg)
            self.stats['errores'].append(error_msg)
        
        return archivos
    
    def verificar_archivo_en_qnap(self, archivo: Path, qnap_id: str) -> Tuple[bool, Optional[Path]]:
        """
        Verifica si un archivo existe en un Qnap específico
        
        Args:
            archivo: Archivo a verificar
            qnap_id: ID del Qnap donde buscar
        
        Returns:
            Tuple[bool, Optional[Path]]: (existe, ruta_en_qnap)
        """
        if qnap_id not in self.qnap_handlers:
            return False, None
        
        handler = self.qnap_handlers[qnap_id]
        
        if not handler.is_connected():
            self.logger.warning(f"Qnap {qnap_id} no está conectado")
            return False, None
        
        try:
            # Buscar archivo por nombre
            existe, ruta_qnap = handler.check_file_exists(archivo.name)
            
            if existe:
                self.logger.debug(f"Archivo {archivo.name} encontrado en {qnap_id}: {ruta_qnap}")
            
            return existe, ruta_qnap
            
        except Exception as e:
            self.logger.debug(f"Error verificando archivo {archivo.name} en {qnap_id}: {e}")
            return False, None
    
    def verificar_duplicados(self, archivo: Path) -> Dict[str, bool]:
        """
        Verifica si un archivo existe en alguno de los Qnaps configurados
        
        Args:
            archivo: Archivo a verificar
        
        Returns:
            Dict[str, bool]: Resultado por Qnap {qnap_id: existe}
        """
        resultados = {}
        
        for qnap_id in self.qnaps_a_revisar:
            existe, _ = self.verificar_archivo_en_qnap(archivo, qnap_id)
            resultados[qnap_id] = existe
            
            if existe:
                self.stats['duplicados_por_qnap'][qnap_id] += 1
        
        return resultados
    
    def mover_a_rechazados(self, archivo: Path) -> Tuple[bool, str]:
        """
        Mueve un archivo duplicado a la carpeta de rechazados
        
        Args:
            archivo: Archivo a mover
        
        Returns:
            Tuple[bool, str]: (éxito, mensaje)
        """
        try:
            # Crear carpeta rechazados si no existe
            self.carpeta_rechazados.mkdir(parents=True, exist_ok=True)
            
            # Ruta destino
            destino = self.carpeta_rechazados / archivo.name
            
            # Si ya existe un archivo con el mismo nombre en rechazados, agregar timestamp
            if destino.exists():
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                nombre_base = archivo.stem
                extension = archivo.suffix
                destino = self.carpeta_rechazados / f"{nombre_base}_{timestamp}{extension}"
            
            # Mover archivo
            success = safe_move_file(archivo, destino, overwrite=False)
            
            if success:
                message = f"Movido a rechazados: {archivo.name} → {destino.name}"
                log_file_operation('validacion', 'MOVE_TO_REJECTED', str(archivo), True)
            else:
                message = f"Error moviendo a rechazados: {archivo.name}"
                log_file_operation('validacion', 'MOVE_TO_REJECTED', str(archivo), False, message)
            
            return success, message
            
        except Exception as e:
            error_msg = f"Excepción moviendo archivo {archivo.name}: {e}"
            log_file_operation('validacion', 'MOVE_TO_REJECTED', str(archivo), False, error_msg)
            return False, error_msg
    
    def procesar_archivo(self, archivo: Path) -> Dict:
        """
        Procesa un archivo individual: verifica duplicados y mueve si es necesario
        
        Args:
            archivo: Archivo a procesar
        
        Returns:
            Dict: Resultado del procesamiento
        """
        resultado = {
            'archivo': archivo.name,
            'es_duplicado': False,
            'qnaps_encontrado': [],
            'movido': False,
            'error': None
        }
        
        try:
            self.stats['archivos_escaneados'] += 1
            
            # Verificar en todos los Qnaps
            duplicados = self.verificar_duplicados(archivo)
            
            # Determinar si es duplicado
            qnaps_con_archivo = [qnap_id for qnap_id, existe in duplicados.items() if existe]
            
            if qnaps_con_archivo:
                # Es un duplicado
                resultado['es_duplicado'] = True
                resultado['qnaps_encontrado'] = qnaps_con_archivo
                self.stats['archivos_duplicados'] += 1
                
                self.logger.info(f"DUPLICADO: {archivo.name} encontrado en {qnaps_con_archivo}")
                
                # Mover a rechazados
                success, message = self.mover_a_rechazados(archivo)
                resultado['movido'] = success
                
                if success:
                    self.stats['archivos_movidos'] += 1
                    self.logger.info(f"✓ {message}")
                else:
                    self.stats['archivos_fallidos'] += 1
                    resultado['error'] = message
                    self.logger.error(f"✗ {message}")
            else:
                # No es duplicado
                self.stats['archivos_no_duplicados'] += 1
                self.logger.debug(f"OK: {archivo.name} no encontrado en Qnaps")
        
        except Exception as e:
            error_msg = f"Error procesando archivo {archivo.name}: {e}"
            self.logger.error(error_msg)
            resultado['error'] = error_msg
            self.stats['archivos_fallidos'] += 1
            self.stats['errores'].append(error_msg)
        
        return resultado
    
    def ejecutar(self, dry_run: bool = False) -> Dict:
        """
        Ejecuta la operación completa de validación de duplicados
        
        Args:
            dry_run: Si True, solo simula sin mover archivos
        
        Returns:
            Dict: Estadísticas completas de la operación
        """
        self.stats['tiempo_inicio'] = time.time()
        
        # Log inicio de operación
        operation_details = f"Origen: {self.carpeta_origen}, Rechazados: {self.carpeta_rechazados}"
        if dry_run:
            operation_details += " (DRY RUN)"
        
        log_operation_start('validacion', self.operation_name, operation_details)
        
        try:
            # 1. Conectar a Qnaps
            self.logger.info("=== FASE 1: CONEXIÓN A QNAPS ===")
            conexiones = self.conectar_qnaps()
            
            # Verificar que al menos un Qnap esté conectado
            qnaps_conectados = [qnap_id for qnap_id, (success, _) in conexiones.items() if success]
            
            if not qnaps_conectados:
                raise Exception("No se pudo conectar a ningún Qnap")
            
            self.logger.info(f"Qnaps conectados para verificación: {qnaps_conectados}")
            
            # 2. Escanear carpeta origen
            self.logger.info("=== FASE 2: ESCANEO DE ARCHIVOS ===")
            archivos = self.escanear_carpeta_origen()
            
            if not archivos:
                self.logger.info("No hay archivos en carpeta origen - operación completada")
                return self.stats
            
            # 3. Procesar archivos
            self.logger.info("=== FASE 3: PROCESAMIENTO DE ARCHIVOS ===")
            progress = ProgressTracker(len(archivos), "Validando duplicados")
            
            resultados_procesamiento = []
            
            for archivo in archivos:
                if dry_run:
                    self.logger.info(f"DRY RUN: Procesaría {archivo.name}")
                    # En dry run, simular procesamiento
                    resultado = {
                        'archivo': archivo.name,
                        'es_duplicado': False,  # Simulado
                        'qnaps_encontrado': [],
                        'movido': False,
                        'error': None
                    }
                else:
                    resultado = self.procesar_archivo(archivo)
                
                resultados_procesamiento.append(resultado)
                progress.update()
            
            # 4. Resumen de resultados
            self.logger.info("=== FASE 4: RESUMEN DE RESULTADOS ===")
            self._log_summary()
            
        except Exception as e:
            error_msg = f"Error en ejecución de operación: {e}"
            self.logger.error(error_msg)
            self.stats['errores'].append(error_msg)
        
        finally:
            # 5. Desconectar Qnaps
            self.logger.info("=== FASE 5: DESCONEXIÓN ===")
            self.desconectar_qnaps()
            
            # Finalizar estadísticas
            self.stats['tiempo_fin'] = time.time()
            duration = self.stats['tiempo_fin'] - self.stats['tiempo_inicio']
            
            # Log final de operación
            operation_success = self.stats['archivos_fallidos'] == 0 and len(self.stats['errores']) == 0
            log_operation_end('validacion', self.operation_name, operation_success, self.stats, duration)
        
        return self.stats
    
    def _log_summary(self):
        """Log resumen detallado de la operación"""
        self.logger.info("📊 ESTADÍSTICAS DE VALIDACIÓN:")
        self.logger.info(f"  📄 Archivos escaneados: {self.stats['archivos_escaneados']}")
        self.logger.info(f"  🔍 Archivos duplicados: {self.stats['archivos_duplicados']}")
        self.logger.info(f"  ✅ Archivos no duplicados: {self.stats['archivos_no_duplicados']}")
        self.logger.info(f"  📦 Archivos movidos a rechazados: {self.stats['archivos_movidos']}")
        self.logger.info(f"  ❌ Archivos fallidos: {self.stats['archivos_fallidos']}")
        
        # Duplicados por Qnap
        self.logger.info("  📀 Duplicados por Qnap:")
        for qnap_id, count in self.stats['duplicados_por_qnap'].items():
            self.logger.info(f"    - {qnap_id}: {count}")
        
        # Errores
        if self.stats['errores']:
            self.logger.info(f"  🚨 Errores ({len(self.stats['errores'])}):")
            for error in self.stats['errores'][:5]:  # Primeros 5 errores
                self.logger.info(f"    - {error}")
    
    def get_stats(self) -> Dict:
        """Obtiene estadísticas actuales de la operación"""
        return self.stats.copy()

if __name__ == "__main__":
    # Test básico de la operación
    print("🔍 Validación de Duplicados - Test")
    print("=" * 40)
    
    # Configurar logging para test
    logging.basicConfig(level=logging.INFO)
    
    try:
        # Crear operación
        validador = ValidacionDuplicados()
        print(f"✓ Operación inicializada")
        print(f"  Carpeta origen: {validador.carpeta_origen}")
        print(f"  Carpeta rechazados: {validador.carpeta_rechazados}")
        print(f"  Qnaps a revisar: {validador.qnaps_a_revisar}")
        
        # Ejecutar en modo dry run
        print(f"\n🧪 Ejecutando en modo DRY RUN...")
        stats = validador.ejecutar(dry_run=True)
        
        print(f"\n📊 Estadísticas:")
        print(f"  Archivos escaneados: {stats['archivos_escaneados']}")
        print(f"  Archivos duplicados: {stats['archivos_duplicados']}")
        print(f"  Errores: {len(stats['errores'])}")
        
        if stats['tiempo_inicio'] and stats['tiempo_fin']:
            duracion = stats['tiempo_fin'] - stats['tiempo_inicio']
            print(f"  Duración: {duracion:.2f} segundos")
        
        print("✅ Test completado")
        
    except Exception as e:
        print(f"❌ Error en test: {e}")
        import traceback
        traceback.print_exc()
