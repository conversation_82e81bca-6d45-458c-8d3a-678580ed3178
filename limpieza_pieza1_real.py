"""
GUI Limpieza por Lista - Pieza 1: Base + Header + Configuración Real
================================================================
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
from datetime import datetime
import threading
import time
from pathlib import Path

# Agregar el directorio actual al path para imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Imports del sistema
try:
    from gestor_license_manager import GestorLicenseManager, show_license_dialog
    from src.core.config_manager import ConfigManager
    from src.core.logger import LoggerManager
    from src.handlers.qnap_handler import QnapHandler
    from src.handlers.pc_handler import PCHandler
    from location_config_widget import LocationConfigWidget
except ImportError as e:
    print(f"Warning: Some modules not found: {e}")
    print("Running in standalone mode...")

class CleanupByListGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Gestor de Archivos - Limpieza por Lista")
        self.root.geometry("1100x850")
        self.root.resizable(True, True)
        
        # Configuración inicial
        self.licensed = False
        self.running = False
        
        # Variables de configuración
        self.archivo_lista = tk.StringVar()
        self.dry_run = tk.BooleanVar(value=True)
        
        # Config manager y handlers
        try:
            self.config = ConfigManager()
            self.logger = LoggerManager("limpieza_gui").get_logger()
        except:
            import logging
            logging.basicConfig(level=logging.INFO)
            self.logger = logging.getLogger("limpieza_gui")
            self.config = None
        
        # Variables de estado para ubicaciones
        self.location_status = {}
        self.handlers = {}
        
        # Configurar logging
        self.setup_ui()
        self.check_license()
        self.load_default_config()
    
    def setup_header(self, parent):
        """Configurar header con logo IGSON"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill='x', pady=(0,10))
        
        # Intentar cargar logo IGSON
        logo_loaded = False
        try:
            from PIL import Image, ImageTk
            logo_path = os.path.join(current_dir, "logo_igson.png")
            if os.path.exists(logo_path):
                img = Image.open(logo_path)
                img = img.resize((80, 80), Image.Resampling.LANCZOS)
                self.logo_photo = ImageTk.PhotoImage(img)
                
                logo_label = ttk.Label(header_frame, image=self.logo_photo)
                logo_label.pack(side='left', padx=(0,15))
                logo_loaded = True
        except Exception as e:
            self.logger.debug(f"Error cargando logo: {e}")
        
        if not logo_loaded:
            ttk.Label(header_frame, text="🏢", font=("Arial", 40)).pack(side='left', padx=(0,15))
        
        # Información de la aplicación
        info_frame = ttk.Frame(header_frame)
        info_frame.pack(side='left', fill='both', expand=True)
        
        ttk.Label(info_frame, text="IGSON - Gestor de Archivos Corporativo", 
                 font=("Arial", 16, "bold")).pack(anchor='w')
        ttk.Label(info_frame, text="Limpieza por Lista con Configuración Real", 
                 font=("Arial", 12)).pack(anchor='w')
        ttk.Label(info_frame, text="Versión 2.0 - Junio 2025", 
                 font=("Arial", 9), foreground="gray").pack(anchor='w')
    
    def setup_ui(self):
        """Configurar interfaz de usuario"""
        # Notebook principal
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Tab 1: Operación principal
        self.operation_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.operation_frame, text="🗑️ Limpieza por Lista")
        self.setup_operation_tab()
        
        # Tab 2: Configuración de ubicaciones
        self.config_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.config_frame, text="⚙️ Configuración")
        self.setup_config_tab()
        
        # Tab 3: Logs del sistema
        self.logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.logs_frame, text="📋 Logs")
        self.setup_logs_tab()
    
    def setup_operation_tab(self):
        """Configurar tab de operación principal"""
        # Frame principal con padding
        main_frame = ttk.Frame(self.operation_frame, padding="10")
        main_frame.pack(fill='both', expand=True)
        
        # Header con logo
        self.setup_header(main_frame)
        
        # Frame de configuración de archivo
        file_config_frame = ttk.LabelFrame(main_frame, text="Configuración de Lista", padding="10")
        file_config_frame.pack(fill='x', pady=(10,0))
        
        # Selector de archivo de lista
        ttk.Label(file_config_frame, text="Archivo de Lista:").grid(row=0, column=0, sticky='w', pady=5)
        ttk.Entry(file_config_frame, textvariable=self.archivo_lista, width=60).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(file_config_frame, text="Examinar", 
                  command=self.select_archivo_lista).grid(row=0, column=2, pady=5)
        
        # Botón para crear archivo de prueba
        ttk.Button(file_config_frame, text="📝 Crear Archivo de Prueba", 
                  command=self.create_test_list).grid(row=1, column=0, sticky='w', pady=(10,0))
        
        # Modo dry run
        ttk.Checkbutton(file_config_frame, text="Modo Simulación (Dry Run) - Altamente Recomendado", 
                       variable=self.dry_run).grid(row=1, column=1, columnspan=2, sticky='w', pady=(10,0))
        
        # Frame de estado de ubicaciones
        self.setup_location_status_frame(main_frame)
        
        # Frame de previsualización
        self.setup_preview_frame(main_frame)
        
        # Frame de control
        self.setup_control_frame(main_frame)
        
        # Frame de resultados
        self.setup_results_frame(main_frame)
    
    def setup_location_status_frame(self, parent):
        """Configurar frame de estado de ubicaciones"""
        status_frame = ttk.LabelFrame(parent, text="Estado de Ubicaciones (6 ubicaciones)", padding="10")
        status_frame.pack(fill='x', pady=(10,0))
        
        # Grid para mostrar estado de todas las ubicaciones
        locations = ['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4']
        
        # Headers
        ttk.Label(status_frame, text="Ubicación", font=("Arial", 10, "bold")).grid(row=0, column=0, padx=5, pady=2)
        ttk.Label(status_frame, text="Estado", font=("Arial", 10, "bold")).grid(row=0, column=1, padx=20, pady=2)
        ttk.Label(status_frame, text="Tipo", font=("Arial", 10, "bold")).grid(row=0, column=2, padx=20, pady=2)
        ttk.Label(status_frame, text="Archivos", font=("Arial", 10, "bold")).grid(row=0, column=3, padx=20, pady=2)
        
        # Separador
        ttk.Separator(status_frame, orient='horizontal').grid(row=1, column=0, columnspan=4, sticky='ew', pady=5)
        
        # Estado de cada ubicación
        self.location_labels = {}
        for i, location_id in enumerate(locations):
            row = i + 2
            
            # Nombre de ubicación
            ttk.Label(status_frame, text=location_id.upper(), font=("Arial", 9, "bold")).grid(row=row, column=0, sticky='w', padx=5, pady=2)
            
            # Estado de conexión
            status_label = ttk.Label(status_frame, text="Sin configurar", foreground="gray")
            status_label.grid(row=row, column=1, sticky='w', padx=20, pady=2)
            
            # Tipo de dispositivo
            device_type = "QNAP" if location_id.startswith('qnap') else "PC"
            ttk.Label(status_frame, text=device_type, foreground="blue").grid(row=row, column=2, sticky='w', padx=20, pady=2)
            
            # Contador de archivos encontrados
            files_label = ttk.Label(status_frame, text="0", foreground="gray")
            files_label.grid(row=row, column=3, sticky='w', padx=20, pady=2)
            
            self.location_labels[location_id] = {
                'status': status_label,
                'files': files_label
            }
        
        # Botones de control de estado
        button_frame = ttk.Frame(status_frame)
        button_frame.grid(row=8, column=0, columnspan=4, pady=(10,0))
        
        ttk.Button(button_frame, text="🔄 Actualizar Estado", 
                  command=self.update_all_locations_status).pack(side='left', padx=(0,10))
        ttk.Button(button_frame, text="🧪 Test Todas las Conexiones", 
                  command=self.test_all_connections).pack(side='left')
    
    def setup_config_tab(self):
        """Configurar tab de configuración"""
        # Widget de configuración de ubicaciones
        self.location_widget = LocationConfigWidget(self.config_frame, self.config, self.logger)
        
        # Override del método log_message para integrar con nuestros logs
        self.location_widget.log_message = self.log_message
    
    def setup_logs_tab(self):
        """Configurar tab de logs"""
        logs_main = ttk.Frame(self.logs_frame, padding="10")
        logs_main.pack(fill='both', expand=True)
        
        # Frame de control de logs
        log_control = ttk.Frame(logs_main)
        log_control.pack(fill='x', pady=(0,10))
        
        ttk.Label(log_control, text="Logs del Sistema:", font=("Arial", 12, "bold")).pack(side='left')
        ttk.Button(log_control, text="🗑️ Limpiar", command=self.clear_logs).pack(side='right')
        ttk.Button(log_control, text="💾 Exportar", command=self.export_logs).pack(side='right', padx=(0,10))
        ttk.Button(log_control, text="🔄 Actualizar", command=self.load_existing_logs).pack(side='right', padx=(0,10))
        
        # Área de logs del sistema
        self.system_log_text = scrolledtext.ScrolledText(logs_main, height=30, width=120)
        self.system_log_text.pack(fill='both', expand=True)
        
        # Cargar logs existentes
        self.load_existing_logs()
    
    def select_archivo_lista(self):
        """Seleccionar archivo de lista"""
        filename = filedialog.askopenfilename(
            title="Seleccionar Archivo de Lista",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            self.archivo_lista.set(filename)
            # Previsualizar automáticamente
            self.preview_list_content()
    
    def create_test_list(self):
        """Crear archivo de lista de prueba"""
        try:
            # Crear carpeta de prueba
            test_dir = os.path.join(current_dir, "test_limpieza")
            os.makedirs(test_dir, exist_ok=True)
            
            # Archivo de lista de prueba
            list_file = os.path.join(test_dir, "lista_prueba.txt")
            
            # Contenido de prueba
            test_files = [
                "documento_test1.pdf",
                "imagen_test1.jpg", 
                "video_test1.mp4",
                "archivo_test1.docx",
                "texto_test1.txt"
            ]
            
            # Escribir archivo
            with open(list_file, 'w', encoding='utf-8') as f:
                for file in test_files:
                    f.write(f"{file}\n")
            
            # Crear archivos de prueba en ubicaciones de test
            self.create_test_files_in_locations(test_files)
            
            # Configurar en la GUI
            self.archivo_lista.set(list_file)
            self.log_message(f"✅ Archivo de prueba creado: {list_file}")
            self.log_message(f"📝 {len(test_files)} archivos en la lista")
            
            # Previsualizar
            self.preview_list_content()
            
        except Exception as e:
            self.log_message(f"❌ Error creando archivo de prueba: {e}")
            messagebox.showerror("Error", f"Error creando archivo de prueba: {e}")
    
    def create_test_files_in_locations(self, file_list):
        """Crear archivos de prueba en ubicaciones de test"""
        try:
            test_base = os.path.join(current_dir, "test_limpieza", "ubicaciones")
            
            # Ubicaciones de prueba
            locations = {
                'qnap1': os.path.join(test_base, "qnap1"),
                'qnap2': os.path.join(test_base, "qnap2"),
                'pc1': os.path.join(test_base, "pc1"),
                'pc2': os.path.join(test_base, "pc2"),
                'pc3': os.path.join(test_base, "pc3"),
                'pc4': os.path.join(test_base, "pc4")
            }
            
            # Crear directorios
            for location, path in locations.items():
                os.makedirs(path, exist_ok=True)
            
            # Crear archivos distribuidos (simulando archivos reales)
            for i, filename in enumerate(file_list):
                # Cada archivo aparece en 2-3 ubicaciones (simulando duplicados)
                location_keys = list(locations.keys())
                
                # Archivo aparece en QNAP1 siempre
                file_path = os.path.join(locations['qnap1'], filename)
                with open(file_path, 'w') as f:
                    f.write(f"Contenido de {filename} en QNAP1 - {datetime.now()}")
                
                # Y en 1-2 ubicaciones más
                if i % 2 == 0:  # Archivos pares también en QNAP2
                    file_path = os.path.join(locations['qnap2'], filename)
                    with open(file_path, 'w') as f:
                        f.write(f"Contenido de {filename} en QNAP2 - {datetime.now()}")
                
                if i % 3 == 0:  # Cada tercer archivo también en PC1
                    file_path = os.path.join(locations['pc1'], filename)
                    with open(file_path, 'w') as f:
                        f.write(f"Contenido de {filename} en PC1 - {datetime.now()}")
            
            self.log_message(f"📂 Archivos de prueba creados en {len(locations)} ubicaciones")
            
        except Exception as e:
            self.log_message(f"❌ Error creando archivos de prueba: {e}")
    
    def load_default_config(self):
        """Cargar configuración por defecto"""
        # Configurar archivo de lista por defecto
        test_list = os.path.join(current_dir, "test_limpieza", "lista_prueba.txt")
        if os.path.exists(test_list):
            self.archivo_lista.set(test_list)
        
        # Actualizar estado inicial
        self.update_all_locations_status()
    
    def log_message(self, message):
        """Agregar mensaje al log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        # Log en tab de operación (si existe)
        if hasattr(self, 'log_text'):
            self.log_text.insert(tk.END, formatted_message)
            self.log_text.see(tk.END)
        
        # Log en tab de logs del sistema
        if hasattr(self, 'system_log_text'):
            self.system_log_text.insert(tk.END, formatted_message)
            self.system_log_text.see(tk.END)
        
        self.root.update_idletasks()
        
        # Log al sistema
        if self.logger:
            self.logger.info(message)
    
    def update_all_locations_status(self):
        """Actualizar estado de todas las ubicaciones"""
        def update_thread():
            try:
                self.log_message("🔄 Actualizando estado de todas las ubicaciones...")
                
                # Obtener ubicaciones habilitadas desde el widget de configuración
                enabled_locations = self.location_widget.get_enabled_locations()
                
                # Actualizar cada ubicación
                for location_id in ['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4']:
                    if location_id in enabled_locations:
                        config = enabled_locations[location_id]
                        
                        if location_id.startswith('qnap'):
                            # QNAP: verificar path
                            path = config['path']
                            if path and Path(path).exists():
                                self.location_labels[location_id]['status'].config(text="✅ Conectado", foreground="green")
                                self.log_message(f"✅ {location_id.upper()}: Conectado")
                            else:
                                self.location_labels[location_id]['status'].config(text="❌ No accesible", foreground="red")
                                self.log_message(f"❌ {location_id.upper()}: No accesible")
                        else:
                            # PC: verificar configuración SMB
                            hostname = config.get('hostname', '')
                            username = config.get('username', '')
                            share = config.get('share', '')
                            
                            if hostname and username and share:
                                self.location_labels[location_id]['status'].config(text="✅ Configurado", foreground="green")
                                self.log_message(f"✅ {location_id.upper()}: Configurado")
                            else:
                                self.location_labels[location_id]['status'].config(text="⚠️ Incompleto", foreground="orange")
                                self.log_message(f"⚠️ {location_id.upper()}: Configuración incompleta")
                    else:
                        self.location_labels[location_id]['status'].config(text="⚪ Deshabilitado", foreground="gray")
                
                self.log_message("✅ Estado de ubicaciones actualizado")
                
            except Exception as e:
                self.log_message(f"❌ Error actualizando estado: {e}")
        
        thread = threading.Thread(target=update_thread)
        thread.daemon = True
        thread.start()
    
    def test_all_connections(self):
        """Test de todas las conexiones"""
        def test_thread():
            try:
                self.log_message("🧪 Iniciando test de todas las conexiones...")
                
                enabled_locations = self.location_widget.get_enabled_locations()
                total_tests = len(enabled_locations)
                passed_tests = 0
                
                for location_id, config in enabled_locations.items():
                    self.log_message(f"🧪 Testing {location_id.upper()}...")
                    
                    try:
                        if location_id.startswith('qnap'):
                            # Test QNAP
                            path = config['path']
                            if config['subfolder']:
                                full_path = os.path.join(path, config['subfolder'])
                            else:
                                full_path = path
                            
                            if Path(full_path).exists():
                                # Contar archivos de test si los hay
                                try:
                                    files_count = len([f for f in os.listdir(full_path) if os.path.isfile(os.path.join(full_path, f))])
                                    self.location_labels[location_id]['files'].config(text=str(files_count), foreground="blue")
                                    passed_tests += 1
                                    self.log_message(f"✅ {location_id.upper()}: {files_count} archivos encontrados")
                                except:
                                    self.location_labels[location_id]['files'].config(text="?", foreground="gray")
                                    self.log_message(f"⚠️ {location_id.upper()}: Accesible pero no se puede contar archivos")
                            else:
                                self.log_message(f"❌ {location_id.upper()}: Ruta no accesible")
                        
                        else:
                            # Test PC (simulado)
                            hostname = config.get('hostname', '')
                            if hostname:
                                # Simular test SMB
                                time.sleep(0.5)  # Simular latencia
                                passed_tests += 1
                                self.location_labels[location_id]['files'].config(text="?", foreground="orange")
                                self.log_message(f"✅ {location_id.upper()}: Test SMB simulado exitoso")
                            else:
                                self.log_message(f"❌ {location_id.upper()}: Sin hostname configurado")
                        
                        time.sleep(0.3)  # Pausa entre tests
                        
                    except Exception as e:
                        self.log_message(f"❌ Error testing {location_id}: {e}")
                
                # Resumen de tests
                self.log_message("=" * 40)
                self.log_message(f"🧪 TEST COMPLETADO: {passed_tests}/{total_tests} ubicaciones OK")
                self.log_message("=" * 40)
                
            except Exception as e:
                self.log_message(f"❌ Error en test masivo: {e}")
        
        thread = threading.Thread(target=test_thread)
        thread.daemon = True
        thread.start()
    
    # =============================
    # FUNCIONES AUXILIARES FASE 2
    # =============================
    
    def is_valid_video_basename(self, basename):
        """Validar que el nombre base sea válido para archivos de video
        
        FASE 2: Solo acepta nombres base sin extensión
        Ejemplos válidos: 20826, 48820, archivo_video1, etc.
        Ejemplos inválidos: archivo.mxf, video.mov, etc.
        """
        if not basename or len(basename.strip()) == 0:
            return False
        
        basename = basename.strip()
        
        # No debe tener extensión (no debe contener puntos)
        if '.' in basename:
            return False
        
        # Caracteres no permitidos en Windows
        invalid_chars = ['<', '>', ':', '"', '|', '?', '*', '/', '\\']
        for char in invalid_chars:
            if char in basename:
                return False
        
        # Nombres reservados en Windows
        reserved_names = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 
                         'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 
                         'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9']
        
        if basename.upper() in reserved_names:
            return False
        
        # Debe tener al menos un caracter alfanumérico
        if not any(c.isalnum() for c in basename):
            return False
        
        return True
    
    def get_target_files_for_basename(self, basename):
        """Obtener lista de archivos objetivo para un nombre base
        
        FASE 2: Cada nombre base genera archivos .mxf y .mov
        Ejemplo: '20826' → ['20826.mxf', '20826.mov']
        """
        if not basename or not self.is_valid_video_basename(basename):
            return []
        
        return [f"{basename}.mxf", f"{basename}.mov"]
