"""
dashboard_pieza3d.py
PIEZA 3D: Métodos de soporte para Limpieza
Gestor de Archivos Corporativo
"""

# CONTINUACIÓN DE LA CLASE GestorDashboard - MÉTODOS DE SOPORTE LIMPIEZA

    def select_limpieza_file(self):
        """Seleccionar archivo de lista para limpieza"""
        file_path = filedialog.askopenfilename(
            title="Seleccionar Archivo de Lista",
            filetypes=[("Archivos de texto", "*.txt"), ("Todos los archivos", "*.*")]
        )
        if file_path:
            self.limpieza_archivo_var.set(file_path)
            self.update_file_info(file_path)
            self.update_status(f"Archivo de lista seleccionado: {Path(file_path).name}", "success")
            self.validate_limpieza_config()
    
    def update_file_info(self, file_path: str):
        """Actualizar información del archivo de lista"""
        try:
            path = Path(file_path)
            if path.exists():
                size_kb = path.stat().st_size / 1024
                
                # Contar líneas en thread separado para archivos grandes
                def count_lines():
                    try:
                        with open(path, 'r', encoding='utf-8', errors='ignore') as f:
                            lines = len(f.readlines())
                        
                        info_text = f"📄 {path.name} | {size_kb:.1f} KB | {lines} líneas"
                        self.root.after(0, lambda: self.file_info_label.config(text=info_text, foreground='#2E7D32'))
                    except Exception as e:
                        error_text = f"❌ Error: {str(e)[:50]}..."
                        self.root.after(0, lambda: self.file_info_label.config(text=error_text, foreground='#C62828'))
                
                if size_kb > 100:  # Archivo grande, usar thread
                    self.file_info_label.config(text="🔄 Analizando archivo...", foreground='#1976D2')
                    threading.Thread(target=count_lines, daemon=True).start()
                else:
                    count_lines()  # Archivo pequeño, ejecutar directamente
            else:
                self.file_info_label.config(text="❌ Archivo no encontrado", foreground='#C62828')
        except Exception as e:
            self.file_info_label.config(text=f"❌ Error: {str(e)[:50]}...", foreground='#C62828')
    
    def edit_list_file(self):
        """Editar archivo de lista en ventana integrada"""
        file_path = self.limpieza_archivo_var.get().strip()
        
        edit_window = tk.Toplevel(self.root)
        edit_window.title(f"Editor - {Path(file_path).name if file_path else 'Nuevo archivo'}")
        edit_window.geometry("700x600")
        edit_window.transient(self.root)
        edit_window.grab_set()
        
        # Header con información
        header_frame = ttk.Frame(edit_window)
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(header_frame, text="✏️ Editor de Lista de Archivos", 
                 style='Header.TLabel').pack(side=tk.LEFT)
        
        # Frame de herramientas
        tools_frame = ttk.LabelFrame(edit_window, text="🛠️ Herramientas", padding="5")
        tools_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # Botones de herramientas
        ttk.Button(tools_frame, text="📋 Pegar desde Clipboard", 
                  command=lambda: self.paste_from_clipboard(edit_text)).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(tools_frame, text="🧹 Limpiar Líneas Vacías", 
                  command=lambda: self.clean_empty_lines(edit_text)).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(tools_frame, text="🔤 Remover Duplicados", 
                  command=lambda: self.remove_duplicates(edit_text)).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(tools_frame, text="📊 Validar", 
                  command=lambda: self.validate_list_content(edit_text)).pack(side=tk.RIGHT, padx=5)
        
        # Área de edición con números de línea
        edit_frame = ttk.Frame(edit_window)
        edit_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        # Frame para texto y line numbers
        text_frame = ttk.Frame(edit_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        # Line numbers
        line_numbers = tk.Text(text_frame, width=4, padx=3, takefocus=0,
                              border=0, state='disabled', wrap='none', font=('Consolas', 10))
        line_numbers.pack(side=tk.LEFT, fill=tk.Y)
        
        # Main text editor
        edit_text = tk.Text(text_frame, wrap=tk.NONE, font=('Consolas', 10), undo=True)
        edit_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Scrollbars
        edit_scroll_v = ttk.Scrollbar(edit_frame, orient=tk.VERTICAL)
        edit_scroll_v.pack(side=tk.RIGHT, fill=tk.Y)
        
        edit_scroll_h = ttk.Scrollbar(edit_frame, orient=tk.HORIZONTAL)
        edit_scroll_h.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Configurar scrollbars
        edit_text.configure(yscrollcommand=edit_scroll_v.set, xscrollcommand=edit_scroll_h.set)
        edit_scroll_v.configure(command=self.sync_scroll_y(edit_text, line_numbers))
        edit_scroll_h.configure(command=edit_text.xview)
        
        # Sincronizar line numbers
        def update_line_numbers(*args):
            line_numbers.config(state='normal')
            line_numbers.delete(1.0, tk.END)
            
            content = edit_text.get(1.0, tk.END)
            lines = content.split('\n')
            
            for i in range(len(lines)):
                line_numbers.insert(tk.END, f"{i+1:3d}\n")
            
            line_numbers.config(state='disabled')
        
        edit_text.bind('<KeyRelease>', update_line_numbers)
        edit_text.bind('<Button-1>', update_line_numbers)
        
        # Cargar contenido si existe
        try:
            if file_path and Path(file_path).exists():
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    edit_text.insert(1.0, content)
            else:
                edit_text.insert(1.0, "# Lista de archivos a eliminar\n# Un archivo por línea\n\n")
            
            update_line_numbers()
        except Exception as e:
            edit_text.insert(1.0, f"# Error cargando archivo: {e}\n\n")
        
        # Status bar
        status_frame = ttk.Frame(edit_window)
        status_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        status_label = ttk.Label(status_frame, text="Listo para editar", style='Small.TLabel')
        status_label.pack(side=tk.LEFT)
        
        # Botones principales
        buttons_frame = ttk.Frame(edit_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        def save_file():
            try:
                content = edit_text.get(1.0, tk.END)
                save_path = file_path
                
                if not save_path:
                    save_path = filedialog.asksaveasfilename(
                        title="Guardar archivo de lista",
                        defaultextension=".txt",
                        filetypes=[("Archivos de texto", "*.txt")]
                    )
                
                if save_path:
                    with open(save_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    self.limpieza_archivo_var.set(save_path)
                    self.update_file_info(save_path)
                    messagebox.showinfo("Éxito", "Archivo guardado correctamente")
                    edit_window.destroy()
            except Exception as e:
                messagebox.showerror("Error", f"Error guardando archivo: {e}")
        
        def save_as_file():
            try:
                content = edit_text.get(1.0, tk.END)
                save_path = filedialog.asksaveasfilename(
                    title="Guardar como archivo de lista",
                    defaultextension=".txt",
                    filetypes=[("Archivos de texto", "*.txt")]
                )
                
                if save_path:
                    with open(save_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    self.limpieza_archivo_var.set(save_path)
                    self.update_file_info(save_path)
                    messagebox.showinfo("Éxito", "Archivo guardado correctamente")
                    edit_window.destroy()
            except Exception as e:
                messagebox.showerror("Error", f"Error guardando archivo: {e}")
        
        ttk.Button(buttons_frame, text="💾 Guardar", command=save_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="💾 Guardar Como", command=save_as_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="❌ Cancelar", command=edit_window.destroy).pack(side=tk.RIGHT, padx=5)
    
    def sync_scroll_y(self, text_widget, line_widget):
        """Sincronizar scroll vertical entre widgets"""
        def scroll_command(*args):
            text_widget.yview(*args)
            line_widget.yview(*args)
        return scroll_command
    
    def paste_from_clipboard(self, text_widget):
        """Pegar contenido del clipboard"""
        try:
            clipboard_content = self.root.clipboard_get()
            text_widget.insert(tk.INSERT, clipboard_content)
        except Exception:
            messagebox.showwarning("Clipboard", "No hay contenido válido en el clipboard")
    
    def clean_empty_lines(self, text_widget):
        """Limpiar líneas vacías del editor"""
        content = text_widget.get(1.0, tk.END)
        lines = content.split('\n')
        clean_lines = [line.strip() for line in lines if line.strip()]
        
        text_widget.delete(1.0, tk.END)
        text_widget.insert(1.0, '\n'.join(clean_lines))
    
    def remove_duplicates(self, text_widget):
        """Remover líneas duplicadas"""
        content = text_widget.get(1.0, tk.END)
        lines = content.split('\n')
        unique_lines = []
        seen = set()
        
        for line in lines:
            clean_line = line.strip()
            if clean_line and clean_line not in seen:
                unique_lines.append(clean_line)
                seen.add(clean_line)
        
        text_widget.delete(1.0, tk.END)
        text_widget.insert(1.0, '\n'.join(unique_lines))
    
    def validate_list_content(self, text_widget):
        """Validar contenido de la lista"""
        content = text_widget.get(1.0, tk.END)
        lines = content.split('\n')
        
        valid_count = 0
        invalid_count = 0
        empty_count = 0
        
        for line in lines:
            clean_line = line.strip()
            if not clean_line or clean_line.startswith('#'):
                empty_count += 1
            elif self.is_valid_filename(clean_line):
                valid_count += 1
            else:
                invalid_count += 1
        
        message = f"Validación de lista:\n\n"
        message += f"✅ Archivos válidos: {valid_count}\n"
        message += f"❌ Nombres inválidos: {invalid_count}\n"
        message += f"⚪ Líneas vacías/comentarios: {empty_count}\n"
        
        if invalid_count > 0:
            message += f"\n⚠️ Hay {invalid_count} nombres de archivo inválidos"
        
        messagebox.showinfo("Validación", message)
    
    def is_valid_filename(self, filename: str) -> bool:
        """Verificar si es un nombre de archivo válido"""
        import re
        
        # Caracteres no permitidos en Windows
        invalid_chars = r'[<>:"/\\|?*]'
        if re.search(invalid_chars, filename):
            return False
        
        # Nombres reservados en Windows
        reserved_names = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 
                         'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 
                         'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9']
        
        name_without_ext = filename.split('.')[0].upper()
        if name_without_ext in reserved_names:
            return False
        
        return True
    
    def analyze_limpieza(self):
        """Analizar archivo de lista antes de ejecutar limpieza"""
        archivo = self.limpieza_archivo_var.get().strip()
        if not archivo or not Path(archivo).exists():
            messagebox.showwarning("Configuración Incompleta", "Seleccione un archivo de lista válido")
            return
        
        analyze_window = tk.Toplevel(self.root)
        analyze_window.title("Análisis - Limpieza por Lista")
        analyze_window.geometry("900x700")
        analyze_window.transient(self.root)
        
        # Header
        header_frame = ttk.Frame(analyze_window)
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(header_frame, text="🔍 Análisis de Limpieza", 
                 style='Title.TLabel').pack(side=tk.LEFT)
        
        # Notebook para diferentes análisis
        analyze_notebook = ttk.Notebook(analyze_window)
        analyze_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Tab 1: Resumen general
        summary_frame = ttk.Frame(analyze_notebook)
        analyze_notebook.add(summary_frame, text="📊 Resumen")
        
        summary_text = tk.Text(summary_frame, wrap=tk.WORD, font=('Consolas', 9))
        summary_scroll = ttk.Scrollbar(summary_frame, orient=tk.VERTICAL, command=summary_text.yview)
        summary_text.configure(yscrollcommand=summary_scroll.set)
        
        summary_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        summary_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Tab 2: Archivos válidos
        valid_frame = ttk.Frame(analyze_notebook)
        analyze_notebook.add(valid_frame, text="✅ Válidos")
        
        valid_text = tk.Text(valid_frame, wrap=tk.WORD, font=('Consolas', 9))
        valid_scroll = ttk.Scrollbar(valid_frame, orient=tk.VERTICAL, command=valid_text.yview)
        valid_text.configure(yscrollcommand=valid_scroll.set)
        
        valid_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        valid_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Tab 3: Archivos inválidos
        invalid_frame = ttk.Frame(analyze_notebook)
        analyze_notebook.add(invalid_frame, text="❌ Inválidos")
        
        invalid_text = tk.Text(invalid_frame, wrap=tk.WORD, font=('Consolas', 9))
        invalid_scroll = ttk.Scrollbar(invalid_frame, orient=tk.VERTICAL, command=invalid_text.yview)
        invalid_text.configure(yscrollcommand=invalid_scroll.set)
        
        invalid_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        invalid_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Botones
        buttons_frame = ttk.Frame(analyze_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(buttons_frame, text="🔄 Actualizar", 
                  command=lambda: self.refresh_limpieza_analysis(archivo, summary_text, valid_text, invalid_text)).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(buttons_frame, text="Cerrar", 
                  command=analyze_window.destroy).pack(side=tk.RIGHT, padx=5)
        
        # Ejecutar análisis inicial
        self.refresh_limpieza_analysis(archivo, summary_text, valid_text, invalid_text)
    
    def refresh_limpieza_analysis(self, archivo, summary_text, valid_text, invalid_text):
        """Actualizar análisis de limpieza"""
        # Limpiar widgets
        for widget in [summary_text, valid_text, invalid_text]:
            widget.delete(1.0, tk.END)
            widget.insert(1.0, "🔄 Analizando...\n")
            widget.update()
        
        def analyze_worker():
            try:
                from core.utils import read_text_file, validate_file_list
                
                # Leer archivo
                lines = read_text_file(Path(archivo))
                if lines is None:
                    for widget in [summary_text, valid_text, invalid_text]:
                        self.root.after(0, lambda w=widget: w.delete(1.0, tk.END))
                        self.root.after(0, lambda w=widget: w.insert(1.0, "❌ Error leyendo archivo\n"))
                    return
                
                # Validar
                validation = validate_file_list(lines)
                
                # Ubicaciones seleccionadas
                selected_locations = [loc for loc, var in self.limpieza_locations.items() if var.get()]
                
                # Generar resumen
                summary = []
                summary.append(f"🔍 ANÁLISIS DE LIMPIEZA\n")
                summary.append(f"=" * 50 + "\n\n")
                summary.append(f"📄 Archivo: {archivo}\n")
                summary.append(f"📊 Total líneas: {len(lines)}\n")
                summary.append(f"✅ Archivos válidos: {len(validation['valid'])}\n")
                summary.append(f"❌ Nombres inválidos: {len(validation['invalid_names'])}\n")
                summary.append(f"⚪ Líneas vacías: {len(validation['empty_lines'])}\n\n")
                
                summary.append(f"🌐 UBICACIONES SELECCIONADAS ({len(selected_locations)}):\n")
                for loc in selected_locations:
                    summary.append(f"  • {loc.upper()}\n")
                
                summary.append(f"\n🔧 OPCIONES CONFIGURADAS:\n")
                summary.append(f"  • Modo simulación: {'Sí' if self.limpieza_dry_run_var.get() else 'No'}\n")
                summary.append(f"  • Crear backup: {'Sí' if getattr(self, 'limpieza_backup_var', tk.BooleanVar()).get() else 'No'}\n")
                summary.append(f"  • Procesamiento paralelo: {'Sí' if getattr(self, 'limpieza_parallel_var', tk.BooleanVar()).get() else 'No'}\n")
                summary.append(f"  • Confirmar eliminación: {'Sí' if getattr(self, 'limpieza_confirm_var', tk.BooleanVar()).get() else 'No'}\n")
                
                if len(validation['valid']) > 0:
                    summary.append(f"\n✅ LISTO PARA EJECUTAR\n")
                else:
                    summary.append(f"\n⚠️ NO HAY ARCHIVOS VÁLIDOS PARA PROCESAR\n")
                
                # Actualizar widgets
                self.root.after(0, lambda: self.update_analysis_widget(summary_text, ''.join(summary)))
                
                # Archivos válidos
                valid_content = []
                valid_content.append(f"✅ ARCHIVOS VÁLIDOS ({len(validation['valid'])})\n")
                valid_content.append("=" * 40 + "\n\n")
                
                for i, filename in enumerate(validation['valid'], 1):
                    valid_content.append(f"{i:4d}. {filename}\n")
                
                self.root.after(0, lambda: self.update_analysis_widget(valid_text, ''.join(valid_content)))
                
                # Archivos inválidos
                invalid_content = []
                if validation['invalid_names']:
                    invalid_content.append(f"❌ NOMBRES INVÁLIDOS ({len(validation['invalid_names'])})\n")
                    invalid_content.append("=" * 40 + "\n\n")
                    
                    for i, filename in enumerate(validation['invalid_names'], 1):
                        invalid_content.append(f"{i:4d}. {filename}\n")
                else:
                    invalid_content.append("✅ NO HAY NOMBRES INVÁLIDOS\n")
                
                self.root.after(0, lambda: self.update_analysis_widget(invalid_text, ''.join(invalid_content)))
                
            except Exception as e:
                error_msg = f"❌ Error en análisis: {e}\n"
                for widget in [summary_text, valid_text, invalid_text]:
                    self.root.after(0, lambda w=widget: self.update_analysis_widget(w, error_msg))
        
        threading.Thread(target=analyze_worker, daemon=True).start()
    
    def update_analysis_widget(self, widget, content):
        """Actualizar contenido de widget de análisis"""
        widget.delete(1.0, tk.END)
        widget.insert(1.0, content)
    
    def validate_limpieza_config(self):
        """Validar configuración de limpieza"""
        archivo = self.limpieza_archivo_var.get().strip()
        selected_locations = [loc for loc, var in self.limpieza_locations.items() if var.get()]
        
        issues = []
        
        if not archivo:
            issues.append("📄 Archivo de lista no seleccionado")
        elif not Path(archivo).exists():
            issues.append("📄 Archivo de lista no existe")
        
        if not selected_locations:
            issues.append("🌐 No hay ubicaciones seleccionadas")
        
        # Actualizar UI según validación
        if issues:
            self.update_status(f"Configuración incompleta: {issues[0]}", "warning")
            if hasattr(self, 'limpieza_execute_btn'):
                self.limpieza_execute_btn.config(state='disabled')
        else:
            self.update_status("Configuración de limpieza válida", "success")
            if hasattr(self, 'limpieza_execute_btn'):
                self.limpieza_execute_btn.config(state='normal')
        
        return len(issues) == 0
    
    def save_limpieza_config(self):
        """Guardar configuración de limpieza"""
        try:
            from core.config_manager import config_manager
            
            # Obtener ubicaciones seleccionadas
            selected_locations = {}
            for loc, var in self.limpieza_locations.items():
                selected_locations[loc] = var.get()
            
            config = {
                'archivo_lista': self.limpieza_archivo_var.get(),
                'dry_run': self.limpieza_dry_run_var.get(),
                'ubicaciones_seleccionadas': selected_locations,
                'backup': getattr(self, 'limpieza_backup_var', tk.BooleanVar()).get(),
                'paralelo': getattr(self, 'limpieza_parallel_var', tk.BooleanVar()).get(),
                'confirmar': getattr(self, 'limpieza_confirm_var', tk.BooleanVar()).get(),
                'verificar_antes': getattr(self, 'limpieza_verify_before_var', tk.BooleanVar()).get(),
                'continuar_errores': getattr(self, 'limpieza_skip_errors_var', tk.BooleanVar()).get()
            }
            
            config_manager.save_user_config('limpieza', config)
            messagebox.showinfo("Configuración", "Configuración de limpieza guardada correctamente")
            self.update_status("Configuración guardada", "success")
            
        except Exception as e:
            messagebox.showerror("Error", f"Error guardando configuración: {e}")
            self.update_status(f"Error guardando configuración: {e}", "error")
    
    def load_saved_config(self):
        """Cargar configuración guardada al iniciar"""
        try:
            from core.config_manager import config_manager
            
            # Cargar configuración de validación
            validacion_config = config_manager.get_user_config('validacion', {})
            if validacion_config:
                self.validacion_origen_var.set(validacion_config.get('carpeta_origen', ''))
                self.validacion_rechazados_var.set(validacion_config.get('carpeta_rechazados', ''))
                self.validacion_dry_run_var.set(validacion_config.get('dry_run', True))
            
            # Cargar configuración de limpieza
            limpieza_config = config_manager.get_user_config('limpieza', {})
            if limpieza_config:
                self.limpieza_archivo_var.set(limpieza_config.get('archivo_lista', ''))
                self.limpieza_dry_run_var.set(limpieza_config.get('dry_run', True))
                
                # Cargar ubicaciones seleccionadas
                ubicaciones_config = limpieza_config.get('ubicaciones_seleccionadas', {})
                for loc, var in getattr(self, 'limpieza_locations', {}).items():
                    var.set(ubicaciones_config.get(loc, True))
                
                # Actualizar info del archivo si existe
                archivo = limpieza_config.get('archivo_lista', '')
                if archivo:
                    self.update_file_info(archivo)
            
        except Exception as e:
            self.logger.warning(f"Error cargando configuración guardada: {e}")

# FIN PIEZA 3D
