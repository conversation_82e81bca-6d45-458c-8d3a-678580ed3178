"""
Gestor de Archivos Corporativo - Punto de Entrada Principal
=========================================================

Aplicación completa para gestión de archivos en entorno corporativo
con múltiples ubicaciones (Qnaps + PCs)
"""

import sys
import os
from pathlib import Path
import logging

# Agregar src al path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def setup_logging():
    """Configurar logging básico para la aplicación"""
    from core.logger import gestor_logger
    
    # El sistema de logging ya está configurado en core.logger
    # Solo necesitamos obtener el logger principal
    logger = gestor_logger.get_logger('sistema')
    logger.info("=== GESTOR DE ARCHIVOS CORPORATIVO INICIADO ===")
    logger.info(f"Directorio de trabajo: {os.getcwd()}")
    logger.info(f"Python: {sys.version}")
    
    return logger

def check_dependencies():
    """Verificar dependencias críticas"""
    missing_deps = []
    
    try:
        import tkinter
    except ImportError:
        missing_deps.append("tkinter")
    
    try:
        import cryptography
    except ImportError:
        missing_deps.append("cryptography")
    
    try:
        import psutil
    except ImportError:
        missing_deps.append("psutil")
    
    if missing_deps:
        print(f"❌ Dependencias faltantes: {', '.join(missing_deps)}")
        print("Instalar con: pip install -r requirements.txt")
        return False
    
    return True

def main():
    """Función principal de la aplicación"""
    
    print("🚀 Iniciando Gestor de Archivos Corporativo...")
    
    # Verificar dependencias
    if not check_dependencies():
        sys.exit(1)
    
    try:
        # Configurar logging
        logger = setup_logging()
        
        # Verificar configuración
        from core.config_manager import config
        
        app_info = config.get_app_info()
        logger.info(f"Aplicación: {app_info.get('name')} v{app_info.get('version')}")
        logger.info(f"Modo: {'Desarrollo' if config.is_dev_mode() else 'Producción'}")
        
        # Verificar ubicaciones configuradas
        locations = config.get_enabled_locations()
        logger.info(f"Ubicaciones configuradas: {len(locations)}")
        
        # Validar configuración básica
        validation = config.validate_config()
        if validation['errors']:
            logger.error("Errores de configuración:")
            for error in validation['errors']:
                logger.error(f"  - {error}")
            
            print("❌ Errores de configuración detectados")
            print("Revise el archivo de configuración y los logs para más detalles")
            input("Presione Enter para continuar de todas formas...")
        
        if validation['warnings']:
            logger.warning("Advertencias de configuración:")
            for warning in validation['warnings']:
                logger.warning(f"  - {warning}")
        
        # Iniciar dashboard
        logger.info("Iniciando interfaz gráfica...")
        
        # Importar dashboard desde raíz (archivo principal de 4,100+ líneas)
        sys.path.insert(0, str(Path(__file__).parent))
        from dashboard import GestorDashboard
        
        dashboard = GestorDashboard()
        logger.info("Dashboard inicializado exitosamente")
        
        # Ejecutar aplicación
        dashboard.run()
        
    except KeyboardInterrupt:
        print("\n⚠️ Aplicación interrumpida por el usuario")
        logger.info("Aplicación interrumpida por el usuario")
        sys.exit(0)
        
    except Exception as e:
        error_msg = f"Error fatal en la aplicación: {e}"
        print(f"❌ {error_msg}")
        
        # Log error si el logger está disponible
        try:
            logger.error(error_msg)
            logger.error("Detalles del error:", exc_info=True)
        except:
            pass
        
        # Mostrar información de debug
        import traceback
        print("\n🔍 Información de debug:")
        traceback.print_exc()
        
        input("\nPresione Enter para salir...")
        sys.exit(1)
    
    finally:
        # Cleanup final
        try:
            logger.info("=== GESTOR DE ARCHIVOS CORPORATIVO FINALIZADO ===")
        except:
            pass

if __name__ == "__main__":
    main()
