"""
dashboard_pieza5a.py
PIEZA 5A: Métodos de logs avanzados - b<PERSON>queda, exportar, estadísticas
Gestor de Archivos Corporativo
"""

# CONTINUACIÓN DE LA CLASE GestorDashboard - MÉTODOS DE LOGS AVANZADOS

    def search_logs(self):
        """Buscar texto en logs"""
        search_term = self.search_entry.get().strip()
        if not search_term:
            messagebox.showwarning("Búsqueda", "Ingrese un término de búsqueda")
            return
        
        self.search_results_text.config(state='normal')
        self.search_results_text.delete(1.0, tk.END)
        self.search_results_text.insert(1.0, f"🔍 Buscando '{search_term}'...\n")
        self.search_results_text.config(state='disabled')
        self.search_results_text.update()
        
        def search_worker():
            try:
                from core.logger import gestor_logger
                
                log_files = gestor_logger.get_log_files()
                results = []
                case_sensitive = self.search_case_var.get()
                
                for category, file_path in log_files.items():
                    if not Path(file_path).exists():
                        continue
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            lines = f.readlines()
                        
                        for line_num, line in enumerate(lines, 1):
                            search_line = line if case_sensitive else line.lower()
                            search_target = search_term if case_sensitive else search_term.lower()
                            
                            if search_target in search_line:
                                results.append((category, line_num, line.strip()))
                    
                    except Exception as e:
                        results.append(('ERROR', 0, f"Error leyendo {file_path}: {e}"))
                
                # Mostrar resultados
                search_content = f"🔍 RESULTADOS DE BÚSQUEDA: '{search_term}'\n"
                search_content += "=" * 60 + "\n\n"
                
                if results:
                    search_content += f"📊 Encontradas {len(results)} coincidencias:\n\n"
                    
                    for category, line_num, line in results[:100]:  # Máximo 100 resultados
                        search_content += f"[{category.upper()}:{line_num}] {line}\n"
                    
                    if len(results) > 100:
                        search_content += f"\n... y {len(results) - 100} coincidencias más\n"
                else:
                    search_content += "❌ No se encontraron coincidencias\n"
                
                self.root.after(0, lambda: self.display_search_results(search_content))
                
            except Exception as e:
                error_msg = f"❌ Error en búsqueda: {e}\n"
                self.root.after(0, lambda: self.display_search_results(error_msg))
        
        threading.Thread(target=search_worker, daemon=True).start()
    
    def display_search_results(self, content):
        """Mostrar resultados de búsqueda"""
        self.search_results_text.config(state='normal')
        self.search_results_text.delete(1.0, tk.END)
        self.search_results_text.insert(1.0, content)
        
        # Highlight términos de búsqueda
        search_term = self.search_entry.get().strip()
        if search_term:
            self.highlight_search_term(self.search_results_text, search_term)
        
        self.search_results_text.config(state='disabled')
    
    def highlight_search_term(self, text_widget, search_term):
        """Resaltar término de búsqueda en texto"""
        try:
            content = text_widget.get(1.0, tk.END)
            start_pos = '1.0'
            
            while True:
                pos = text_widget.search(search_term, start_pos, tk.END, nocase=not self.search_case_var.get())
                if not pos:
                    break
                
                end_pos = f"{pos}+{len(search_term)}c"
                text_widget.tag_add('SEARCH_HIGHLIGHT', pos, end_pos)
                start_pos = end_pos
                
        except Exception as e:
            self.logger.debug(f"Error resaltando búsqueda: {e}")
    
    def export_logs(self):
        """Exportar logs filtrados a archivo"""
        try:
            export_path = filedialog.asksaveasfilename(
                title="Exportar Logs",
                defaultextension=".txt",
                filetypes=[("Archivos de texto", "*.txt"), ("Todos los archivos", "*.*")]
            )
            
            if not export_path:
                return
            
            # Obtener contenido actual filtrado
            content = self.logs_text.get(1.0, tk.END)
            
            # Agregar metadatos
            header = f"=== EXPORT DE LOGS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===\n"
            header += f"Categoría: {self.log_category_var.get()}\n"
            header += f"Nivel: {self.log_level_var.get()}\n"
            header += f"Filtro: {self.log_filter_var.get() or 'Ninguno'}\n"
            header += f"Fecha: {self.log_date_var.get()}\n"
            header += f"Líneas: {self.log_lines_var.get()}\n"
            header += "=" * 60 + "\n\n"
            
            full_content = header + content
            
            with open(export_path, 'w', encoding='utf-8') as f:
                f.write(full_content)
            
            messagebox.showinfo("Exportar", f"Logs exportados correctamente a:\n{export_path}")
            self.update_status(f"Logs exportados: {Path(export_path).name}", "success")
            
        except Exception as e:
            messagebox.showerror("Error", f"Error exportando logs: {e}")
    
    def clear_log_files(self):
        """Limpiar archivos de log físicos"""
        if not messagebox.askyesno("Confirmar", 
                                   "¿Está seguro de que desea limpiar TODOS los archivos de log?\n\n"
                                   "Esta acción no se puede deshacer."):
            return
        
        try:
            from core.logger import gestor_logger
            
            log_files = gestor_logger.get_log_files()
            cleared_count = 0
            
            for category, file_path in log_files.items():
                try:
                    if Path(file_path).exists():
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(f"# Log limpiado el {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        cleared_count += 1
                except Exception as e:
                    self.logger.error(f"Error limpiando {file_path}: {e}")
            
            messagebox.showinfo("Limpiar Logs", f"Se limpiaron {cleared_count} archivos de log")
            self.update_status(f"{cleared_count} archivos de log limpiados", "success")
            self.refresh_logs()
            
        except Exception as e:
            messagebox.showerror("Error", f"Error limpiando logs: {e}")
    
    def show_log_statistics(self):
        """Mostrar estadísticas de logs"""
        stats_window = tk.Toplevel(self.root)
        stats_window.title("📊 Estadísticas de Logs")
        stats_window.geometry("600x500")
        stats_window.transient(self.root)
        
        stats_text = tk.Text(stats_window, wrap=tk.WORD, font=('Consolas', 9))
        stats_scroll = ttk.Scrollbar(stats_window, orient=tk.VERTICAL, command=stats_text.yview)
        stats_text.configure(yscrollcommand=stats_scroll.set)
        
        stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        stats_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
        # Calcular estadísticas en thread
        def calc_stats():
            try:
                from core.logger import gestor_logger
                
                stats_text.insert(1.0, "📊 Calculando estadísticas...\n")
                stats_text.update()
                
                log_files = gestor_logger.get_log_files()
                total_lines = 0
                total_size = 0
                level_counts = {'DEBUG': 0, 'INFO': 0, 'WARNING': 0, 'ERROR': 0, 'CRITICAL': 0}
                category_stats = {}
                
                for category, file_path in log_files.items():
                    if not Path(file_path).exists():
                        continue
                    
                    try:
                        file_size = Path(file_path).stat().st_size
                        total_size += file_size
                        
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            lines = f.readlines()
                        
                        file_lines = len(lines)
                        total_lines += file_lines
                        
                        category_stats[category] = {
                            'lines': file_lines,
                            'size': file_size,
                            'levels': {'DEBUG': 0, 'INFO': 0, 'WARNING': 0, 'ERROR': 0, 'CRITICAL': 0}
                        }
                        
                        for line in lines:
                            for level in level_counts.keys():
                                if level in line:
                                    level_counts[level] += 1
                                    category_stats[category]['levels'][level] += 1
                                    break
                    
                    except Exception as e:
                        category_stats[category] = {'error': str(e)}
                
                # Generar reporte
                report = []
                report.append("📊 ESTADÍSTICAS DE LOGS\n")
                report.append("=" * 50 + "\n\n")
                
                report.append("📈 RESUMEN GENERAL:\n")
                report.append(f"  Total líneas: {total_lines:,}\n")
                report.append(f"  Tamaño total: {total_size / (1024*1024):.2f} MB\n")
                report.append(f"  Archivos: {len([f for f in log_files.values() if Path(f).exists()])}\n\n")
                
                report.append("📊 POR NIVEL:\n")
                for level, count in level_counts.items():
                    percentage = (count / total_lines * 100) if total_lines > 0 else 0
                    report.append(f"  {level:<8}: {count:,} ({percentage:.1f}%)\n")
                
                report.append("\n📁 POR CATEGORÍA:\n")
                for category, stats in category_stats.items():
                    if 'error' in stats:
                        report.append(f"  {category.upper()}: ❌ Error - {stats['error']}\n")
                    else:
                        size_mb = stats['size'] / (1024*1024)
                        report.append(f"  {category.upper()}:\n")
                        report.append(f"    Líneas: {stats['lines']:,}\n")
                        report.append(f"    Tamaño: {size_mb:.2f} MB\n")
                        
                        # Niveles por categoría
                        for level, count in stats['levels'].items():
                            if count > 0:
                                report.append(f"    {level}: {count}\n")
                        report.append("\n")
                
                stats_content = ''.join(report)
                stats_text.delete(1.0, tk.END)
                stats_text.insert(1.0, stats_content)
                
            except Exception as e:
                stats_text.delete(1.0, tk.END)
                stats_text.insert(1.0, f"❌ Error calculando estadísticas: {e}\n")
        
        threading.Thread(target=calc_stats, daemon=True).start()
        
        ttk.Button(stats_window, text="Cerrar", command=stats_window.destroy).pack(pady=10)
    
    def clear_log_display(self):
        """Limpiar vista de logs"""
        self.logs_text.config(state='normal')
        self.logs_text.delete(1.0, tk.END)
        self.logs_text.insert(1.0, "Vista limpiada\n")
        self.logs_text.config(state='disabled')
        
        self.logs_count_label.config(text="0 líneas")
        self.update_status("Vista de logs limpiada", "info")
    
    def update_connectivity_stats(self):
        """Actualizar estadísticas de conectividad"""
        try:
            stats_content = []
            stats_content.append("🌐 ESTADÍSTICAS DE CONECTIVIDAD\n")
            stats_content.append("=" * 40 + "\n\n")
            
            online_count = 0
            total_count = len(getattr(self, 'connectivity_labels', {}))
            response_times = []
            
            for location_id, status in self.connectivity_status.items():
                if status.get('connected', False):
                    online_count += 1
                    if 'response_time' in status:
                        response_times.append(status['response_time'])
                
                stats_content.append(f"📍 {location_id.upper()}:\n")
                
                if status.get('connected', False):
                    stats_content.append(f"   Estado: 🟢 ONLINE\n")
                    if 'response_time' in status:
                        stats_content.append(f"   Tiempo respuesta: {status['response_time']:.2f}s\n")
                    if 'method' in status:
                        stats_content.append(f"   Método: {status['method']}\n")
                    if 'write_access' in status:
                        access = "✅ Lectura/Escritura" if status['write_access'] else "⚠️ Solo lectura"
                        stats_content.append(f"   Acceso: {access}\n")
                else:
                    stats_content.append(f"   Estado: 🔴 OFFLINE\n")
                    if 'error' in status:
                        stats_content.append(f"   Error: {status['error']}\n")
                
                stats_content.append("\n")
            
            # Estadísticas generales
            stats_content.append("📊 RESUMEN:\n")
            stats_content.append(f"   Ubicaciones online: {online_count}/{total_count}\n")
            availability = (online_count / total_count * 100) if total_count > 0 else 0
            stats_content.append(f"   Disponibilidad: {availability:.1f}%\n")
            
            if response_times:
                avg_time = sum(response_times) / len(response_times)
                min_time = min(response_times)
                max_time = max(response_times)
                
                stats_content.append(f"   Tiempo promedio: {avg_time:.2f}s\n")
                stats_content.append(f"   Tiempo mínimo: {min_time:.2f}s\n")
                stats_content.append(f"   Tiempo máximo: {max_time:.2f}s\n")
            
            # Actualizar widget
            if hasattr(self, 'connectivity_stats_text'):
                self.connectivity_stats_text.config(state='normal')
                self.connectivity_stats_text.delete(1.0, tk.END)
                self.connectivity_stats_text.insert(1.0, ''.join(stats_content))
                self.connectivity_stats_text.config(state='disabled')
                
        except Exception as e:
            self.logger.error(f"Error actualizando estadísticas conectividad: {e}")
    
    def update_operations_stats(self):
        """Actualizar estadísticas de operaciones"""
        try:
            stats_content = []
            stats_content.append("⚙️ ESTADÍSTICAS DE OPERACIONES\n")
            stats_content.append("=" * 40 + "\n\n")
            
            if hasattr(self, 'last_operation_stats') and self.last_operation_stats:
                for operation, stats in self.last_operation_stats.items():
                    stats_content.append(f"🔧 {operation.upper()}:\n")
                    
                    if operation == 'validacion':
                        stats_content.append(f"   Archivos analizados: {stats.get('archivos_analizados', 0)}\n")
                        stats_content.append(f"   Duplicados encontrados: {stats.get('duplicados_encontrados', 0)}\n")
                        stats_content.append(f"   Archivos movidos: {stats.get('archivos_movidos', 0)}\n")
                        
                    elif operation == 'limpieza':
                        stats_content.append(f"   Archivos válidos: {stats.get('archivos_validos', 0)}\n")
                        stats_content.append(f"   Total eliminados: {stats.get('total_archivos_eliminados', 0)}\n")
                        stats_content.append(f"   Ubicaciones procesadas: {stats.get('ubicaciones_conectadas', 0)}\n")
                    
                    duracion = stats.get('duracion_total', 0)
                    stats_content.append(f"   Duración: {duracion:.2f}s\n")
                    stats_content.append(f"   Errores: {stats.get('errores', 0)}\n\n")
            else:
                stats_content.append("ℹ️ No hay operaciones completadas aún\n")
            
            # Actualizar widget
            if hasattr(self, 'operations_stats_text'):
                self.operations_stats_text.config(state='normal')
                self.operations_stats_text.delete(1.0, tk.END)
                self.operations_stats_text.insert(1.0, ''.join(stats_content))
                self.operations_stats_text.config(state='disabled')
                
        except Exception as e:
            self.logger.error(f"Error actualizando estadísticas operaciones: {e}")
    
    def update_system_stats(self):
        """Actualizar estadísticas del sistema (worker thread)"""
        try:
            import psutil
            
            # CPU y memoria
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Actualizar connectivity y operations stats
            self.update_connectivity_stats()
            self.update_operations_stats()
            
        except Exception as e:
            self.logger.error(f"Error en stats worker: {e}")

# FIN PIEZA 5A
