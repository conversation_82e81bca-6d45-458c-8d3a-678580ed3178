"""
Gestor de Archivos Corporativo - Main con Logo IGSON
===================================================

Aplicación completa para gestión de archivos en entorno corporativo
con múltiples ubicaciones (Qnaps + PCs)

INCLUYE: Logo IGSON prominente en dashboard principal
"""

import sys
import os
from pathlib import Path
import logging

# Agregar src al path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Imports para manejo de imagen
try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

def mostrar_logo_igson_startup():
    """Mostrar logo IGSON en startup"""
    try:
        logo_path = Path(__file__).parent / "logo_igson.png"
        
        print("\n" + "="*70)
        print("      🏢 IGSON - GESTOR DE ARCHIVOS CORPORATIVO")
        print("="*70)
        
        if logo_path.exists():
            print(f"📷 Logo corporativo: {logo_path.name}")
            
            if PIL_AVAILABLE:
                try:
                    # Mostrar información detallada de la imagen
                    with Image.open(logo_path) as img:
                        print(f"📐 Dimensiones: {img.size[0]}x{img.size[1]} píxeles")
                        print(f"🎨 Formato: {img.format}")
                        print(f"🎯 Modo color: {img.mode}")
                        
                        # Calcular tamaño en KB
                        size_kb = logo_path.stat().st_size / 1024
                        print(f"📦 Tamaño: {size_kb:.1f} KB")
                except Exception as e:
                    print(f"⚠️  No se pudo analizar imagen: {e}")
            else:
                print("ℹ️  Pillow no disponible - instale para análisis completo")
                size_kb = logo_path.stat().st_size / 1024
                print(f"📦 Tamaño archivo: {size_kb:.1f} KB")
        else:
            print("⚠️  Logo no encontrado en ruta esperada")
            print(f"📍 Buscado en: {logo_path}")
            print("💡 Asegúrese de tener logo_igson.png en la raíz del proyecto")
        
        print("\n🎯 APLICACIÓN: Dashboard Unificado de Gestión")
        print("🔧 Funciones: Validación de Duplicados + Limpieza por Lista")
        print("🌐 Entorno: Multi-ubicación (QNAPs + PCs)")
        print("="*70 + "\n")
        
        return logo_path.exists()
        
    except Exception as e:
        print(f"⚠️  Error mostrando logo: {e}")
        return False

def setup_logging():
    """Configurar logging básico para la aplicación"""
    try:
        from core.logger import gestor_logger
        
        # El sistema de logging ya está configurado en core.logger
        logger = gestor_logger.get_logger('sistema')
        logger.info("=== GESTOR DE ARCHIVOS CORPORATIVO INICIADO ===")
        logger.info(f"Directorio de trabajo: {os.getcwd()}")
        logger.info(f"Python: {sys.version}")
        
        return logger
    except ImportError:
        # Fallback a logging básico si no está disponible el sistema core
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        logger = logging.getLogger('gestor_main')
        logger.info("=== GESTOR DE ARCHIVOS CORPORATIVO INICIADO (MODO BÁSICO) ===")
        return logger

def check_dependencies():
    """Verificar dependencias críticas"""
    missing_deps = []
    
    print("🔍 Verificando dependencias...")
    
    try:
        import tkinter
        print("  ✅ tkinter - OK")
    except ImportError:
        missing_deps.append("tkinter")
        print("  ❌ tkinter - FALTANTE")
    
    try:
        import cryptography
        print("  ✅ cryptography - OK")
    except ImportError:
        missing_deps.append("cryptography")
        print("  ⚠️  cryptography - OPCIONAL (para credenciales)")
    
    try:
        import psutil
        print("  ✅ psutil - OK")
    except ImportError:
        missing_deps.append("psutil")
        print("  ⚠️  psutil - OPCIONAL (para monitoreo)")
    
    try:
        from PIL import Image
        print("  ✅ Pillow (PIL) - OK")
    except ImportError:
        print("  ⚠️  Pillow (PIL) - OPCIONAL (para logo avanzado)")
    
    critical_missing = [dep for dep in missing_deps if dep == "tkinter"]
    
    if critical_missing:
        print(f"\n❌ Dependencias críticas faltantes: {', '.join(critical_missing)}")
        print("Instalar con: pip install -r requirements.txt")
        return False
    
    if missing_deps:
        print(f"\n⚠️  Dependencias opcionales faltantes: {', '.join(missing_deps)}")
        print("La aplicación funcionará con funcionalidad limitada")
    else:
        print("\n✅ Todas las dependencias están disponibles")
    
    return True

def main():
    """Función principal de la aplicación con logo IGSON"""
    
    # Mostrar logo IGSON prominente
    logo_disponible = mostrar_logo_igson_startup()
    
    print("🚀 Iniciando sistema...")
    
    # Verificar dependencias
    if not check_dependencies():
        input("\n❌ Presione Enter para salir...")
        sys.exit(1)
    
    try:
        # Configurar logging
        logger = setup_logging()
        
        # Verificar configuración si está disponible
        try:
            from core.config_manager import config
            
            app_info = config.get_app_info()
            logger.info(f"Aplicación: {app_info.get('name')} v{app_info.get('version')}")
            logger.info(f"Modo: {'Desarrollo' if config.is_dev_mode() else 'Producción'}")
            
            # Verificar ubicaciones configuradas
            locations = config.get_enabled_locations()
            logger.info(f"Ubicaciones configuradas: {len(locations)}")
            
            # Validar configuración básica
            validation = config.validate_config()
            if validation['errors']:
                logger.error("Errores de configuración:")
                for error in validation['errors']:
                    logger.error(f"  - {error}")
                
                print("⚠️  Errores de configuración detectados")
                print("La aplicación funcionará con configuración por defecto")
            
            if validation['warnings']:
                logger.warning("Advertencias de configuración:")
                for warning in validation['warnings']:
                    logger.warning(f"  - {warning}")
        
        except ImportError:
            print("ℹ️  Sistema de configuración no disponible - usando valores por defecto")
            logger.info("Ejecutando en modo standalone con configuración básica")
        
        # Iniciar dashboard
        logger.info("Iniciando interfaz gráfica...")
        print("🎨 Cargando dashboard principal...")
        
        try:
            # Importar dashboard desde raíz (archivo principal de 4,100+ líneas)
            sys.path.insert(0, str(Path(__file__).parent))
            from dashboard import GestorDashboard
            
            print("✅ Dashboard cargado correctamente")
            
            dashboard = GestorDashboard()
            logger.info("Dashboard inicializado exitosamente")
            
            # Mensaje especial si el logo está disponible
            if logo_disponible:
                print("🏢 IGSON - Sistema corporativo listo")
            else:
                print("⚠️  Sistema listo (logo no encontrado)")
            
            print("🖥️  Abriendo interfaz gráfica...\n")
            
            # Ejecutar aplicación
            dashboard.run()
            
        except ImportError as e:
            print(f"❌ Error importando dashboard: {e}")
            print("💡 Verificar que dashboard.py existe en la raíz del proyecto")
            print("\n🔧 Alternativa: Ejecutar aplicaciones independientes:")
            print("   - python limpieza_lista_fixed.py")
            print("   - python validacion_duplicados_fixed.py")
            
            logger.error(f"Error importando dashboard: {e}")
            input("\nPresione Enter para salir...")
            sys.exit(1)
        
    except KeyboardInterrupt:
        print("\n⚠️  Aplicación interrumpida por el usuario")
        logger.info("Aplicación interrumpida por el usuario")
        sys.exit(0)
        
    except Exception as e:
        error_msg = f"Error fatal en la aplicación: {e}"
        print(f"\n❌ {error_msg}")
        
        # Log error si el logger está disponible
        try:
            logger.error(error_msg)
            logger.error("Detalles del error:", exc_info=True)
        except:
            pass
        
        # Mostrar información de debug
        import traceback
        print("\n🔍 Información de debug:")
        traceback.print_exc()
        
        print(f"\n💡 Sugerencias:")
        print(f"   1. Verificar que todos los archivos estén presentes")
        print(f"   2. Comprobar permisos de archivos y carpetas")
        print(f"   3. Reinstalar dependencias: pip install -r requirements.txt")
        print(f"   4. Ejecutar desde el directorio correcto del proyecto")
        
        input("\nPresione Enter para salir...")
        sys.exit(1)
    
    finally:
        # Cleanup final
        try:
            logger.info("=== GESTOR DE ARCHIVOS CORPORATIVO FINALIZADO ===")
        except:
            pass

if __name__ == "__main__":
    main()
