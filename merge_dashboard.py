#!/usr/bin/env python3
"""
merge_dashboard.py
Script para hacer merge automático de todas las piezas del dashboard

Uso:
    python merge_dashboard.py

El script:
1. <PERSON> todas las piezas en orden correcto
2. Elimina headers duplicados y comentarios de continuación
3. Genera dashboard.py completo y funcional
4. Hace backup del archivo anterior si existe

Autor: Igson
Fecha: Junio 2025
"""

import os
import shutil
from pathlib import Path
from datetime import datetime

def main():
    """Función principal del merge"""
    
    print("🔧 MERGE AUTOMÁTICO - GESTOR DE ARCHIVOS CORPORATIVO")
    print("=" * 60)
    
    # Directorio actual
    current_dir = Path.cwd()
    print(f"📁 Directorio actual: {current_dir}")
    
    # Lista de piezas en orden correcto
    piezas = [
        "dashboard_pieza1.py",
        "dashboard_pieza2.py", 
        "dashboard_pieza3a.py",
        "dashboard_pieza3b.py",
        "dashboard_pieza3c.py",
        "dashboard_pieza3d.py",
        "dashboard_pieza4.py",
        "dashboard_pieza5a.py",
        "dashboard_pieza5b.py",
        "dashboard_pieza5c.py"
    ]
    
    print(f"\n📋 Piezas a procesar: {len(piezas)}")
    for i, pieza in enumerate(piezas, 1):
        print(f"  {i:2d}. {pieza}")
    
    # Verificar que todas las piezas existen
    print(f"\n🔍 Verificando archivos...")
    missing_files = []
    for pieza in piezas:
        pieza_path = current_dir / pieza
        if pieza_path.exists():
            size_kb = pieza_path.stat().st_size / 1024
            print(f"  ✅ {pieza} ({size_kb:.1f} KB)")
        else:
            print(f"  ❌ {pieza} - NO ENCONTRADO")
            missing_files.append(pieza)
    
    if missing_files:
        print(f"\n❌ ERROR: Faltan {len(missing_files)} archivos:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nPor favor asegúrate de que todos los archivos estén en el directorio actual.")
        input("Presiona Enter para salir...")
        return False
    
    # Hacer backup si dashboard.py ya existe
    output_file = current_dir / "dashboard.py"
    if output_file.exists():
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = current_dir / f"dashboard_backup_{timestamp}.py"
        shutil.copy2(output_file, backup_file)
        print(f"\n💾 Backup creado: {backup_file.name}")
    
    # Procesar y combinar archivos
    print(f"\n🔄 Procesando archivos...")
    
    combined_content = []
    
    # Header principal del archivo final
    header = f'''"""
dashboard.py
Gestor de Archivos Corporativo - Dashboard Completo

ARCHIVO GENERADO AUTOMÁTICAMENTE
Fecha de merge: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

Piezas combinadas:
{chr(10).join(f"- {pieza}" for pieza in piezas)}

Total líneas de código: ~3,150
Desarrollado: Junio 2025
Autor: Igson
"""

'''
    
    combined_content.append(header)
    
    for i, pieza in enumerate(piezas, 1):
        print(f"  📄 Procesando {pieza}...")
        
        pieza_path = current_dir / pieza
        with open(pieza_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Limpiar contenido
        cleaned_content = clean_piece_content(content, i == 1)  # Solo la primera pieza mantiene imports
        
        if cleaned_content.strip():
            combined_content.append(f"\n# === CONTENIDO DE {pieza.upper()} ===\n")
            combined_content.append(cleaned_content)
        
        print(f"    ✅ Procesado ({len(cleaned_content.splitlines())} líneas)")
    
    # Escribir archivo final
    print(f"\n💾 Escribiendo archivo final...")
    
    final_content = ''.join(combined_content)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(final_content)
    
    # Estadísticas finales
    final_size_kb = output_file.stat().st_size / 1024
    final_lines = len(final_content.splitlines())
    
    print(f"\n🎉 ¡MERGE COMPLETADO EXITOSAMENTE!")
    print(f"=" * 40)
    print(f"📄 Archivo generado: {output_file.name}")
    print(f"📏 Tamaño: {final_size_kb:.1f} KB")
    print(f"📊 Líneas totales: {final_lines:,}")
    print(f"🔧 Piezas combinadas: {len(piezas)}")
    
    # Verificación básica
    print(f"\n🔍 Verificación básica...")
    if verify_merged_file(output_file):
        print(f"  ✅ Archivo parece válido")
        print(f"  ✅ Sintaxis Python correcta")
        print(f"  ✅ Clase GestorDashboard encontrada")
    else:
        print(f"  ⚠️  Posibles problemas detectados")
    
    print(f"\n📝 SIGUIENTE PASO:")
    print(f"  Ejecuta: python {output_file.name}")
    print(f"  O desde src/: python ../dashboard.py")
    
    input(f"\nPresiona Enter para continuar...")
    return True

def clean_piece_content(content: str, is_first_piece: bool) -> str:
    """Limpiar contenido de una pieza eliminando headers y comentarios"""
    
    lines = content.splitlines()
    cleaned_lines = []
    
    skip_header = True
    skip_continuation_comments = True
    
    for line in lines:
        line_stripped = line.strip()
        
        # Saltar header inicial (hasta primera línea de código real)
        if skip_header:
            if (line_stripped.startswith('"""') or 
                line_stripped.startswith('#') or 
                line_stripped.startswith('dashboard_pieza') or
                line_stripped.startswith('PIEZA') or
                line_stripped.startswith('Gestor de Archivos') or
                line == '' or
                line_stripped.startswith('CONTINUACIÓN')):
                continue
            else:
                skip_header = False
        
        # Saltar comentarios de continuación de clase
        if skip_continuation_comments:
            if (line_stripped.startswith('# CONTINUACIÓN DE LA CLASE') or
                line_stripped.startswith('# FIN PIEZA')):
                continue
            elif line_stripped and not line_stripped.startswith('#'):
                skip_continuation_comments = False
        
        # Para la primera pieza, mantener imports
        if is_first_piece:
            cleaned_lines.append(line)
        else:
            # Para el resto, saltar imports duplicados
            if (line_stripped.startswith('import ') or 
                line_stripped.startswith('from ') or
                (line_stripped.startswith('#') and 'import' in line_stripped.lower())):
                continue
            else:
                cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def verify_merged_file(file_path: Path) -> bool:
    """Verificación básica del archivo mergeado"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Verificar sintaxis básica
        try:
            compile(content, str(file_path), 'exec')
        except SyntaxError as e:
            print(f"    ❌ Error de sintaxis: {e}")
            return False
        
        # Verificar que contiene la clase principal
        if 'class GestorDashboard:' not in content:
            print(f"    ❌ Clase GestorDashboard no encontrada")
            return False
        
        # Verificar métodos principales
        required_methods = [
            'def __init__(self)',
            'def create_tabs(self)',
            'def execute_validacion(self)',
            'def execute_limpieza(self)',
            'def run(self)'
        ]
        
        missing_methods = []
        for method in required_methods:
            if method not in content:
                missing_methods.append(method)
        
        if missing_methods:
            print(f"    ⚠️  Métodos faltantes: {missing_methods}")
            return False
        
        return True
        
    except Exception as e:
        print(f"    ❌ Error verificando archivo: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            exit(1)
    except KeyboardInterrupt:
        print(f"\n\n🛑 Operación cancelada por el usuario")
        exit(1)
    except Exception as e:
        print(f"\n❌ ERROR FATAL: {e}")
        import traceback
        traceback.print_exc()
        input("Presiona Enter para salir...")
        exit(1)
