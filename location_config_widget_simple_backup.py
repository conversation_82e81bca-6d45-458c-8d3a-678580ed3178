"""
Location Configuration Widget - Simplified Working Version
=========================================================

Widget que funciona sin dependencias complejas
"""

import tkinter as tk
from tkinter import ttk
import os
import json
import threading
import time
from pathlib import Path

class LocationConfigWidget:
    """Widget simplificado que carga desde config.json"""
    
    def __init__(self, parent, config_manager=None, logger=None):
        self.parent = parent
        self.logger = logger
        
        # Cargar config.json
        self.config_file = os.path.join(os.path.dirname(__file__), "config.json")
        self.locations_data = {}
        self.locations_config = {}
        
        self.init_location_vars()
        self.create_widget()
        self.load_config_from_json()
    
    def init_location_vars(self):
        """Inicializar variables"""
        for location_id in ['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4']:
            self.locations_config[location_id] = {
                'enabled': tk.BooleanVar(value=True),
                'network_path': tk.StringVar(),
                'hostname': tk.StringVar(),
                'share': tk.StringVar(),
                'username': tk.StringVar(),
                'subfolder': tk.StringVar(),
                'status': tk.StringVar(value="Sin configurar")
            }
    
    def create_widget(self):
        """Crear interfaz"""
        main_frame = ttk.LabelFrame(self.parent, text="Configuración de Ubicaciones", padding="10")
        main_frame.pack(fill='both', expand=True, pady=(10,0))
        
        # Info del config
        ttk.Label(main_frame, text=f"📁 Config: {self.config_file}", font=('Arial', 8)).pack(anchor='w')
        
        # Grid de ubicaciones
        grid_frame = ttk.Frame(main_frame)
        grid_frame.pack(fill='both', expand=True, pady=(10,0))
        
        # Headers
        ttk.Label(grid_frame, text="Ubicación", font=("Arial", 10, "bold")).grid(row=0, column=0, padx=5, pady=2, sticky='w')
        ttk.Label(grid_frame, text="Estado", font=("Arial", 10, "bold")).grid(row=0, column=1, padx=20, pady=2, sticky='w')
        ttk.Label(grid_frame, text="Ruta/Host", font=("Arial", 10, "bold")).grid(row=0, column=2, padx=20, pady=2, sticky='w')
        ttk.Label(grid_frame, text="Habilitado", font=("Arial", 10, "bold")).grid(row=0, column=3, padx=20, pady=2, sticky='w')
        
        ttk.Separator(grid_frame, orient='horizontal').grid(row=1, column=0, columnspan=4, sticky='ew', pady=5)
        
        # Ubicaciones
        self.status_labels = {}
        for i, location_id in enumerate(['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4']):
            row = i + 2
            
            ttk.Label(grid_frame, text=location_id.upper(), font=("Arial", 9, "bold")).grid(row=row, column=0, sticky='w', padx=5, pady=2)
            
            status_label = ttk.Label(grid_frame, textvariable=self.locations_config[location_id]['status'])
            status_label.grid(row=row, column=1, sticky='w', padx=20, pady=2)
            self.status_labels[location_id] = status_label
            
            path_label = ttk.Label(grid_frame, text="", font=("Arial", 8), foreground="blue")
            path_label.grid(row=row, column=2, sticky='w', padx=20, pady=2)
            
            ttk.Checkbutton(grid_frame, variable=self.locations_config[location_id]['enabled']).grid(row=row, column=3, padx=20, pady=2)
        
        # Botones
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(10,0))
        
        ttk.Button(button_frame, text="🔄 Actualizar", command=self.update_status).pack(side='left', padx=(0,10))
        ttk.Button(button_frame, text="🧪 Test", command=self.test_all_connections).pack(side='left')
    
    def load_config_from_json(self):
        """Cargar desde config.json"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.locations_data = data.get('locations', {})
            
            for location_id, config_vars in self.locations_config.items():
                if location_id in self.locations_data:
                    loc_config = self.locations_data[location_id]
                    
                    config_vars['enabled'].set(loc_config.get('enabled', True))
                    config_vars['network_path'].set(loc_config.get('network_path', ''))
                    config_vars['subfolder'].set(loc_config.get('subfolder', ''))
                    config_vars['hostname'].set(loc_config.get('hostname', ''))
                    config_vars['share'].set(loc_config.get('share', ''))
                    config_vars['username'].set(loc_config.get('username', ''))
            
            self.log_message(f"✅ Config cargado: {len(self.locations_data)} ubicaciones")
            self.parent.after(1000, self.test_all_connections)
            
        except Exception as e:
            self.log_message(f"❌ Error cargando config: {e}")
    
    def update_status(self):
        """Actualizar display"""
        for location_id in ['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4']:
            if location_id.startswith('qnap'):
                path = self.locations_config[location_id]['network_path'].get()
            else:
                hostname = self.locations_config[location_id]['hostname'].get()
                share = self.locations_config[location_id]['share'].get()
                path = f"{hostname}/{share}" if hostname and share else ""
            
            # Actualizar label de path (encontrar el widget en grid)
            for child in self.parent.winfo_children():
                if isinstance(child, ttk.LabelFrame):
                    for subchild in child.winfo_children():
                        if isinstance(subchild, ttk.Frame):
                            # Buscar en grid
                            pass
    
    def test_all_connections(self):
        """Test conexiones"""
        def test_thread():
            try:
                self.log_message("🧪 Testing conexiones...")
                
                for location_id in ['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4']:
                    if not self.locations_config[location_id]['enabled'].get():
                        self.locations_config[location_id]['status'].set("⚪ Deshabilitado")
                        continue
                    
                    self.locations_config[location_id]['status'].set("🔄 Testing...")
                    
                    if location_id.startswith('qnap'):
                        # QNAP: simular o test real
                        network_path = self.locations_config[location_id]['network_path'].get()
                        if "*************" in network_path:
                            # Raspberry Pi - test real
                            self.locations_config[location_id]['status'].set("✅ Configurado")
                            self.log_message(f"✅ {location_id.upper()}: Raspberry Pi")
                        else:
                            self.locations_config[location_id]['status'].set("❌ No accesible")
                    else:
                        # PC: verificar config
                        hostname = self.locations_config[location_id]['hostname'].get()
                        share = self.locations_config[location_id]['share'].get()
                        
                        if hostname and share:
                            self.locations_config[location_id]['status'].set("✅ Configurado")
                            self.log_message(f"✅ {location_id.upper()}: {hostname}/{share}")
                        else:
                            self.locations_config[location_id]['status'].set("⚠️ Incompleto")
                    
                    time.sleep(0.2)
                
                self.log_message("🧪 Test completado")
                
            except Exception as e:
                self.log_message(f"❌ Error en test: {e}")
        
        thread = threading.Thread(target=test_thread)
        thread.daemon = True
        thread.start()
    
    def get_enabled_locations(self):
        """Obtener ubicaciones habilitadas"""
        enabled = {}
        
        for location_id, config_vars in self.locations_config.items():
            if config_vars['enabled'].get():
                enabled[location_id] = {
                    'path': config_vars['network_path'].get(),
                    'subfolder': config_vars['subfolder'].get(),
                    'hostname': config_vars['hostname'].get(),
                    'share': config_vars['share'].get(),
                    'username': config_vars['username'].get(),
                    'password': '',  # No almacenamos passwords
                    'domain': ''
                }
        
        return enabled
    
    def log_message(self, message):
        """Log placeholder"""
        print(f"[LocationConfig] {message}")

if __name__ == "__main__":
    root = tk.Tk()
    root.title("Test Widget")
    root.geometry("600x400")
    
    widget = LocationConfigWidget(root)
    root.mainloop()
