"""
Location Configuration Widget - Fixed Version
============================================

Widget que carga configuración real desde config.json
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import json
from pathlib import Path
import threading
import time

import subprocess
import socket
import platform

def ping_host(self, hostname, timeout=3):
    """Test de ping con timeout corto"""
    try:
        if platform.system().lower() == "windows":
            cmd = f"ping -n 1 -w {timeout*1000} {hostname}"
        else:
            cmd = f"ping -c 1 -W {timeout} {hostname}"
        
        result = subprocess.run(cmd, shell=True, capture_output=True, 
                              text=True, timeout=timeout+1)
        return result.returncode == 0
        
    except (subprocess.TimeoutExpired, Exception) as e:
        self.log_message(f"❌ Ping timeout/error para {hostname}: {e}")
        return False

def test_smb_connection(self, hostname, share, username="", timeout=3):
    """Test de conexión SMB básica"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((hostname, 445))
        sock.close()
        
        if result == 0:
            self.log_message(f"✅ Puerto SMB 445 abierto en {hostname}")
            return True
        else:
            self.log_message(f"❌ Puerto SMB 445 cerrado en {hostname}")
            return False
            
    except Exception as e:
        self.log_message(f"❌ Error test SMB {hostname}: {e}")
        return False

def test_location_connectivity(self, location_id):
    """Test de conectividad REAL para una ubicación específica"""
    try:
        config_vars = self.locations_config[location_id]
        
        if not config_vars['enabled'].get():
            return "⚪ Deshabilitado"
        
        if location_id.startswith('qnap'):
            # QNAP: Test ruta local o de red
            path = config_vars['network_path'].get()
            
            if not path:
                return "❓ Sin configurar"
            
            # Test según tipo de ruta
            if path.endswith(':\\') and len(path) == 3:
                # Ruta local (M:\, N:\)
                if Path(path).exists():
                    return "✅ Conectado"
                else:
                    return "❌ Error red"
            elif path.startswith('\\\\'):
                # Ruta de red
                if self.test_network_path(path, timeout=3):
                    return "✅ Conectado"
                else:
                    return "❌ Error red"
            else:
                # Ruta local normal
                if Path(path).exists():
                    return "✅ Conectado"
                else:
                    return "❌ Error red"
        
        else:
            # PC: Test SMB REAL
            hostname = config_vars['hostname'].get()
            share = config_vars['share'].get()
            username = config_vars['username'].get()
            
            if not hostname or not share:
                return "❓ Sin configurar"
            
            # Test 1: Ping
            if not self.ping_host(hostname, timeout=3):
                return "❌ Error red"
            
            # Test 2: SMB
            if self.test_smb_connection(hostname, share, username, timeout=3):
                return "✅ Conectado"
            else:
                return "🔐 Error credenciales"
    
    except Exception as e:
        self.log_message(f"❌ Error testing {location_id}: {e}")
        return "❌ Error"

def test_network_path(self, network_path, timeout=3):
    """Test de acceso a ruta de red"""
    try:
        if not network_path or not network_path.startswith('\\\\'):
            return False
        
        # Extraer hostname de la ruta de red
        parts = network_path.replace('\\\\', '').split('\\')
        if len(parts) < 2:
            return False
        
        hostname = parts[0]
        share = parts[1]
        
        # Test ping primero
        if not self.ping_host(hostname, timeout):
            return False
        
        # Test SMB
        return self.test_smb_connection(hostname, share, timeout=timeout)
        
    except Exception as e:
        self.log_message(f"❌ Error test ruta de red {network_path}: {e}")
        return False

class LocationConfigWidget:
    """Widget de configuración que lee desde config.json real"""
    
    def __init__(self, parent, config_manager=None, logger=None):
        self.parent = parent
        self.logger = logger
        
        # Cargar configuración desde JSON real
        self.config_file = os.path.join(os.path.dirname(__file__), "config.json")
        
        # Fallback a src/config/config.json si no existe en raíz
        if not os.path.exists(self.config_file):
            self.config_file = os.path.join(os.path.dirname(__file__), "src", "config", "config.json")
        self.locations_data = {}
        
        # Variables de configuración - SE INICIALIZARÁN EN create_widget()
        self.locations_config = {}
        
        self.create_widget()
        self.load_config_from_json()
    
    def init_location_vars(self):
        """Inicializar variables de ubicaciones"""
        for location_id in ['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4']:
            self.locations_config[location_id] = {
                'enabled': tk.BooleanVar(value=True),
                'network_path': tk.StringVar(),
                'hostname': tk.StringVar(),
                'share': tk.StringVar(),
                'username': tk.StringVar(),
                'password': tk.StringVar(),
                'domain': tk.StringVar(),
                'subfolder': tk.StringVar(),
                'status': tk.StringVar(value="Sin configurar")
            }
    
    def load_config_from_json(self):
        """Cargar configuración desde config.json real"""
        try:
            if not os.path.exists(self.config_file):
                self.log_message(f"❌ No encontrado: {self.config_file}")
                return
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.locations_data = data.get('locations', {})
            
            # Aplicar configuración a variables
            for location_id, config_vars in self.locations_config.items():
                if location_id in self.locations_data:
                    location_config = self.locations_data[location_id]
                    
                    # Configuración básica
                    config_vars['enabled'].set(location_config.get('enabled', True))
                    config_vars['network_path'].set(location_config.get('network_path', ''))
                    config_vars['subfolder'].set(location_config.get('subfolder', ''))
                    
                    # Configuración específica de PCs
                    if not location_id.startswith('qnap'):
                        config_vars['hostname'].set(location_config.get('hostname', ''))
                        config_vars['share'].set(location_config.get('share', ''))
                        config_vars['username'].set(location_config.get('username', ''))
                        config_vars['password'].set(location_config.get('password', ''))
                        config_vars['domain'].set(location_config.get('domain', ''))
            
            self.log_message(f"✅ Configuración cargada desde: {self.config_file}")
            
            # Auto-test conexiones
            self.parent.after(1500, self.test_all_connections)
            
        except Exception as e:
            self.log_message(f"❌ Error cargando config.json: {e}")
    
    def create_widget(self):
        """Crear interfaz simplificada"""
        # PRIMERO: Inicializar variables de ubicaciones (ahora que Tkinter está listo)
        self.init_location_vars()
        
        # Frame principal
        self.main_frame = ttk.LabelFrame(self.parent, text="Configuración de Ubicaciones (desde config.json)", padding="10")
        self.main_frame.pack(fill='both', expand=True, pady=(10,0))
        
        # Información de configuración
        info_frame = ttk.Frame(self.main_frame)
        info_frame.pack(fill='x', pady=(0,10))
        
        ttk.Label(info_frame, text="📁 Configuración cargada desde:", font=('Arial', 9, 'bold')).pack(anchor='w')
        config_path_label = ttk.Label(info_frame, text=self.config_file, font=('Arial', 8), foreground='blue')
        config_path_label.pack(anchor='w', padx=(15,0))
        
        # Grid de estado de ubicaciones
        status_frame = ttk.LabelFrame(self.main_frame, text="Estado de Ubicaciones", padding="10")
        status_frame.pack(fill='both', expand=True)
        
        # Headers
        ttk.Label(status_frame, text="Ubicación", font=("Arial", 10, "bold")).grid(row=0, column=0, padx=5, pady=2, sticky='w')
        ttk.Label(status_frame, text="Estado", font=("Arial", 10, "bold")).grid(row=0, column=1, padx=20, pady=2, sticky='w')
        ttk.Label(status_frame, text="Ruta/Host", font=("Arial", 10, "bold")).grid(row=0, column=2, padx=20, pady=2, sticky='w')
        ttk.Label(status_frame, text="Habilitado", font=("Arial", 10, "bold")).grid(row=0, column=3, padx=20, pady=2, sticky='w')
        
        # Separador
        ttk.Separator(status_frame, orient='horizontal').grid(row=1, column=0, columnspan=4, sticky='ew', pady=5)
        
        # Estado de cada ubicación
        self.status_labels = {}
        self.path_labels = {}
        
        for i, location_id in enumerate(['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4']):
            row = i + 2
            
            # Nombre de ubicación
            name_label = ttk.Label(status_frame, text=location_id.upper(), font=("Arial", 9, "bold"))
            name_label.grid(row=row, column=0, sticky='w', padx=5, pady=2)
            
            # Estado de conexión
            status_label = ttk.Label(status_frame, textvariable=self.locations_config[location_id]['status'], foreground="gray")
            status_label.grid(row=row, column=1, sticky='w', padx=20, pady=2)
            self.status_labels[location_id] = status_label
            
            # Ruta/Host
            if location_id.startswith('qnap'):
                path_text = self.locations_config[location_id]['network_path'].get()
            else:
                hostname = self.locations_config[location_id]['hostname'].get()
                share = self.locations_config[location_id]['share'].get()
                path_text = f"{hostname}/{share}" if hostname and share else "No configurado"
            
            path_label = ttk.Label(status_frame, text=path_text, font=("Arial", 8), foreground="blue")
            path_label.grid(row=row, column=2, sticky='w', padx=20, pady=2)
            self.path_labels[location_id] = path_label
            
            # Checkbox habilitado
            enabled_check = ttk.Checkbutton(status_frame, variable=self.locations_config[location_id]['enabled'])
            enabled_check.grid(row=row, column=3, padx=20, pady=2)
        
        # Botones de control
        button_frame = ttk.Frame(self.main_frame)
        button_frame.pack(fill='x', pady=(10,0))
        
        ttk.Button(button_frame, text="🔄 Actualizar Estado", 
                  command=self.update_status_display).pack(side='left', padx=(0,10))
        ttk.Button(button_frame, text="🧪 Test Conexiones", 
                  command=self.test_all_connections).pack(side='left', padx=(0,10))
        ttk.Button(button_frame, text="📖 Recargar Config", 
                  command=self.load_config_from_json).pack(side='left')
    
    def update_status_display(self):
        """Actualizar display de rutas basado en configuración actual"""
        try:
            for location_id in ['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4']:
                if location_id.startswith('qnap'):
                    path_text = self.locations_config[location_id]['network_path'].get()
                else:
                    hostname = self.locations_config[location_id]['hostname'].get()
                    share = self.locations_config[location_id]['share'].get()
                    path_text = f"{hostname}/{share}" if hostname and share else "No configurado"
                
                self.path_labels[location_id].config(text=path_text)
            
            self.log_message("🔄 Display actualizado")
            
        except Exception as e:
            self.log_message(f"❌ Error actualizando display: {e}")
    
    def test_all_connections(self):
        """Test de todas las conexiones"""
        def test_thread():
            try:
                self.log_message("🧪 Iniciando test de conexiones...")
                
                for location_id in ['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4']:
                    if not self.locations_config[location_id]['enabled'].get():
                        self.locations_config[location_id]['status'].set("⚪ Deshabilitado")
                        continue
                    
                    self.locations_config[location_id]['status'].set("🔄 Testing...")
                    
                    try:
                        if location_id.startswith('qnap'):
                            # Test QNAP
                            network_path = self.locations_config[location_id]['network_path'].get()
                            if network_path and Path(network_path.replace('\\\\', '\\').replace('\\', '/')).exists():
                                self.locations_config[location_id]['status'].set("✅ Conectado")
                                self.log_message(f"✅ {location_id.upper()}: QNAP accesible")
                            else:
                                self.locations_config[location_id]['status'].set("❌ No accesible")
                                self.log_message(f"❌ {location_id.upper()}: Ruta no accesible")
                        else:
                            # Test PC (verificar configuración)
                            hostname = self.locations_config[location_id]['hostname'].get()
                            share = self.locations_config[location_id]['share'].get()
                            username = self.locations_config[location_id]['username'].get()
                            
                            if hostname and share and username:
                                self.locations_config[location_id]['status'].set("✅ Configurado")
                                self.log_message(f"✅ {location_id.upper()}: Configuración SMB completa")
                            else:
                                self.locations_config[location_id]['status'].set("⚠️ Incompleto")
                                self.log_message(f"⚠️ {location_id.upper()}: Configuración SMB incompleta")
                    
                    except Exception as e:
                        self.locations_config[location_id]['status'].set("❌ Error")
                        self.log_message(f"❌ Error testing {location_id}: {e}")
                    
                    time.sleep(0.3)
                
                self.log_message("🧪 Test de conexiones completado")
                
            except Exception as e:
                self.log_message(f"❌ Error en test: {e}")
        
        thread = threading.Thread(target=test_thread)
        thread.daemon = True
        thread.start()
    
    def get_enabled_locations(self):
        """Obtener ubicaciones habilitadas en formato compatible"""
        enabled = {}
        
        for location_id, config_vars in self.locations_config.items():
            if config_vars['enabled'].get():
                enabled[location_id] = {
                    'path': config_vars['network_path'].get(),
                    'subfolder': config_vars['subfolder'].get(),
                    'hostname': config_vars['hostname'].get(),
                    'share': config_vars['share'].get(),
                    'username': config_vars['username'].get(),
                    'password': config_vars['password'].get(),
                    'domain': config_vars['domain'].get()
                }
        
        return enabled
    
    def log_message(self, message):
        """Log message (placeholder)"""
        print(f"[LocationConfig] {message}")

if __name__ == "__main__":
    # Test standalone
    root = tk.Tk()
    root.title("Test - Location Config Widget Fixed")
    root.geometry("800x600")
    
    widget = LocationConfigWidget(root)
    
    root.mainloop()
