"""
GUI Limpieza por Lista - Pieza 4: Preview + Execute
================================================
"""

def preview_list(self):
    """Previsualizar archivos en la lista"""
    if not self.archivo_lista.get():
        messagebox.showwarning("Advertencia", "Debe seleccionar un archivo de lista")
        return
    
    if not os.path.exists(self.archivo_lista.get()):
        messagebox.showerror("Error", "El archivo de lista no existe")
        return
    
    try:
        with open(self.archivo_lista.get(), 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Limpiar líneas
        files = [line.strip() for line in lines if line.strip()]
        
        if not files:
            messagebox.showinfo("Lista Vacía", "El archivo de lista está vacío")
            return
        
        # Mostrar previsualización
        preview_window = tk.Toplevel(self.root)
        preview_window.title("Previsualización de Lista")
        preview_window.geometry("500x400")
        preview_window.resizable(True, True)
        
        frame = ttk.Frame(preview_window, padding="10")
        frame.pack(fill='both', expand=True)
        
        ttk.Label(frame, text=f"Archivos a eliminar ({len(files)} archivos):", 
                 font=("Arial", 12, "bold")).pack(pady=(0,10))
        
        # Lista de archivos
        listbox = tk.Listbox(frame, height=15)
        listbox.pack(fill='both', expand=True, pady=(0,10))
        
        for file in files:
            listbox.insert(tk.END, file)
        
        # Botón cerrar
        ttk.Button(frame, text="Cerrar", 
                  command=preview_window.destroy).pack()
        
    except Exception as e:
        messagebox.showerror("Error", f"Error leyendo archivo: {e}")

def execute_cleanup(self):
    """Ejecutar limpieza por lista"""
    if self.running:
        return
    
    # Validaciones
    if not self.archivo_lista.get():
        messagebox.showerror("Error", "Debe seleccionar un archivo de lista")
        return
    
    if not os.path.exists(self.archivo_lista.get()):
        messagebox.showerror("Error", "El archivo de lista no existe")
        return
    
    # Verificar ubicaciones habilitadas
    enabled_locations = [loc for loc, vars_dict in self.ubicaciones.items() 
                       if vars_dict["enabled"].get()]
    
    if not enabled_locations:
        messagebox.showerror("Error", "Debe habilitar al menos una ubicación")
        return
    
    # Verificar licencia obligatoria
    if not self.licensed:
        messagebox.showerror("Licencia Requerida", 
            "Esta aplicación requiere una licencia válida para funcionar.")
        return
    
    # Confirmar operación
    if not self.dry_run.get():
        response = messagebox.askyesno("Confirmar", 
            f"¿Está seguro de eliminar archivos en {len(enabled_locations)} ubicaciones?\n\n"
            "Esta operación no se puede deshacer.")
        if not response:
            return
    
    # Iniciar ejecución
    self.running = True
    self.execute_btn.config(state='disabled')
    self.stop_btn.config(state='normal')
    self.progress.config(value=0)
    
    # Resetear progreso por ubicación
    for progress_bar in self.location_progress.values():
        progress_bar.config(value=0)
    
    thread = threading.Thread(target=self._execute_cleanup_thread)
    thread.daemon = True
    thread.start()

def stop_cleanup(self):
    """Detener limpieza"""
    if self.running:
        self.running = False
        self.log_message("⏹ Deteniendo limpieza...")

def _reset_ui(self):
    """Restaurar estado de la UI"""
    self.execute_btn.config(state='normal')
    self.stop_btn.config(state='disabled')
    self.progress.config(value=0)
    for progress_bar in self.location_progress.values():
        progress_bar.config(value=0)
