#!/usr/bin/env python3
"""
fix_specific_syntax.py
Corrector específico para errores de sintaxis puntuales

Este script encuentra y corrige errores específicos como:
- Funciones sin contenido
- Bloques vacíos
- Problemas de indentación en líneas específicas

Autor: <PERSON>gson
<PERSON>cha: Junio 2025
"""

from pathlib import Path
from datetime import datetime
import re

def main():
    """Función principal de corrección específica"""
    
    print("🔧 CORRECTOR ESPECÍFICO DE SINTAXIS")
    print("=" * 40)
    
    dashboard_file = Path("dashboard.py")
    
    if not dashboard_file.exists():
        print("❌ ERROR: dashboard.py no encontrado")
        input("Presiona Enter para salir...")
        return False
    
    # Leer contenido línea por línea
    print(f"📖 Analizando línea por línea...")
    
    with open(dashboard_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    print(f"📊 Total líneas: {len(lines)}")
    
    # Encontrar y corregir el error específico en línea 4023
    print(f"🔍 Buscando errores en líneas 4020-4030...")
    
    fixed_lines = []
    i = 0
    
    while i < len(lines):
        line = lines[i]
        line_num = i + 1
        
        # Buscar la línea problemática específica
        if line_num == 4023:
            print(f"📍 Línea {line_num}: {line.strip()}")
            
            # Si es una definición de función vacía
            if line.strip().startswith('def ') and line.strip().endswith(':'):
                fixed_lines.append(line)
                
                # Verificar si la siguiente línea es solo un docstring
                if i + 1 < len(lines):
                    next_line = lines[i + 1]
                    if next_line.strip().startswith('"""'):
                        # Agregar el docstring
                        fixed_lines.append(next_line)
                        i += 1
                        
                        # Buscar el cierre del docstring
                        if i + 1 < len(lines):
                            i += 1
                            while i < len(lines):
                                docstring_line = lines[i]
                                fixed_lines.append(docstring_line)
                                if docstring_line.strip().endswith('"""'):
                                    break
                                i += 1
                        
                        # Agregar el contenido de la función
                        indent = len(line) - len(line.lstrip()) + 4
                        
                        # Si la función es main(), agregar contenido típico
                        if 'def main(' in line:
                            fixed_lines.append(' ' * indent + 'import sys\n')
                            fixed_lines.append(' ' * indent + '\n')
                            fixed_lines.append(' ' * indent + '# Configurar logging básico\n')
                            fixed_lines.append(' ' * indent + 'logging.basicConfig(\n')
                            fixed_lines.append(' ' * indent + '    level=logging.INFO,\n')
                            fixed_lines.append(' ' * indent + '    format=\'%(asctime)s - %(name)s - %(levelname)s - %(message)s\'\n')
                            fixed_lines.append(' ' * indent + ')\n')
                            fixed_lines.append(' ' * indent + '\n')
                            fixed_lines.append(' ' * indent + 'logger = logging.getLogger(\'dashboard.main\')\n')
                            fixed_lines.append(' ' * indent + '\n')
                            fixed_lines.append(' ' * indent + 'try:\n')
                            fixed_lines.append(' ' * indent + '    logger.info("=== INICIANDO GESTOR DE ARCHIVOS CORPORATIVO ===")\n')
                            fixed_lines.append(' ' * indent + '    \n')
                            fixed_lines.append(' ' * indent + '    # Verificar dependencias críticas\n')
                            fixed_lines.append(' ' * indent + '    try:\n')
                            fixed_lines.append(' ' * indent + '        import tkinter\n')
                            fixed_lines.append(' ' * indent + '        import threading\n')
                            fixed_lines.append(' ' * indent + '        from pathlib import Path\n')
                            fixed_lines.append(' ' * indent + '    except ImportError as e:\n')
                            fixed_lines.append(' ' * indent + '        logger.error(f"Dependencia faltante: {e}")\n')
                            fixed_lines.append(' ' * indent + '        print(f"❌ Error: Dependencia faltante - {e}")\n')
                            fixed_lines.append(' ' * indent + '        input("Presione Enter para salir...")\n')
                            fixed_lines.append(' ' * indent + '        sys.exit(1)\n')
                            fixed_lines.append(' ' * indent + '    \n')
                            fixed_lines.append(' ' * indent + '    # Crear y ejecutar dashboard\n')
                            fixed_lines.append(' ' * indent + '    dashboard = GestorDashboard()\n')
                            fixed_lines.append(' ' * indent + '    dashboard.run()\n')
                            fixed_lines.append(' ' * indent + '    \n')
                            fixed_lines.append(' ' * indent + 'except KeyboardInterrupt:\n')
                            fixed_lines.append(' ' * indent + '    logger.info("Aplicación interrumpida por el usuario (Ctrl+C)")\n')
                            fixed_lines.append(' ' * indent + '    print("\\n🛑 Aplicación detenida por el usuario")\n')
                            fixed_lines.append(' ' * indent + '    \n')
                            fixed_lines.append(' ' * indent + 'except Exception as e:\n')
                            fixed_lines.append(' ' * indent + '    logger.error(f"Error fatal en main: {e}")\n')
                            fixed_lines.append(' ' * indent + '    print(f"❌ Error fatal: {e}")\n')
                            fixed_lines.append(' ' * indent + '    \n')
                            fixed_lines.append(' ' * indent + '    # Mostrar información de debug si es necesario\n')
                            fixed_lines.append(' ' * indent + '    if "--debug" in sys.argv:\n')
                            fixed_lines.append(' ' * indent + '        import traceback\n')
                            fixed_lines.append(' ' * indent + '        print("\\n🔍 Información de debug:")\n')
                            fixed_lines.append(' ' * indent + '        traceback.print_exc()\n')
                            fixed_lines.append(' ' * indent + '    \n')
                            fixed_lines.append(' ' * indent + '    input("Presione Enter para salir...")\n')
                            fixed_lines.append(' ' * indent + '    sys.exit(1)\n')
                        else:
                            # Para otras funciones, agregar pass
                            fixed_lines.append(' ' * indent + 'pass\n')
                    else:
                        # No hay docstring, agregar pass directamente
                        indent = len(line) - len(line.lstrip()) + 4
                        fixed_lines.append(' ' * indent + 'pass\n')
                        fixed_lines.append(next_line)
                        i += 1
                else:
                    # No hay línea siguiente, agregar pass
                    indent = len(line) - len(line.lstrip()) + 4
                    fixed_lines.append(' ' * indent + 'pass\n')
            else:
                fixed_lines.append(line)
        else:
            fixed_lines.append(line)
        
        i += 1
    
    # Verificar que se agregó el bloque if __name__ == "__main__" al final
    print(f"🔍 Verificando bloque principal...")
    
    # Buscar si ya existe el bloque if __name__
    has_main_block = any('if __name__ == "__main__"' in line for line in fixed_lines)
    
    if not has_main_block:
        print(f"➕ Agregando bloque if __name__ == '__main__'...")
        fixed_lines.append('\n')
        fixed_lines.append('if __name__ == "__main__":\n')
        fixed_lines.append('    main()\n')
    
    # Escribir archivo corregido
    print(f"💾 Escribiendo archivo corregido...")
    
    # Hacer backup
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = Path(f"dashboard_antes_fix_especifico_{timestamp}.py")
    backup_file.write_text(''.join(lines), encoding='utf-8')
    print(f"💾 Backup específico: {backup_file.name}")
    
    # Escribir versión corregida
    with open(dashboard_file, 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    # Estadísticas
    new_size = dashboard_file.stat().st_size / 1024
    new_lines = len(fixed_lines)
    
    print(f"\n🎉 ¡CORRECCIÓN ESPECÍFICA COMPLETADA!")
    print(f"=" * 40)
    print(f"📏 Tamaño final: {new_size:.1f} KB")
    print(f"📊 Líneas finales: {new_lines:,}")
    print(f"🔧 Líneas agregadas: {new_lines - len(lines):+,}")
    
    # Verificación final
    print(f"\n🔍 Verificación final...")
    if verify_syntax(dashboard_file):
        print(f"  ✅ ¡Sintaxis completamente correcta!")
        print(f"  ✅ Archivo listo para ejecutar")
        
        print(f"\n🚀 ¡LISTO PARA USAR!")
        print(f"  Ejecuta: python dashboard.py")
        
        return True
    else:
        print(f"  ⚠️  Revisar manualmente")
        return False

def verify_syntax(file_path: Path) -> bool:
    """Verificar que la sintaxis sea correcta"""
    try:
        content = file_path.read_text(encoding='utf-8')
        compile(content, str(file_path), 'exec')
        return True
    except SyntaxError as e:
        print(f"    ❌ Error de sintaxis: {e}")
        print(f"    📍 Línea {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"    ❌ Error verificando: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        input("\nPresiona Enter para continuar...")
        if not success:
            exit(1)
    except KeyboardInterrupt:
        print(f"\n\n🛑 Operación cancelada")
        exit(1)
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        input("Presiona Enter para salir...")
        exit(1)
