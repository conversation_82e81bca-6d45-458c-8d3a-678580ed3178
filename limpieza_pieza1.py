"""
GUI Limpieza por Lista - Pieza 1: Base + Header
==============================================
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
from datetime import datetime
import threading
import time
from pathlib import Path

# Agregar el directorio actual al path para imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Imports del sistema
try:
    from gestor_license_manager import GestorLicenseManager, show_license_dialog
    from src.core.config_manager import ConfigManager
    from src.core.logger import LoggerManager
    from src.core.utils import create_test_environment
except ImportError as e:
    print(f"Warning: Some modules not found: {e}")
    print("Running in standalone mode...")

class CleanupByListGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Gestor de Archivos - Limpieza por Lista")
        self.root.geometry("900x750")
        self.root.resizable(True, True)
        
        # Configuración inicial
        self.licensed = False
        self.running = False
        
        # Variables de configuración
        self.archivo_lista = tk.StringVar()
        self.dry_run = tk.BooleanVar(value=True)
        
        # Variables de ubicaciones (6 ubicaciones: 2 QNAPs + 4 PCs)
        self.ubicaciones = {
            "QNAP1": {"enabled": tk.BooleanVar(value=True), "path": tk.StringVar()},
            "QNAP2": {"enabled": tk.BooleanVar(value=True), "path": tk.StringVar()},
            "PC1": {"enabled": tk.BooleanVar(value=True), "path": tk.StringVar()},
            "PC2": {"enabled": tk.BooleanVar(value=True), "path": tk.StringVar()},
            "PC3": {"enabled": tk.BooleanVar(value=True), "path": tk.StringVar()},
            "PC4": {"enabled": tk.BooleanVar(value=True), "path": tk.StringVar()}
        }
        
        # Configurar logging
        try:
            self.logger = LoggerManager("limpieza_gui").get_logger()
        except:
            import logging
            logging.basicConfig(level=logging.INFO)
            self.logger = logging.getLogger("limpieza_gui")
        
        self.setup_ui()
        self.check_license()
        self.load_default_config()
    
    def setup_header(self, parent):
        """Configurar header con logo IGSON"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill='x', pady=(0,10))
        
        # Intentar cargar logo IGSON
        try:
            from PIL import Image, ImageTk
            logo_path = os.path.join(current_dir, "logo_igson.png")
            if os.path.exists(logo_path):
                img = Image.open(logo_path)
                img = img.resize((80, 80), Image.Resampling.LANCZOS)
                self.logo_photo = ImageTk.PhotoImage(img)
                
                logo_label = ttk.Label(header_frame, image=self.logo_photo)
                logo_label.pack(side='left', padx=(0,15))
            else:
                # Logo de texto si no hay imagen
                ttk.Label(header_frame, text="🏢", font=("Arial", 40)).pack(side='left', padx=(0,15))
        except ImportError:
            # Logo de texto si no hay PIL
            ttk.Label(header_frame, text="🏢", font=("Arial", 40)).pack(side='left', padx=(0,15))
        
        # Información de la aplicación
        info_frame = ttk.Frame(header_frame)
        info_frame.pack(side='left', fill='both', expand=True)
        
        ttk.Label(info_frame, text="IGSON - Gestor de Archivos Corporativo", 
                 font=("Arial", 16, "bold")).pack(anchor='w')
        ttk.Label(info_frame, text="Limpieza por Lista", 
                 font=("Arial", 12)).pack(anchor='w')
        ttk.Label(info_frame, text="Versión 1.0 - Junio 2025", 
                 font=("Arial", 9), foreground="gray").pack(anchor='w')
