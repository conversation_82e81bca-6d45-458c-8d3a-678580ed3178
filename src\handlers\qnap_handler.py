"""
Gestor de Archivos Corporativo - QNAP Handler v2.3
==================================================

v2.3 - Julio 2025:
- ✅ Soporte para extensiones configurables 
- ✅ Validación de archivos según whitelist/blacklist
- ✅ Logging de archivos rechazados por extensión
- ✅ Búsqueda recursiva con filtros de extensión
"""

import os
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
import logging

class QnapHandler:
    """Handler especializado para dispositivos QNAP v2.3"""
    
    def __init__(self, location_id: str, config_manager=None):
        self.location_id = location_id
        self.device_type = "QNAP"
        self.logger = logging.getLogger('gestor.conectividad')
        self.current_path = None
        self.location_config = {}
        
        # v2.3 - Configuración de extensiones
        self.config_manager = config_manager
        
        self.logger.info(f"Inicializado QNAP Handler v2.3 para {location_id}")
    
    def connect(self) -> bool:
        """Conectar a QNAP (simulado)"""
        try:
            # Simulación de conexión exitosa
            self.current_path = Path(f"M:\\{self.location_id}")
            return True
        except Exception as e:
            self.logger.error(f"Error conectando a QNAP {self.location_id}: {e}")
            return False
    
    def is_connected(self) -> bool:
        """Verificar si está conectado"""
        return self.current_path is not None
    
    # ===========================================
    # MÉTODOS v2.3 - EXTENSIONES CONFIGURABLES
    # ===========================================
    
    def _is_file_extension_allowed(self, filename: str) -> Tuple[bool, str]:
        """Valida si un archivo está permitido según configuración de extensiones v2.3"""
        if not self.config_manager:
            # Sin config manager, usar comportamiento v2.2 (solo .mov y .mxf)
            file_ext = Path(filename).suffix.lower()
            allowed = file_ext in ['.mov', '.mxf', '.mp4', '.avi']
            reason = f"Legacy mode: {file_ext} {'permitido' if allowed else 'no permitido'}"
            return allowed, reason
        
        try:
            allowed = self.config_manager.is_file_extension_allowed(filename)
            status = self.config_manager.get_extension_status(filename)
            return allowed, status['reason']
        except Exception as e:
            self.logger.error(f"Error validando extensión para {filename}: {e}")
            return False, f"Error de validación: {e}"
    
    def _get_search_extensions(self) -> List[str]:
        """Obtiene lista de extensiones para búsqueda automática"""
        if not self.config_manager:
            return ['.mov', '.mxf']  # Comportamiento legacy
        
        try:
            mode = self.config_manager.get_extension_mode()
            
            if mode == "whitelist":
                return self.config_manager.get_whitelist_extensions()
            elif mode == "all":
                return ['.mov', '.mxf', '.mp4', '.avi']
            else:  # blacklist
                common_video = ['.mov', '.mxf', '.mp4', '.avi', '.mkv', '.wmv']
                blacklist = self.config_manager.get_blacklist_extensions()
                return [ext for ext in common_video if ext not in blacklist]
        except Exception as e:
            self.logger.error(f"Error obteniendo extensiones de búsqueda: {e}")
            return ['.mov', '.mxf']
    
    def check_file_exists(self, filename: str, target_folder: str = "", recursive: bool = True) -> Tuple[bool, Optional[Path], str]:
        """Verifica si un archivo existe en el QNAP (v2.3 con validación de extensiones)"""
        if not self.is_connected():
            return False, None, f"No conectado a QNAP {self.location_id}"
        
        try:
            search_path = self.current_path
            if target_folder:
                search_path = self.current_path / target_folder
            
            self.logger.debug(f"Buscando {filename} en {search_path} (recursivo: {recursive})")
            
            # Lista de patrones a buscar
            search_patterns = [filename]
            
            # Si no tiene extensión, agregar extensiones permitidas
            if '.' not in filename:
                search_extensions = self._get_search_extensions()
                for ext in search_extensions:
                    search_patterns.append(f"{filename}{ext}")
            
            # Buscar cada patrón (simulado)
            for pattern in search_patterns:
                # Simulación de archivo encontrado
                if pattern.endswith(('.mov', '.mxf')):
                    simulated_path = search_path / pattern
                    
                    # Validar extensiones v2.3
                    allowed, reason = self._is_file_extension_allowed(pattern)
                    
                    if allowed:
                        status_msg = f"Archivo encontrado y permitido: {pattern} ({reason})"
                        self.logger.info(status_msg)
                        return True, simulated_path, status_msg
                    else:
                        status_msg = f"Archivo encontrado pero bloqueado: {pattern} ({reason})"
                        self.logger.warning(status_msg)
                        return False, simulated_path, status_msg
            
            return False, None, f"Archivo {filename} no encontrado en {self.location_id}"
                
        except Exception as e:
            error_msg = f"Error verificando archivo {filename} en QNAP: {e}"
            self.logger.error(error_msg)
            return False, None, error_msg
    
    def delete_file_from_qnap(self, file_path: Union[str, Path], safe_mode: bool = True) -> Tuple[bool, str]:
        """Elimina un archivo del QNAP de forma segura (v2.3 con validación de extensiones)"""
        if not self.is_connected():
            return False, f"No conectado a QNAP {self.location_id}"
        
        try:
            file_obj = Path(file_path) if isinstance(file_path, str) else file_path
            
            # v2.3 - Validación de extensión antes de eliminar
            allowed, reason = self._is_file_extension_allowed(file_obj.name)
            if not allowed:
                status_msg = f"Archivo bloqueado por extensión: {file_obj.name} ({reason})"
                self.logger.warning(status_msg)
                return False, status_msg
            
            # Log antes de eliminar
            self.logger.info(f"Eliminando archivo de QNAP {self.location_id}: {file_obj}")
            self.logger.info(f"Extensión validada: {reason}")
            
            # Simulación de eliminación exitosa
            message = f"Archivo eliminado exitosamente: {file_obj.name}"
            self.logger.info(f"✓ {message}")
            return True, message
            
        except Exception as e:
            error_msg = f"Error eliminando archivo de QNAP: {e}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def bulk_delete_files(self, file_list: List[str], target_folder: str = "", recursive: bool = True) -> Dict:
        """Elimina múltiples archivos de forma eficiente (v2.3 con validación de extensiones)"""
        stats = {
            'total_files': len(file_list),
            'found_files': 0,
            'deleted_files': 0,
            'failed_files': 0,
            'blocked_by_extension': 0,  # v2.3
            'errors': [],
            'deleted_list': [],
            'not_found_list': [],
            'blocked_list': []  # v2.3
        }
        
        if not self.is_connected():
            stats['errors'].append(f"No conectado a QNAP {self.location_id}")
            return stats
        
        self.logger.info(f"Iniciando eliminación masiva v2.3 en QNAP {self.location_id}: {len(file_list)} archivos")
        if self.config_manager:
            ext_config = self.config_manager.get_file_extensions_config()
            self.logger.info(f"Configuración extensiones: modo={ext_config['mode']}")
        
        for filename in file_list:
            try:
                # Buscar archivo (con validación de extensiones incluida)
                exists, file_path, status_msg = self.check_file_exists(filename, target_folder, recursive)
                
                if exists and file_path:
                    stats['found_files'] += 1
                    
                    # Verificar si está permitido (ya validado en check_file_exists)
                    allowed, reason = self._is_file_extension_allowed(file_path.name)
                    
                    if allowed:
                        success, message = self.delete_file_from_qnap(file_path, safe_mode=True)
                        
                        if success:
                            stats['deleted_files'] += 1
                            try:
                                rel_path = file_path.relative_to(self.current_path)
                                stats['deleted_list'].append(f"{filename} -> {rel_path}")
                            except:
                                stats['deleted_list'].append(f"{filename} -> {file_path}")
                        else:
                            stats['failed_files'] += 1
                            stats['errors'].append(f"{filename}: {message}")
                    else:
                        stats['blocked_by_extension'] += 1
                        stats['blocked_list'].append(f"{filename} -> {reason}")
                        self.logger.warning(f"Archivo bloqueado: {filename} ({reason})")
                else:
                    stats['not_found_list'].append(filename)
                    if "bloqueado" in status_msg.lower():
                        stats['blocked_by_extension'] += 1
                        stats['blocked_list'].append(f"{filename} -> {status_msg}")
                
            except Exception as e:
                stats['failed_files'] += 1
                stats['errors'].append(f"{filename}: {e}")
        
        # Log resumen v2.3
        self.logger.info(f"Eliminación masiva v2.3 completada en QNAP {self.location_id}:")
        self.logger.info(f"  - Archivos encontrados: {stats['found_files']}")
        self.logger.info(f"  - Archivos eliminados: {stats['deleted_files']}")
        self.logger.info(f"  - Archivos fallidos: {stats['failed_files']}")
        self.logger.info(f"  - Archivos bloqueados por extensión: {stats['blocked_by_extension']}")
        self.logger.info(f"  - Archivos no encontrados: {len(stats['not_found_list'])}")
        
        return stats

if __name__ == "__main__":
    # Test v2.3 del QNAP Handler
    print("🗄️  QNAP Handler v2.3 Test - Extensiones Configurables")
    print("=" * 55)
    
    # Configurar logging básico para test
    import logging
    logging.basicConfig(level=logging.INFO)
    
    try:
        # Test con ubicación QNAP (será simulado)
        qnap_handler = QnapHandler("qnap1")
        print(f"✓ QNAP Handler v2.3 creado para: qnap1")
        
        # Test validación de extensiones (sin config manager)
        test_files = ["video.mov", "archivo.txt", "programa.exe"]
        print("\n🔍 Test Validación Extensiones (Legacy Mode):")
        for filename in test_files:
            allowed, reason = qnap_handler._is_file_extension_allowed(filename)
            symbol = "✅" if allowed else "❌"
            print(f"  {symbol} {filename} - {reason}")
        
        # Test extensiones de búsqueda
        extensions = qnap_handler._get_search_extensions()
        print(f"\n📋 Extensiones de búsqueda: {extensions}")
        
        print("\n✅ QNAP Handler v2.3 test completado")
        
    except Exception as e:
        print(f"❌ Error en test: {e}")
