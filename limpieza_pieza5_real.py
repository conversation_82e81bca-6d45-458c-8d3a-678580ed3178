def _execute_cleanup_thread(self):
        """Hilo de ejecución de limpieza FASE 2 - Búsqueda múltiples extensiones
        
        NUEVA FUNCIONALIDAD:
        - Por cada nombre base en lista → buscar .mxf y .mov
        - Estadísticas separadas por extensión
        - Reporte detallado por tipo de archivo
        """
        try:
            self.log_message("🗑️ Iniciando limpieza por lista FASE 2 - Videos .mxf/.mov...")
            self.log_message(f"📁 Archivo de lista: {self.archivo_lista.get()}")
            self.log_message(f"🔧 Modo: {'Simulación' if self.dry_run.get() else 'Real'}")
            
            # Leer archivo de lista
            with open(self.archivo_lista.get(), 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # Procesar nombres base válidos
            valid_basenames = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#') and self.is_valid_video_basename(line):
                    valid_basenames.append(line)
            
            if not valid_basenames:
                self.log_message("❌ No hay nombres base válidos en la lista")
                return
            
            # Generar lista completa de archivos objetivo
            all_target_files = []
            basename_to_targets = {}
            
            for basename in valid_basenames:
                target_files = self.get_target_files_for_basename(basename)
                all_target_files.extend(target_files)
                basename_to_targets[basename] = target_files
            
            self.log_message(f"📋 Nombres base a procesar: {len(valid_basenames)}")
            self.log_message(f"🎬 Archivos objetivo totales: {len(all_target_files)}")
            
            # Obtener ubicaciones habilitadas
            enabled_locations = self.location_widget.get_enabled_locations()
            self.log_message(f"🏢 Ubicaciones habilitadas: {', '.join(enabled_locations.keys())}")
            
            # Inicializar handlers
            location_handlers = {}
            for location_id, config in enabled_locations.items():
                try:
                    if location_id.startswith('qnap'):
                        handler = QnapHandler(location_id)
                        search_path = config['path']
                        if config['subfolder']:
                            search_path = os.path.join(search_path, config['subfolder'])
                    else:
                        handler = PCHandler(location_id)
                        search_path = f"\\\\{config.get('hostname', 'localhost')}\\{config.get('share', 'shared')}"
                        if config['subfolder']:
                            search_path = os.path.join(search_path, config['subfolder'])
                    
                    location_handlers[location_id] = {
                        'handler': handler,
                        'path': search_path,
                        'config': config
                    }
                    
                    self.log_message(f"🔌 Handler configurado para {location_id.upper()}: {search_path}")
                    
                except Exception as e:
                    self.log_message(f"❌ Error configurando handler {location_id}: {e}")
            
            # Estadísticas FASE 2 - Con separación por extensión
            stats = {
                'total_basenames': len(valid_basenames),
                'total_target_files': len(all_target_files),
                'total_locations': len(location_handlers),
                'files_found': 0,
                'files_deleted': 0,
                'files_failed': 0,
                'extension_stats': {'.mxf': {'found': 0, 'deleted': 0, 'failed': 0},
                                  '.mov': {'found': 0, 'deleted': 0, 'failed': 0}},
                'location_stats': {}
            }
            
            # Inicializar estadísticas por ubicación
            for location_id in location_handlers.keys():
                stats['location_stats'][location_id] = {
                    'found': 0, 'deleted': 0, 'failed': 0, 'errors': [],
                    'mxf_found': 0, 'mov_found': 0
                }
            
            self.log_message("=" * 60)
            self.log_message("🚀 INICIANDO OPERACIÓN DE LIMPIEZA FASE 2")
            self.log_message("=" * 60)
            
            # Procesar cada nombre base
            total_operations = len(valid_basenames) * len(location_handlers) * 2  # 2 extensiones por basename
            current_operation = 0
            
            for basename_index, basename in enumerate(valid_basenames):
                if not self.running:
                    break
                
                self.log_message(f"🔍 Procesando nombre base {basename_index + 1}/{len(