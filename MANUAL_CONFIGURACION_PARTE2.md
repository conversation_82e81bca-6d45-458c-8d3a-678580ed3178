# Manual de Configuración de Conexiones - PARTE 2
## Gestor de Archivos Corporativo

*CONTINUACIÓN DE PARTE 1...*

---

## 🔄 Sistema de Failover y Detección

### Método Híbrido de Conexión

```python
# Flujo de conexión
1. Intenta método primario (symlink o network)
   ↓ Si falla ↓
2. Intenta método secundario (network o symlink)  
   ↓ Si falla ↓
3. Marca ubicación como "Sin conexión"
```

### Prioridades Recomendadas

#### Para QNAP (NAS)
```json
"connection_priority": ["symlink", "network"]
```

#### Para PCs
```json
"connection_priority": ["network", "symlink"]
```

---

## 🚀 Casos de Uso Específicos

### Caso 1: NAS Synology como QNAP

**Configuración Synology DSM:**
```
Panel de Control > Compartir archivos > Carpeta compartida
- Nombre: datos
- Ubicación: /volume1/datos
```

**GUI Configuration:**
```
Hostname/IP: *************
Share: datos           # ← Nombre de la carpeta compartida
Usuario: admin
Contraseña: [synology_password]
Dominio: WORKGROUP
Subcarpeta: produccion\2025
```

### Caso 2: Servidor CentOS/RHEL

**Configuración smb.conf:**
```ini
[enterprise]           # ← Share name
path = /opt/enterprise_data
valid users = @enterprise_group
```

**GUI Configuration:**
```
Hostname/IP: centos-server.empresa.com
Share: enterprise      # ← Nombre del [enterprise]
Usuario: enterprise_user
Dominio: EMPRESA.LOCAL
Subcarpeta: reports\quarterly
```

### Caso 3: FreeNAS/TrueNAS

**Configuración TrueNAS:**
```
Sharing > Windows (SMB) Shares
- Name: backup         # ← Este nombre va en Share
- Path: /mnt/pool1/backup
```

**GUI Configuration:**
```
Hostname/IP: truenas.local
Share: backup          # ← Nombre del share TrueNAS
Usuario: backup_user
Dominio: WORKGROUP  
Subcarpeta: daily\logs
```

---

## 🔗 Integración con Active Directory

### Configuración Samba con AD

```ini
# /etc/samba/smb.conf
[global]
workgroup = EMPRESA
realm = EMPRESA.LOCAL
security = ads
password server = dc1.empresa.local

[profiles]             # ← Share name
path = /srv/profiles
read only = no
profile acls = yes
```

**Comandos de Configuración AD:**
```bash
sudo net ads join -U Administrator
sudo systemctl enable winbind
sudo systemctl start winbind
```

**GUI Configuration para AD:**
```
Hostname/IP: file-server.empresa.local
Share: profiles        # ← Nombre del [profiles]
Usuario: EMPRESA\administrador.sistemas
Dominio: EMPRESA.LOCAL
Subcarpeta: users\%username%
```

---

## 📝 Ejemplos de Configuración Completa

### Escenario 1: Empresa Pequeña (2 QNAP + 2 PCs)

```json
{
  "locations": {
    "qnap1": {
      "name": "QNAP Principal",
      "network_path": "\\\\*************\\datos",
      "symlink_path": "Q:\\",
      "enabled": true,
      "connection_priority": ["symlink", "network"],
      "credentials": {
        "username": "admin",
        "password_encrypted": "gAAAAABh_qnap_password",
        "domain": "WORKGROUP"
      }
    },
    "pc1": {
      "name": "PC Producción",
      "network_path": "\\\\PC-PROD\\shared",
      "symlink_path": "C:\\Links\\pc1",
      "enabled": true,
      "connection_priority": ["network", "symlink"],
      "credentials": {
        "username": "operador",
        "password_encrypted": "gAAAAABh_pc_password",
        "domain": ""
      }
    }
  }
}
```

### Escenario 2: Configuración Samba + Windows

```json
{
  "locations": {
    "ubuntu_server": {
      "name": "Ubuntu File Server",
      "network_path": "\\\\************\\shared",
      "symlink_path": "U:\\",
      "enabled": true,
      "connection_priority": ["symlink", "network"],
      "credentials": {
        "username": "operador",
        "password_encrypted": "gAAAAABh_ubuntu_password",
        "domain": "WORKGROUP"
      }
    }
  }
}
```

---

## 🧪 Testing y Verificación

### Test de Conectividad Individual

**Dashboard de Conectividad:**
- ✅ Verde: Conectado
- ❌ Rojo: Error
- ⚪ Gris: Deshabilitado
- ❓ Amarillo: Sin configurar

### Tests desde Línea de Comandos

```cmd
# Test SMB Manual
net use \\*************\share /user:admin password
net view \\*************

# Test de Symlinks
dir C:\Links\qnap1
echo test > C:\Links\qnap1\test.txt
```

---

## 🚨 Troubleshooting

### Problemas Comunes

#### 1. **Error "Acceso Denegado"**
```
Causa: Credenciales incorrectas
Solución:
- Verificar usuario/contraseña
- Comprobar permisos del share
- Probar acceso manual: \\servidor\share
```

#### 2. **"No se puede encontrar la ruta de red"**
```
Causa: Problema de conectividad
Solución:
- ping *************
- telnet ************* 445
- Usar IP en lugar de nombre
```

#### 3. **Symlinks No Funcionan**
```
Causa: Permisos insuficientes
Solución:
- Ejecutar como Administrador
- Verificar que el target existe
- Recrear el symlink
```

### Comandos de Diagnóstico

```cmd
# Verificar conectividad
ping qnap1
telnet qnap1 445

# Listar shares
net view \\qnap1

# Test autenticación
net use \\qnap1\share /user:admin
```

---

## 🛠️ Configuración de Operaciones

### Validación de Duplicados

```json
"operations": {
  "validacion_duplicados": {
    "carpeta_origen": "C:\\Temp\\archivos_origen",
    "carpeta_rechazados": "C:\\Temp\\rechazados",
    "qnaps_a_revisar": ["qnap1", "qnap2"],
    "enabled": true
  }
}
```

### Limpieza por Lista

```json
"operations": {
  "limpieza_lista": {
    "ubicaciones_limpieza": ["qnap1", "qnap2", "pc1", "pc2", "pc3", "pc4"],
    "carpetas_objetivo": {
      "qnap1": "\\datos\\produccion",
      "qnap2": "\\backup\\files",
      "pc1": "\\shared\\work"
    },
    "enabled": true
  }
}
```

---

## 📋 Plantillas de Configuración SMB

### Plantilla Básica Ubuntu Server

```ini
# /etc/samba/smb.conf - Configuración básica
[global]
workgroup = WORKGROUP
server string = File Server
security = user

[shared]               # ← Share name
comment = Shared Files
path = /srv/shared
valid users = operador, admin
read only = no
```

**Correspondencia GUI:**
```
Hostname/IP: [IP_DEL_SERVIDOR]
Share: shared          # ← Nombre del [shared]
Usuario: operador
Dominio: WORKGROUP
```

### Comandos de Configuración Samba

```bash
# Crear usuario Samba
sudo smbpasswd -a operador

# Reiniciar servicio
sudo systemctl restart smbd

# Verificar configuración
testparm

# Test desde cliente
smbclient //servidor/shared -U operador
```

---

## 🔐 Gestión de Credenciales

### Encriptación de Contraseñas

```python
# La aplicación maneja automáticamente:
"password_encrypted": "gAAAAABh_abc123_encrypted_password"
```

### Configuración por Interfaz

1. Abrir aplicación
2. Ir a "Configuración" 
3. Seleccionar ubicación
4. Introducir credenciales

### Credenciales por Tipo

#### QNAP NAS
```json
"credentials": {
  "username": "admin",
  "password_encrypted": "...",
  "domain": "WORKGROUP"
}
```

#### PC con Dominio
```json
"credentials": {
  "username": "DOMINIO\\usuario",
  "password_encrypted": "...",
  "domain": "EMPRESA.LOCAL"
}
```

---

## 🔍 Detección Automática de QNAPs

### Auto-Discovery

```json
"auto_discovery": {
  "enabled": true,
  "scan_range": "***********-254",
  "qnap_ports": [80, 443, 8080],
  "timeout_per_host": 2
}
```

### Protocolos de Detección

1. **Puerto HTTP** - Busca servicios QNAP
2. **Banner SMB** - Identifica comparticiones QNAP  
3. **MDNS/Bonjour** - Busca servicios _qnap._tcp

---

## 📞 Soporte Técnico

### Información de Contacto
- **Desarrollador**: IGSON
- **Versión**: 2.0
- **Fecha**: Junio 2025

### Logs para Soporte

```
logs/sistema_YYYYMMDD.log
logs/conectividad_YYYYMMDD.log
config.json (sin contraseñas)
```

### Comandos de Diagnóstico

```cmd
# Estado del sistema
systeminfo | findstr /B /C:"OS"
ipconfig /all

# Test de red
nslookup qnap1
ping -t *************

# Servicios SMB
sc query lanmanserver
sc query lanmanworkstation
```

---

## 📚 Referencias

### Documentación
- [Samba Official Documentation](https://www.samba.org/samba/docs/)
- [Active Directory Integration](https://wiki.samba.org/index.php/Setting_up_Samba_as_a_Domain_Member)

### Herramientas
- **smbclient**: Cliente SMB para testing
- **testparm**: Validador configuración Samba
- **net**: Herramientas de red Windows
- **wbinfo**: Información Winbind (AD)

### Puertos SMB
- **445**: SMB directo sobre TCP
- **139**: NetBIOS Session Service
- **138**: NetBIOS Datagram Service  
- **137**: NetBIOS Name Service

---

*Manual de Configuración v2.0 - Gestor de Archivos Corporativo*  
*© 2025 IGSON - Todos los derechos reservados*