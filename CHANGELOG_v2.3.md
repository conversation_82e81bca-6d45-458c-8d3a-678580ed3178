# CHANGELOG VERSIÓN 2.3 - JULIO 2025

## Gestor de Archivos Corporativo - Limpieza por Lista

### 🎯 NUEVA FUNCIONALIDAD PRINCIPAL: EXTENSIONES CONFIGURABLES

**Cambio mayor:** Implementación completa de sistema de extensiones configurables con whitelist/blacklist.

### 🔧 CAMBIOS IMPLEMENTADOS v2.3

**✅ ConfigManager v2.3:**
- **Configuración de extensiones** - Nuevos métodos para gestionar whitelist/blacklist
- **3 modos de validación** - `whitelist`, `blacklist`, `all`
- **Validación automática** - `is_file_extension_allowed(filename)` 
- **Status detallado** - `get_extension_status(filename)` con razones
- **Configuración por defecto** - Whitelist con `.mov`, `.mxf`, `.mp4`, `.avi`

**✅ Handlers v2.3:**
- **QnapHandler actualizado** - Validación de extensiones antes de eliminar
- **PCHandler actualizado** - Soporte SMB con filtros de extensión
- **Búsqueda inteligente** - `_get_search_extensions()` usa configuración
- **Bulk operations** - Estadísticas separadas para archivos bloqueados
- **Logging mejorado** - Razones detalladas de bloqueo por extensión

**✅ GUI v2.3:**
- **Pestaña Extensiones** - Configuración visual completa
- **3 modos visuales** - Radiobuttons para whitelist/blacklist/all
- **Gestión de listas** - Agregar/quitar extensiones fácilmente
- **Prueba en vivo** - Test de validación de archivos
- **Status en tiempo real** - Información de configuración activa
- **Versión en header** - Muestra v2.3 y modo actual

**✅ Procesamiento v2.3:**
- **Thread principal** - Análisis previo de extensiones en lista
- **Dual processing** - Handlers v2.3 + fallback legacy
- **Estadísticas separadas** - `blocked_by_extension` independiente
- **Reporte final** - Sección específica para archivos bloqueados
- **Tips contextuales** - Sugerencias sobre configuración

### 📋 FUNCIONALIDAD COMPLETA

**Configuración de Extensiones:**
```json
{
  "file_extensions": {
    "mode": "whitelist",
    "whitelist": [".mov", ".mxf", ".mp4", ".avi"],
    "blacklist": [".exe", ".dll", ".sys", ".bat"],
    "case_sensitive": false
  }
}
```

**Flujo de Validación:**
1. Usuario selecciona archivo de lista
2. Sistema analiza extensiones según configuración
3. Preview muestra archivos permitidos vs bloqueados
4. Durante limpieza: validación automática
5. Estadísticas separadas por tipo de rechazo

**Modos de Operación:**
- **Whitelist**: Solo archivos con extensiones específicas
- **Blacklist**: Todos excepto extensiones bloqueadas
- **All**: Permitir todas las extensiones

### 🧪 TESTING Y VALIDACIÓN

**Test Cases v2.3:**
- ✅ ConfigManager carga configuración por defecto
- ✅ Validación de archivos según modo activo
- ✅ Handlers respetan configuración de extensiones
- ✅ UI actualiza en tiempo real
- ✅ Estadísticas separadas funcionan correctamente
- ✅ Logging detallado de bloqueos

**Escenarios de Prueba:**
```
Lista: ["video1", "documento.txt", "programa.exe"]
Modo Whitelist [.mov, .mxf]:
- video1 → busca video1.mov → ✅ permitido
- documento.txt → ❌ bloqueado (no en whitelist)
- programa.exe → ❌ bloqueado (no en whitelist)

Modo Blacklist [.exe, .dll]:
- video1 → busca video1.mov → ✅ permitido
- documento.txt → ✅ permitido (no en blacklist)
- programa.exe → ❌ bloqueado (en blacklist)
```

### 📦 COMPILACIÓN v2.3

**Archivo generado:** `LimpiezaPorLista_v2.3.exe`
**Ubicación:** `dist/`
**Características:**
- Configuración de extensiones integrada
- UI completa de gestión
- Validación automática
- Estadísticas avanzadas

**Comando de compilación:**
```bash
pyinstaller --onefile --windowed --add-data "src;src" --name "LimpiezaPorLista_v2.3" limpieza_lista_gui_real.py
```

### ⚡ MEJORAS DE RENDIMIENTO v2.3

- **Validación early-exit**: Se detiene al primer archivo válido
- **Configuración cacheada**: No re-lee config en cada archivo
- **Bulk processing**: Operaciones masivas optimizadas
- **Logging selectivo**: Solo archivos relevantes

### 🔄 COMPATIBILIDAD

**Retrocompatibilidad:**
- ✅ Config v2.2 → automático upgrade a v2.3
- ✅ Handlers legacy → fallback a comportamiento v2.2
- ✅ Sin ConfigManager → modo legacy (.mov/.mxf)

**Migración automática:**
- Al detectar config v2.2: agrega sección `file_extensions`
- Valores por defecto: whitelist con extensiones de video
- Sin interrupción del servicio

---

## HISTORIAL DE VERSIONES

### v2.2 - Julio 2025
- ✅ Búsqueda recursiva en QNAPs y PCs
- ✅ Eliminación en todas las subcarpetas
- ✅ Logging con rutas completas
- ✅ Handlers reales SMB

### v2.1 - Julio 2025
- ✅ Búsqueda automática extensiones .mov/.mxf
- ✅ Handlers PC reales con SMB
- ✅ Eliminación directa os.remove()

### v2.0 - Junio 2025  
- ✅ Versión base con QNAPs funcionales
- ✅ Sistema de handlers mock/real

### v2.3 - Julio 2025 (ACTUAL)
- ✅ **EXTENSIONES CONFIGURABLES** - Whitelist/Blacklist
- ✅ **UI DE CONFIGURACIÓN** - Gestión visual completa
- ✅ **VALIDACIÓN AUTOMÁTICA** - Integrada en todo el flujo
- ✅ **ESTADÍSTICAS AVANZADAS** - Separadas por tipo de rechazo
- ✅ **COMPATIBILIDAD TOTAL** - Retrocompatible con v2.2

---

## 🎯 BENEFICIOS PRINCIPALES v2.3

1. **Mayor Control**: Administradores pueden definir exactamente qué archivos procesar
2. **Seguridad Mejorada**: Previene eliminación accidental de archivos críticos
3. **Flexibilidad**: Adaptable a diferentes entornos sin recompilación
4. **Transparencia**: Logs detallados de por qué archivos fueron bloqueados
5. **Eficiencia**: Menos archivos procesados = mayor velocidad
6. **Usabilidad**: Configuración visual sin editar JSON manualmente

---

*Documentación v2.3 - Julio 2025 - Extensiones Configurables*
