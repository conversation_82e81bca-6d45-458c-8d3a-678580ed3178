"""
GUI Limpieza por Lista - Pieza 5: Ejecución Principal + Run
========================================================
"""

def _execute_cleanup_thread(self):
    """Hilo de ejecución de limpieza"""
    try:
        self.log_message("🗑️ Iniciando limpieza por lista...")
        self.log_message(f"📄 Archivo de lista: {self.archivo_lista.get()}")
        self.log_message(f"🔧 Modo: {'Simulación' if self.dry_run.get() else 'Real'}")
        
        # Leer archivo de lista
        with open(self.archivo_lista.get(), 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        files_to_delete = [line.strip() for line in lines if line.strip()]
        self.log_message(f"📋 Archivos en lista: {len(files_to_delete)}")
        
        if not files_to_delete:
            self.log_message("❌ Lista vacía")
            return
        
        # Ubicaciones habilitadas
        enabled_locations = [(loc, vars_dict) for loc, vars_dict in self.ubicaciones.items() 
                           if vars_dict["enabled"].get()]
        
        self.log_message(f"📁 Ubicaciones habilitadas: {len(enabled_locations)}")
        
        total_operations = len(files_to_delete) * len(enabled_locations)
        operations_completed = 0
        
        # Estadísticas
        stats = {
            "total_files": len(files_to_delete),
            "total_locations": len(enabled_locations),
            "files_found": 0,
            "files_deleted": 0,
            "errors": 0
        }
        
        # Procesar cada ubicación
        for location, vars_dict in enabled_locations:
            if not self.running:
                break
            
            location_path = vars_dict["path"].get()
            self.log_message(f"\n📁 Procesando {location}: {location_path}")
            
            if not location_path or not os.path.exists(location_path):
                self.log_message(f"❌ {location}: Ruta no válida")
                stats["errors"] += len(files_to_delete)
                operations_completed += len(files_to_delete)
                continue
            
            location_found = 0
            location_deleted = 0
            
            # Procesar archivos en esta ubicación
            for i, file_name in enumerate(files_to_delete):
                if not self.running:
                    break
                
                file_path = os.path.join(location_path, file_name)
                
                if os.path.exists(file_path):
                    location_found += 1
                    stats["files_found"] += 1
                    
                    if self.dry_run.get():
                        self.log_message(f"💾 (SIMULADO) {location}: {file_name}")
                        location_deleted += 1
                        stats["files_deleted"] += 1
                    else:
                        try:
                            os.remove(file_path)
                            self.log_message(f"🗑️ {location}: {file_name} eliminado")
                            location_deleted += 1
                            stats["files_deleted"] += 1
                        except Exception as e:
                            self.log_message(f"❌ {location}: Error eliminando {file_name}: {e}")
                            stats["errors"] += 1
                else:
                    self.log_message(f"⚪ {location}: {file_name} no encontrado")
                
                operations_completed += 1
                
                # Actualizar progreso
                progress_percent = (operations_completed / total_operations) * 100
                location_progress_percent = ((i + 1) / len(files_to_delete)) * 100
                
                self.root.after(0, lambda p=progress_percent: self.progress.config(value=p))
                self.root.after(0, lambda loc=location, lp=location_progress_percent: 
                               self.location_progress[loc].config(value=lp))
                
                # Actualizar estadísticas
                stats_text = (f"Procesados: {operations_completed}/{total_operations} | "
                            f"Encontrados: {stats['files_found']} | "
                            f"Eliminados: {stats['files_deleted']} | "
                            f"Errores: {stats['errors']}")
                self.root.after(0, lambda: self.stats_label.config(text=stats_text))
                
                time.sleep(0.1)  # Simular procesamiento
            
            self.log_message(f"✅ {location}: {location_found} encontrados, {location_deleted} procesados")
        
        # Resultados finales
        if self.running:
            self.log_message("\n" + "=" * 60)
            self.log_message(f"✅ LIMPIEZA COMPLETADA")
            self.log_message(f"📊 Archivos en lista: {stats['total_files']}")
            self.log_message(f"📁 Ubicaciones procesadas: {stats['total_locations']}")
            self.log_message(f"🔍 Archivos encontrados: {stats['files_found']}")
            self.log_message(f"🗑️ Archivos eliminados: {stats['files_deleted']}")
            self.log_message(f"❌ Errores: {stats['errors']}")
            
            if self.dry_run.get():
                self.log_message("💾 Modo simulación - No se realizaron cambios")
            
            self.log_message("=" * 60)
            
            # Mostrar resumen
            self.root.after(0, lambda: messagebox.showinfo("Completado", 
                f"Limpieza completada\n\n"
                f"Archivos en lista: {stats['total_files']}\n"
                f"Ubicaciones: {stats['total_locations']}\n"
                f"Encontrados: {stats['files_found']}\n"
                f"Eliminados: {stats['files_deleted']}\n"
                f"Errores: {stats['errors']}\n\n"
                f"Modo: {'Simulación' if self.dry_run.get() else 'Real'}"))
        
    except Exception as e:
        self.log_message(f"❌ ERROR: {e}")
        self.logger.error(f"Cleanup error: {e}")
    
    finally:
        # Restaurar UI
        self.running = False
        self.root.after(0, self._reset_ui)

def run(self):
    """Ejecutar aplicación"""
    try:
        self.root.mainloop()
    except KeyboardInterrupt:
        self.logger.info("Application stopped by user")
    except Exception as e:
        self.logger.error(f"Application error: {e}")

# Aplicar métodos a la clase
CleanupByListGUI.check_license = check_license
CleanupByListGUI.setup_ui = setup_ui
CleanupByListGUI.update_license = update_license
CleanupByListGUI.select_archivo_lista = select_archivo_lista
CleanupByListGUI.select_location_path = select_location_path
CleanupByListGUI.load_default_config = load_default_config
CleanupByListGUI.log_message = log_message
CleanupByListGUI.preview_list = preview_list
CleanupByListGUI.execute_cleanup = execute_cleanup
CleanupByListGUI.stop_cleanup = stop_cleanup
CleanupByListGUI._reset_ui = _reset_ui
CleanupByListGUI._execute_cleanup_thread = _execute_cleanup_thread
CleanupByListGUI.run = run

if __name__ == "__main__":
    print("🗑️ Gestor - Limpieza por Lista GUI")
    print("=" * 40)
    
    app = CleanupByListGUI()
    app.run()
