"""
GUI Limpieza por Lista - Pieza 2: Licencias + UI Setup
====================================================
"""

def check_license(self):
    """Verificar licencia al iniciar"""
    try:
        license_manager = GestorLicenseManager("limpieza")
        valid, message = license_manager.validate_license()
        
        if valid:
            self.licensed = True
            self.license_status.config(text=f"✅ Licenciado: {message}", foreground="green")
            self.logger.info(f"License valid: {message}")
        else:
            self.licensed = False
            self.license_status.config(text=f"❌ Sin licencia: {message}", foreground="red")
            self.logger.warning(f"License invalid: {message}")
            
            # Ofrecer activación
            response = messagebox.askyesno("Licencia", 
                f"Licencia no válida: {message}\n\n¿Desea activar licencia?")
            if response:
                activated = show_license_dialog(self.root, "limpieza")
                if activated:
                    self.check_license()  # Re-verificar
    except Exception as e:
        self.logger.error(f"License check error: {e}")
        self.licensed = False
        self.license_status.config(text="❌ Error verificando licencia", foreground="red")

def setup_ui(self):
    """Configurar interfaz de usuario"""
    # Frame principal
    main_frame = ttk.Frame(self.root, padding="10")
    main_frame.pack(fill='both', expand=True)
    
    # Header con logo IGSON
    self.setup_header(main_frame)
    
    # Frame de configuración de archivo
    file_frame = ttk.LabelFrame(main_frame, text="Archivo de Lista", padding="10")
    file_frame.pack(fill='x', pady=(10,0))
    
    ttk.Label(file_frame, text="Archivo TXT con lista:").grid(row=0, column=0, sticky='w', pady=5)
    ttk.Entry(file_frame, textvariable=self.archivo_lista, width=60).grid(row=0, column=1, padx=5, pady=5)
    ttk.Button(file_frame, text="Examinar", 
              command=self.select_archivo_lista).grid(row=0, column=2, pady=5)
    
    # Modo dry run
    ttk.Checkbutton(file_frame, text="Modo Simulación (Dry Run)", 
                   variable=self.dry_run).grid(row=1, column=0, columnspan=2, sticky='w', pady=10)
    
    # Frame de ubicaciones
    locations_frame = ttk.LabelFrame(main_frame, text="Ubicaciones (6 ubicaciones)", padding="10")
    locations_frame.pack(fill='x', pady=(10,0))
    
    # Crear checkboxes y campos para ubicaciones
    row = 0
    for location, vars_dict in self.ubicaciones.items():
        # Checkbox para habilitar/deshabilitar
        ttk.Checkbutton(locations_frame, text=location, 
                       variable=vars_dict["enabled"]).grid(row=row, column=0, sticky='w', pady=2)
        
        # Campo de ruta
        ttk.Entry(locations_frame, textvariable=vars_dict["path"], width=50).grid(row=row, column=1, padx=5, pady=2)
        
        # Botón examinar
        ttk.Button(locations_frame, text="...", width=3,
                  command=lambda loc=location: self.select_location_path(loc)).grid(row=row, column=2, pady=2)
        
        row += 1

def update_license(self):
    """Actualizar licencia"""
    activated = show_license_dialog(self.root, "limpieza")
    if activated:
        self.check_license()

def select_archivo_lista(self):
    """Seleccionar archivo de lista"""
    file_path = filedialog.askopenfilename(
        title="Seleccionar archivo de lista",
        filetypes=[("Archivos de texto", "*.txt"), ("Todos los archivos", "*.*")]
    )
    if file_path:
        self.archivo_lista.set(file_path)

def select_location_path(self, location):
    """Seleccionar ruta para ubicación específica"""
    folder = filedialog.askdirectory(title=f"Seleccionar ruta para {location}")
    if folder:
        self.ubicaciones[location]["path"].set(folder)
