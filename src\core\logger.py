"""
Gestor de Archivos Corporativo - Sistema de Logging
=================================================

Sistema de logging diferenciado por operación con rotación automática
"""

import logging
import logging.handlers
import os
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, Optional
from .utils import get_logs_directory, get_timestamp_string

class GestorLogger:
    """Sistema de logging diferenciado para el Gestor de Archivos"""
    
    def __init__(self):
        self.logs_dir = get_logs_directory()
        self.loggers: Dict[str, logging.Logger] = {}
        self.handlers: Dict[str, logging.Handler] = {}
        self.setup_logging()
    
    def setup_logging(self):
        """Configura sistema de logging"""
        # Configurar formato
        self.formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Configurar loggers por categoría
        self._setup_category_logger('sistema', 'Sistema general y errores')
        self._setup_category_logger('limpieza', 'Operaciones de limpieza por lista')
        self._setup_category_logger('validacion', 'Operaciones de validación de duplicados')
        self._setup_category_logger('conectividad', 'Tests de conexión y diagnósticos')
        
        # Logger principal
        self.main_logger = self.get_logger('sistema')
        self.main_logger.info("=== Sistema de Logging Iniciado ===")
    
    def _setup_category_logger(self, category: str, description: str):
        """Configura logger para una categoría específica"""
        logger_name = f"gestor.{category}"
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.DEBUG)
        
        # Limpiar handlers existentes
        logger.handlers.clear()
        
        # Handler para archivo (con rotación)
        log_filename = f"{category}_{datetime.now().strftime('%Y%m%d')}.log"
        log_filepath = self.logs_dir / log_filename
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_filepath,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(self.formatter)
        
        # Handler para consola (solo en desarrollo)
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(self.formatter)
        
        # Agregar handlers
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        # Guardar referencias
        self.loggers[category] = logger
        self.handlers[f"{category}_file"] = file_handler
        self.handlers[f"{category}_console"] = console_handler
        
        # Log inicial
        logger.info(f"Logger '{category}' inicializado - {description}")
    
    def get_logger(self, category: str = 'sistema') -> logging.Logger:
        """Obtiene logger para una categoría específica"""
        if category in self.loggers:
            return self.loggers[category]
        else:
            # Retornar logger por defecto si no existe la categoría
            return self.loggers.get('sistema', logging.getLogger('gestor.default'))
    
    def log_operation_start(self, category: str, operation: str, details: Optional[str] = None):
        """Registra inicio de operación"""
        logger = self.get_logger(category)
        separator = "=" * 50
        logger.info(separator)
        logger.info(f"INICIO: {operation}")
        if details:
            logger.info(f"Detalles: {details}")
        logger.info(f"Timestamp: {datetime.now().isoformat()}")
        logger.info(separator)
    
    def log_operation_end(self, category: str, operation: str, success: bool, 
                         stats: Optional[Dict] = None, duration: Optional[float] = None):
        """Registra fin de operación"""
        logger = self.get_logger(category)
        status = "EXITOSO" if success else "FALLIDO"
        separator = "=" * 50
        
        logger.info(separator)
        logger.info(f"FIN: {operation} - {status}")
        
        if duration:
            logger.info(f"Duración: {duration:.2f} segundos")
        
        if stats:
            logger.info("Estadísticas:")
            for key, value in stats.items():
                logger.info(f"  {key}: {value}")
        
        logger.info(f"Timestamp: {datetime.now().isoformat()}")
        logger.info(separator)
    
    def log_file_operation(self, category: str, operation: str, file_path: str, 
                          success: bool, error_msg: Optional[str] = None):
        """Registra operación específica de archivo"""
        logger = self.get_logger(category)
        status = "✓" if success else "✗"
        
        if success:
            logger.info(f"{status} {operation}: {file_path}")
        else:
            logger.error(f"{status} {operation}: {file_path}")
            if error_msg:
                logger.error(f"  Error: {error_msg}")
    
    def log_connectivity_test(self, location: str, method: str, success: bool, 
                            response_time: Optional[float] = None, error_msg: Optional[str] = None):
        """Registra test de conectividad"""
        logger = self.get_logger('conectividad')
        status = "ONLINE" if success else "OFFLINE"
        
        log_msg = f"[{location}] {method} - {status}"
        if response_time:
            log_msg += f" ({response_time:.2f}ms)"
        
        if success:
            logger.info(log_msg)
        else:
            logger.warning(log_msg)
            if error_msg:
                logger.warning(f"  Error: {error_msg}")
    
    def log_security_event(self, event_type: str, details: str, severity: str = 'INFO'):
        """Registra evento de seguridad"""
        logger = self.get_logger('sistema')
        log_msg = f"[SECURITY] {event_type}: {details}"
        
        if severity.upper() == 'WARNING':
            logger.warning(log_msg)
        elif severity.upper() == 'ERROR':
            logger.error(log_msg)
        else:
            logger.info(log_msg)
    
    def log_config_change(self, setting: str, old_value: str, new_value: str, user: str = 'System'):
        """Registra cambio de configuración"""
        logger = self.get_logger('sistema')
        logger.info(f"[CONFIG] {setting} cambiado por {user}: '{old_value}' → '{new_value}'")
    
    def get_log_files(self) -> Dict[str, Path]:
        """Obtiene rutas de todos los archivos de log"""
        log_files = {}
        today = datetime.now().strftime('%Y%m%d')
        
        for category in self.loggers.keys():
            log_filename = f"{category}_{today}.log"
            log_filepath = self.logs_dir / log_filename
            if log_filepath.exists():
                log_files[category] = log_filepath
        
        return log_files
    
    def get_log_stats(self) -> Dict[str, Dict]:
        """Obtiene estadísticas de logs"""
        stats = {}
        log_files = self.get_log_files()
        
        for category, log_file in log_files.items():
            try:
                file_size = log_file.stat().st_size
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = sum(1 for line in f)
                
                stats[category] = {
                    'file_path': str(log_file),
                    'file_size': file_size,
                    'file_size_mb': file_size / (1024 * 1024),
                    'total_lines': lines,
                    'last_modified': datetime.fromtimestamp(log_file.stat().st_mtime)
                }
            except Exception as e:
                stats[category] = {
                    'error': str(e)
                }
        
        return stats
    
    def cleanup_old_logs(self, days_to_keep: int = 30):
        """Limpia logs antiguos"""
        try:
            cutoff_date = datetime.now().timestamp() - (days_to_keep * 24 * 60 * 60)
            cleaned_count = 0
            
            for log_file in self.logs_dir.glob("*.log*"):
                if log_file.stat().st_mtime < cutoff_date:
                    log_file.unlink()
                    cleaned_count += 1
            
            if cleaned_count > 0:
                self.main_logger.info(f"Limpieza de logs: {cleaned_count} archivos eliminados")
        
        except Exception as e:
            self.main_logger.error(f"Error en limpieza de logs: {e}")
    
    def set_console_level(self, level: str):
        """Cambia nivel de logging en consola"""
        log_level = getattr(logging, level.upper(), logging.INFO)
        
        for handler_name, handler in self.handlers.items():
            if 'console' in handler_name:
                handler.setLevel(log_level)
        
        self.main_logger.info(f"Nivel de consola cambiado a: {level.upper()}")
    
    def disable_console_output(self):
        """Desactiva salida a consola"""
        for handler_name, handler in self.handlers.items():
            if 'console' in handler_name:
                handler.setLevel(logging.CRITICAL + 1)  # Nivel más alto que CRITICAL
        
        self.main_logger.info("Salida a consola desactivada")
    
    def enable_console_output(self):
        """Reactiva salida a consola"""
        for handler_name, handler in self.handlers.items():
            if 'console' in handler_name:
                handler.setLevel(logging.INFO)
        
        self.main_logger.info("Salida a consola reactivada")

# Instancia global del sistema de logging
gestor_logger = GestorLogger()

# Funciones de conveniencia para acceso rápido
def get_logger(category: str = 'sistema') -> logging.Logger:
    """Función de conveniencia para obtener logger"""
    return gestor_logger.get_logger(category)

def log_operation_start(category: str, operation: str, details: Optional[str] = None):
    """Función de conveniencia para registrar inicio de operación"""
    gestor_logger.log_operation_start(category, operation, details)

def log_operation_end(category: str, operation: str, success: bool, 
                     stats: Optional[Dict] = None, duration: Optional[float] = None):
    """Función de conveniencia para registrar fin de operación"""
    gestor_logger.log_operation_end(category, operation, success, stats, duration)

def log_file_operation(category: str, operation: str, file_path: str, 
                      success: bool, error_msg: Optional[str] = None):
    """Función de conveniencia para registrar operación de archivo"""
    gestor_logger.log_file_operation(category, operation, file_path, success, error_msg)

if __name__ == "__main__":
    # Test del sistema de logging
    print("📝 Logger System Test")
    print("=" * 30)
    
    # Test loggers por categoría
    sistema_logger = get_logger('sistema')
    limpieza_logger = get_logger('limpieza')
    validacion_logger = get_logger('validacion')
    conectividad_logger = get_logger('conectividad')
    
    # Test operaciones
    log_operation_start('limpieza', 'Test de limpieza', 'Prueba del sistema de logging')
    
    # Test operaciones de archivos
    log_file_operation('limpieza', 'DELETE', 'C:\\test\\archivo1.txt', True)
    log_file_operation('limpieza', 'DELETE', 'C:\\test\\archivo2.txt', False, 'File not found')
    
    # Test conectividad
    gestor_logger.log_connectivity_test('qnap1', 'PING', True, 25.5)
    gestor_logger.log_connectivity_test('qnap2', 'SMB', False, None, 'Access denied')
    
    # Test eventos de seguridad
    gestor_logger.log_security_event('LOGIN_ATTEMPT', 'Usuario intentó acceder', 'WARNING')
    
    # Test fin de operación
    stats = {'archivos_procesados': 10, 'archivos_eliminados': 8, 'errores': 2}
    log_operation_end('limpieza', 'Test de limpieza', True, stats, 15.5)
    
    # Mostrar estadísticas
    print("\nLog Statistics:")
    log_stats = gestor_logger.get_log_stats()
    for category, stats in log_stats.items():
        if 'error' not in stats:
            print(f"  {category}: {stats['total_lines']} lines, {stats['file_size_mb']:.2f} MB")
        else:
            print(f"  {category}: Error - {stats['error']}")
    
    print("\nLogger system test completed!")
