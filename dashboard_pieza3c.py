"""
dashboard_pieza3c.py
PIEZA 3C: Métodos de soporte para Validación
Gestor de Archivos Corporativo
"""

# CONTINUACIÓN DE LA CLASE GestorDashboard - MÉTODOS DE SOPORTE VALIDACIÓN

    def select_validacion_origen(self):
        """Seleccionar carpeta origen para validación"""
        folder = filedialog.askdirectory(title="Seleccionar Carpeta Origen")
        if folder:
            self.validacion_origen_var.set(folder)
            self.update_status(f"Carpeta origen seleccionada: {Path(folder).name}", "success")
            self.validate_validacion_config()
    
    def select_validacion_rechazados(self):
        """Seleccionar carpeta rechazados para validación"""
        folder = filedialog.askdirectory(title="Seleccionar Carpeta Rechazados")
        if folder:
            self.validacion_rechazados_var.set(folder)
            self.update_status(f"Carpeta rechazados seleccionada: {Path(folder).name}", "success")
            self.validate_validacion_config()
    
    def create_rechazados_folder(self):
        """Crear carpeta rechazados automáticamente"""
        origen = self.validacion_origen_var.get().strip()
        if not origen:
            messagebox.showwarning("Advertencia", "Seleccione primero la carpeta origen")
            return
        
        # Crear carpeta rechazados en el mismo directorio padre
        parent_dir = Path(origen).parent
        rechazados_path = parent_dir / "rechazados"
        
        try:
            rechazados_path.mkdir(parents=True, exist_ok=True)
            self.validacion_rechazados_var.set(str(rechazados_path))
            self.update_status(f"Carpeta rechazados creada: {rechazados_path.name}", "success")
            self.validate_validacion_config()
        except Exception as e:
            messagebox.showerror("Error", f"Error creando carpeta rechazados: {e}")
    
    def preview_origen_folder(self):
        """Previsualizar contenido de carpeta origen"""
        origen = self.validacion_origen_var.get().strip()
        if not origen or not Path(origen).exists():
            messagebox.showwarning("Advertencia", "Carpeta origen no válida")
            return
        
        preview_window = tk.Toplevel(self.root)
        preview_window.title(f"Contenido - {Path(origen).name}")
        preview_window.geometry("700x500")
        preview_window.transient(self.root)
        preview_window.grab_set()
        
        # Frame principal
        main_frame = ttk.Frame(preview_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Información superior
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(info_frame, text=f"📁 Carpeta: {origen}", 
                 style='Header.TLabel').pack(anchor=tk.W)
        
        # Área de archivos con doble scroll
        files_frame = ttk.Frame(main_frame)
        files_frame.pack(fill=tk.BOTH, expand=True)
        
        files_text = tk.Text(files_frame, wrap=tk.NONE, font=('Consolas', 9))
        files_scroll_v = ttk.Scrollbar(files_frame, orient=tk.VERTICAL, command=files_text.yview)
        files_scroll_h = ttk.Scrollbar(files_frame, orient=tk.HORIZONTAL, command=files_text.xview)
        files_text.configure(yscrollcommand=files_scroll_v.set, xscrollcommand=files_scroll_h.set)
        
        # Cargar contenido en thread separado
        def load_content():
            try:
                files_text.insert(1.0, "🔄 Cargando contenido...\n")
                files_text.update()
                
                files = list(Path(origen).iterdir())
                file_list = [f for f in files if f.is_file()]
                dir_list = [f for f in files if f.is_dir()]
                
                # Calcular tamaño total
                total_size = 0
                for file_path in file_list:
                    try:
                        total_size += file_path.stat().st_size
                    except:
                        pass
                
                # Limpiar y escribir información
                files_text.delete(1.0, tk.END)
                
                files_text.insert(1.0, f"=== CONTENIDO DE {origen} ===\n\n")
                files_text.insert(tk.END, f"📊 ESTADÍSTICAS:\n")
                files_text.insert(tk.END, f"   Archivos: {len(file_list)}\n")
                files_text.insert(tk.END, f"   Carpetas: {len(dir_list)}\n")
                files_text.insert(tk.END, f"   Tamaño total: {total_size / (1024*1024):.2f} MB\n\n")
                
                if dir_list:
                    files_text.insert(tk.END, f"📁 CARPETAS ({len(dir_list)}):\n")
                    for dir_path in sorted(dir_list):
                        files_text.insert(tk.END, f"   📁 {dir_path.name}/\n")
                    files_text.insert(tk.END, "\n")
                
                if file_list:
                    files_text.insert(tk.END, f"📄 ARCHIVOS ({len(file_list)}):\n")
                    for file_path in sorted(file_list):
                        try:
                            size = file_path.stat().st_size / (1024*1024)  # MB
                            if size < 0.01:
                                size_str = f"{file_path.stat().st_size} B"
                            else:
                                size_str = f"{size:.2f} MB"
                            
                            files_text.insert(tk.END, f"   📄 {file_path.name:<50} {size_str:>10}\n")
                        except Exception as e:
                            files_text.insert(tk.END, f"   📄 {file_path.name:<50} {'Error':>10}\n")
                else:
                    files_text.insert(tk.END, "ℹ️ No hay archivos en esta carpeta\n")
                    
            except Exception as e:
                files_text.delete(1.0, tk.END)
                files_text.insert(1.0, f"❌ Error leyendo carpeta: {e}\n")
            
            files_text.config(state='disabled')
        
        # Ejecutar carga en thread
        threading.Thread(target=load_content, daemon=True).start()
        
        # Layout de scrollbars
        files_text.grid(row=0, column=0, sticky='nsew')
        files_scroll_v.grid(row=0, column=1, sticky='ns')
        files_scroll_h.grid(row=1, column=0, sticky='ew')
        
        files_frame.grid_rowconfigure(0, weight=1)
        files_frame.grid_columnconfigure(0, weight=1)
        
        # Botón cerrar
        ttk.Button(preview_window, text="Cerrar", 
                  command=preview_window.destroy).pack(pady=10)
    
    def preview_validacion(self):
        """Previsualizar qué haría la validación"""
        origen = self.validacion_origen_var.get().strip()
        if not origen or not Path(origen).exists():
            messagebox.showwarning("Configuración Incompleta", "Configure una carpeta origen válida")
            return
        
        preview_window = tk.Toplevel(self.root)
        preview_window.title("Previsión - Validación de Duplicados")
        preview_window.geometry("800x600")
        preview_window.transient(self.root)
        
        # Frame principal
        main_frame = ttk.Frame(preview_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Información de configuración
        config_frame = ttk.LabelFrame(main_frame, text="Configuración", padding="10")
        config_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(config_frame, text=f"📁 Origen: {origen}").pack(anchor=tk.W)
        ttk.Label(config_frame, text=f"🗂️ Rechazados: {self.validacion_rechazados_var.get()}").pack(anchor=tk.W)
        ttk.Label(config_frame, text=f"🧪 Modo: {'Simulación' if self.validacion_dry_run_var.get() else 'Real'}").pack(anchor=tk.W)
        
        # Área de previsión
        preview_frame = ttk.LabelFrame(main_frame, text="Previsión", padding="10")
        preview_frame.pack(fill=tk.BOTH, expand=True)
        
        preview_text = tk.Text(preview_frame, wrap=tk.WORD, font=('Consolas', 9))
        preview_scroll = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=preview_text.yview)
        preview_text.configure(yscrollcommand=preview_scroll.set)
        
        preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        preview_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Botones
        buttons_frame = ttk.Frame(preview_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(buttons_frame, text="🔄 Actualizar", 
                  command=lambda: self.refresh_validacion_preview(preview_text)).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(buttons_frame, text="Cerrar", 
                  command=preview_window.destroy).pack(side=tk.RIGHT, padx=5)
        
        # Cargar previsión inicial
        self.refresh_validacion_preview(preview_text)
    
    def refresh_validacion_preview(self, preview_text):
        """Actualizar previsión de validación"""
        preview_text.delete(1.0, tk.END)
        preview_text.insert(1.0, "🔍 PREVISIÓN DE VALIDACIÓN\n\n")
        preview_text.insert(tk.END, "🔄 Analizando archivos...\n")
        preview_text.update()
        
        def preview_worker():
            try:
                origen = self.validacion_origen_var.get().strip()
                files = list(Path(origen).glob('*'))
                file_list = [f for f in files if f.is_file()]
                
                preview_text.insert(tk.END, f"\n📊 ESTADÍSTICAS:\n")
                preview_text.insert(tk.END, f"   Total archivos encontrados: {len(file_list)}\n")
                
                if len(file_list) == 0:
                    preview_text.insert(tk.END, "\n⚠️ No hay archivos para procesar\n")
                    return
                
                # Mostrar primeros archivos
                preview_text.insert(tk.END, f"\n📄 ARCHIVOS A PROCESAR (primeros 20):\n")
                for i, file_path in enumerate(file_list[:20], 1):
                    size = file_path.stat().st_size / (1024*1024)
                    preview_text.insert(tk.END, f"{i:3d}. {file_path.name:<40} ({size:.2f} MB)\n")
                
                if len(file_list) > 20:
                    preview_text.insert(tk.END, f"\n... y {len(file_list) - 20} archivos más\n")
                
                # Información sobre el proceso
                preview_text.insert(tk.END, f"\n🔍 PROCESO QUE SE EJECUTARÁ:\n")
                preview_text.insert(tk.END, f"1. Conectar a ubicaciones QNAP\n")
                preview_text.insert(tk.END, f"2. Buscar duplicados de cada archivo\n")
                preview_text.insert(tk.END, f"3. Mover duplicados a carpeta rechazados\n")
                preview_text.insert(tk.END, f"4. Generar reporte detallado\n")
                
                if self.validacion_dry_run_var.get():
                    preview_text.insert(tk.END, f"\n🧪 MODO SIMULACIÓN ACTIVO\n")
                    preview_text.insert(tk.END, f"   No se realizarán cambios reales\n")
                
                preview_text.insert(tk.END, f"\nℹ️ Use 'Ejecutar Validación' para comenzar el proceso real\n")
                
            except Exception as e:
                preview_text.insert(tk.END, f"\n❌ Error en previsión: {e}\n")
        
        threading.Thread(target=preview_worker, daemon=True).start()
    
    def validate_validacion_config(self):
        """Validar configuración de validación"""
        origen = self.validacion_origen_var.get().strip()
        rechazados = self.validacion_rechazados_var.get().strip()
        
        issues = []
        
        if not origen:
            issues.append("📁 Carpeta origen no configurada")
        elif not Path(origen).exists():
            issues.append("📁 Carpeta origen no existe")
        elif not Path(origen).is_dir():
            issues.append("📁 Carpeta origen no es un directorio")
        
        if not rechazados:
            issues.append("🗂️ Carpeta rechazados no configurada")
        elif origen and rechazados and Path(origen).resolve() == Path(rechazados).resolve():
            issues.append("🗂️ Carpeta rechazados no puede ser igual a origen")
        
        # Actualizar UI según validación
        if issues:
            self.update_status(f"Configuración incompleta: {issues[0]}", "warning")
            if hasattr(self, 'validacion_execute_btn'):
                self.validacion_execute_btn.config(state='disabled')
        else:
            self.update_status("Configuración de validación válida", "success")
            if hasattr(self, 'validacion_execute_btn'):
                self.validacion_execute_btn.config(state='normal')
        
        return len(issues) == 0
    
    def save_validacion_config(self):
        """Guardar configuración de validación"""
        try:
            from core.config_manager import config_manager
            
            config = {
                'carpeta_origen': self.validacion_origen_var.get(),
                'carpeta_rechazados': self.validacion_rechazados_var.get(),
                'dry_run': self.validacion_dry_run_var.get(),
                'backup': getattr(self, 'validacion_backup_var', tk.BooleanVar()).get(),
                'log_detailed': getattr(self, 'validacion_log_detailed_var', tk.BooleanVar()).get(),
                'verify_integrity': getattr(self, 'validacion_verify_integrity_var', tk.BooleanVar()).get()
            }
            
            config_manager.save_user_config('validacion', config)
            messagebox.showinfo("Configuración", "Configuración de validación guardada correctamente")
            self.update_status("Configuración guardada", "success")
            
        except Exception as e:
            messagebox.showerror("Error", f"Error guardando configuración: {e}")
            self.update_status(f"Error guardando configuración: {e}", "error")
    
    def load_validacion_config(self):
        """Cargar configuración de validación"""
        try:
            from core.config_manager import config_manager
            
            config = config_manager.get_user_config('validacion', {})
            
            if config:
                self.validacion_origen_var.set(config.get('carpeta_origen', ''))
                self.validacion_rechazados_var.set(config.get('carpeta_rechazados', ''))
                self.validacion_dry_run_var.set(config.get('dry_run', True))
                
                if hasattr(self, 'validacion_backup_var'):
                    self.validacion_backup_var.set(config.get('backup', True))
                if hasattr(self, 'validacion_log_detailed_var'):
                    self.validacion_log_detailed_var.set(config.get('log_detailed', True))
                if hasattr(self, 'validacion_verify_integrity_var'):
                    self.validacion_verify_integrity_var.set(config.get('verify_integrity', False))
                
                messagebox.showinfo("Configuración", "Configuración cargada correctamente")
                self.update_status("Configuración cargada", "success")
                self.validate_validacion_config()
            else:
                messagebox.showinfo("Configuración", "No hay configuración guardada")
                
        except Exception as e:
            messagebox.showerror("Error", f"Error cargando configuración: {e}")
            self.update_status(f"Error cargando configuración: {e}", "error")
    
    def reset_validacion_config(self):
        """Restaurar configuración de validación a valores por defecto"""
        if messagebox.askyesno("Confirmar", "¿Restaurar configuración a valores por defecto?"):
            self.validacion_origen_var.set('')
            self.validacion_rechazados_var.set('')
            self.validacion_dry_run_var.set(True)
            
            if hasattr(self, 'validacion_backup_var'):
                self.validacion_backup_var.set(True)
            if hasattr(self, 'validacion_log_detailed_var'):
                self.validacion_log_detailed_var.set(True)
            if hasattr(self, 'validacion_verify_integrity_var'):
                self.validacion_verify_integrity_var.set(False)
            
            self.update_status("Configuración restaurada a valores por defecto", "info")
            self.validate_validacion_config()
    
    def update_validacion_progress(self, progress: int, message: str = ""):
        """Actualizar progreso de validación"""
        try:
            if hasattr(self, 'validacion_progress'):
                self.validacion_progress['value'] = progress
                self.validacion_progress_label.config(text=f"{progress}%")
                
                if message:
                    self.update_status(message, "info")
                
                self.root.update_idletasks()
        except Exception as e:
            self.logger.error(f"Error actualizando progreso validación: {e}")
    
    def show_validacion_results(self, text: str, tab: str = "summary"):
        """Mostrar resultados de validación en la pestaña especificada"""
        try:
            if tab == "summary" and hasattr(self, 'validacion_summary_text'):
                widget = self.validacion_summary_text
            elif tab == "log" and hasattr(self, 'validacion_log_text'):
                widget = self.validacion_log_text
            elif tab == "stats" and hasattr(self, 'validacion_stats_text'):
                widget = self.validacion_stats_text
            else:
                return
            
            widget.config(state='normal')
            widget.delete(1.0, tk.END)
            widget.insert(1.0, text)
            widget.see(tk.END)
            widget.config(state='disabled')
            
        except Exception as e:
            self.logger.error(f"Error mostrando resultados validación: {e}")
    
    def append_validacion_log(self, message: str):
        """Agregar mensaje al log de validación"""
        try:
            if hasattr(self, 'validacion_log_text'):
                self.validacion_log_text.config(state='normal')
                timestamp = datetime.now().strftime("%H:%M:%S")
                self.validacion_log_text.insert(tk.END, f"[{timestamp}] {message}\n")
                self.validacion_log_text.see(tk.END)
                self.validacion_log_text.config(state='disabled')
        except Exception as e:
            self.logger.error(f"Error agregando log validación: {e}")

# FIN PIEZA 3C
