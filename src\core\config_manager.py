"""
Gestor de Archivos Corporativo - Configuration Manager v2.3
==========================================================

v2.3 - Julio 2025:
- ✅ Soporte para extensiones configurables
- ✅ Whitelist y blacklist de extensiones
- ✅ Validación de configuración de extensiones

Maneja configuración JSON con encriptación de credenciales
Soporte para múltiples ubicaciones con credenciales independientes
"""

import json
import os
import sys
from pathlib import Path
from cryptography.fernet import Fernet
import base64
import logging
from typing import Dict, Any, Optional, List

class ConfigManager:
    """Gestor de configuración con encriptación de credenciales"""
    
    def __init__(self, config_file: str = "config.json"):
        self.logger = logging.getLogger(__name__)
        self.config_file = self._get_config_path(config_file)
        self.credentials_key = self._get_or_create_key()
        self.config_data = {}
        self.load_config()
    
    def _get_config_path(self, filename: str) -> Path:
        """Obtiene ruta absoluta del archivo de configuración"""
        if getattr(sys, 'frozen', False):
            # Ejecutable compilado
            base_path = Path(sys.executable).parent
        else:
            # Desarrollo
            base_path = Path(__file__).parent.parent / "config"
        
        return base_path / filename
    
    def _get_or_create_key(self) -> Fernet:
        """Obtiene o crea clave de encriptación"""
        key_file = self.config_file.parent / "encryption.key"
        
        if key_file.exists():
            with open(key_file, 'rb') as f:
                key = f.read()
        else:
            key = Fernet.generate_key()
            key_file.parent.mkdir(parents=True, exist_ok=True)
            with open(key_file, 'wb') as f:
                f.write(key)
            self.logger.info("Created new encryption key")
        
        return Fernet(key)
    
    def load_config(self) -> bool:
        """Carga configuración desde archivo"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
                self._ensure_file_extensions_config()
                self.logger.info(f"Configuration loaded from {self.config_file}")
                return True
            else:
                self.logger.warning("Config file not found, creating default")
                self._create_default_config()
                return False
        except Exception as e:
            self.logger.error(f"Error loading config: {e}")
            self._create_default_config()
            return False
    
    def _ensure_file_extensions_config(self):
        """Asegura que existe la configuración de extensiones v2.3"""
        if 'file_extensions' not in self.config_data:
            self.config_data['file_extensions'] = self._get_default_extensions_config()
            self.save_config()
            self.logger.info("Added default file_extensions configuration v2.3")
    
    def _get_default_extensions_config(self) -> Dict:
        """Configuración por defecto de extensiones v2.3"""
        return {
            "mode": "whitelist",  # "whitelist", "blacklist", "all"
            "whitelist": [".mov", ".mxf", ".mp4", ".avi"],
            "blacklist": [".exe", ".dll", ".sys", ".bat", ".config", ".ini"],
            "case_sensitive": False,
            "description": "v2.3 - Configuración de extensiones de archivos permitidas/bloqueadas"
        }
    
    def _create_default_config(self):
        """Crea configuración por defecto"""
        self.config_data = {
            "app_info": {
                "name": "Gestor de Archivos Corporativo",
                "version": "2.3",
                "dev_mode": True
            },
            "file_extensions": self._get_default_extensions_config(),
            "connection_settings": {
                "connection_type": "hybrid",
                "timeout_seconds": 30,
                "retry_attempts": 3
            },
            "locations": {
                "qnap1": {
                    "name": "QNAP Storage 1",
                    "network_path": "\\\\qnap1\\share",
                    "symlink_path": "C:\\Links\\qnap1",
                    "enabled": True,
                    "connection_priority": ["symlink", "network"],
                    "credentials": {
                        "username": "",
                        "password_encrypted": "",
                        "domain": "WORKGROUP"
                    }
                },
                "qnap2": {
                    "name": "QNAP Storage 2",
                    "network_path": "\\\\qnap2\\share",
                    "symlink_path": "C:\\Links\\qnap2",
                    "enabled": True,
                    "connection_priority": ["symlink", "network"],
                    "credentials": {
                        "username": "",
                        "password_encrypted": "",
                        "domain": "WORKGROUP"
                    }
                }
            },
            "operations": {
                "limpieza_lista": {
                    "ubicaciones_limpieza": ["qnap1", "qnap2"],
                    "carpetas_objetivo": {
                        "qnap1": "\\datos\\produccion",
                        "qnap2": "\\backup\\files"
                    },
                    "enabled": True
                }
            },
            "logging": {
                "level": "DEBUG",
                "separate_logs": True,
                "max_log_size_mb": 10,
                "backup_count": 5,
                "console_output": True
            }
        }
        self.save_config()
    
    # ===========================================
    # MÉTODOS v2.3 - EXTENSIONES CONFIGURABLES
    # ===========================================
    
    def get_file_extensions_config(self) -> Dict:
        """Obtiene configuración completa de extensiones"""
        return self.get('file_extensions', self._get_default_extensions_config())
    
    def get_extension_mode(self) -> str:
        """Obtiene modo de extensiones: whitelist, blacklist, all"""
        return self.get('file_extensions.mode', 'whitelist')
    
    def get_whitelist_extensions(self) -> List[str]:
        """Obtiene lista de extensiones permitidas"""
        extensions = self.get('file_extensions.whitelist', [".mov", ".mxf"])
        return [ext.lower() for ext in extensions] if not self.is_case_sensitive() else extensions
    
    def get_blacklist_extensions(self) -> List[str]:
        """Obtiene lista de extensiones bloqueadas"""
        extensions = self.get('file_extensions.blacklist', [".exe", ".dll"])
        return [ext.lower() for ext in extensions] if not self.is_case_sensitive() else extensions
    
    def is_case_sensitive(self) -> bool:
        """Verifica si las extensiones son case-sensitive"""
        return self.get('file_extensions.case_sensitive', False)
    
    def is_file_extension_allowed(self, filename: str) -> bool:
        """
        Valida si un archivo está permitido según configuración de extensiones
        """
        if not filename:
            return False
        
        file_ext = Path(filename).suffix
        if not self.is_case_sensitive():
            file_ext = file_ext.lower()
        
        mode = self.get_extension_mode()
        
        if mode == "all":
            return True
        elif mode == "whitelist":
            whitelist = self.get_whitelist_extensions()
            return file_ext in whitelist
        elif mode == "blacklist":
            blacklist = self.get_blacklist_extensions()
            return file_ext not in blacklist
        else:
            # Modo desconocido, usar whitelist por defecto
            whitelist = self.get_whitelist_extensions()
            return file_ext in whitelist
    
    def get_extension_status(self, filename: str) -> Dict:
        """Obtiene estado detallado de validación de extensión"""
        file_ext = Path(filename).suffix
        if not self.is_case_sensitive():
            file_ext = file_ext.lower()
        
        mode = self.get_extension_mode()
        allowed = self.is_file_extension_allowed(filename)
        
        status = {
            'filename': filename,
            'extension': file_ext,
            'mode': mode,
            'allowed': allowed,
            'reason': ''
        }
        
        if mode == "all":
            status['reason'] = "Todas las extensiones permitidas"
        elif mode == "whitelist":
            if allowed:
                status['reason'] = f"Extensión {file_ext} está en whitelist"
            else:
                status['reason'] = f"Extensión {file_ext} NO está en whitelist"
        elif mode == "blacklist":
            if allowed:
                status['reason'] = f"Extensión {file_ext} NO está en blacklist"
            else:
                status['reason'] = f"Extensión {file_ext} está en blacklist (bloqueada)"
        
        return status
    
    # ===========================================
    # MÉTODOS EXISTENTES
    # ===========================================
    
    def get(self, key_path: str, default=None) -> Any:
        """Obtiene valor usando ruta con puntos"""
        try:
            keys = key_path.split('.')
            value = self.config_data
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any) -> bool:
        """Establece valor usando ruta con puntos"""
        try:
            keys = key_path.split('.')
            target = self.config_data
            for key in keys[:-1]:
                if key not in target:
                    target[key] = {}
                target = target[key]
            target[keys[-1]] = value
            return True
        except Exception as e:
            self.logger.error(f"Error setting config value {key_path}: {e}")
            return False
    
    def get_locations(self) -> Dict[str, Dict]:
        """Obtiene todas las ubicaciones configuradas"""
        return self.config_data.get('locations', {})
    
    def get_enabled_locations(self) -> Dict[str, Dict]:
        """Obtiene solo ubicaciones habilitadas"""
        locations = self.get_locations()
        return {k: v for k, v in locations.items() if v.get('enabled', False)}
    
    def encrypt_password(self, password: str) -> str:
        """Encripta contraseña"""
        try:
            encrypted_bytes = self.credentials_key.encrypt(password.encode())
            return base64.b64encode(encrypted_bytes).decode()
        except Exception as e:
            self.logger.error(f"Error encrypting password: {e}")
            return ""
    
    def decrypt_password(self, encrypted_password: str) -> str:
        """Desencripta contraseña"""
        try:
            if not encrypted_password:
                return ""
            encrypted_bytes = base64.b64decode(encrypted_password.encode())
            decrypted_bytes = self.credentials_key.decrypt(encrypted_bytes)
            return decrypted_bytes.decode()
        except Exception as e:
            self.logger.error(f"Error decrypting password: {e}")
            return ""
    
    def save_config(self) -> bool:
        """Guarda configuración a archivo"""
        try:
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Configuration saved to {self.config_file}")
            return True
        except Exception as e:
            self.logger.error(f"Error saving config: {e}")
            return False
    
    def get_app_info(self) -> Dict[str, str]:
        """Obtiene información de la aplicación"""
        return self.get('app_info', {})

# Instancia global para fácil acceso
config = ConfigManager()

if __name__ == "__main__":
    # Test v2.3 - Extensiones configurables
    print("🔧 Config Manager v2.3 Test - Extensiones Configurables")
    print("=" * 60)
    
    app_info = config.get_app_info()
    print(f"App: {app_info.get('name')} v{app_info.get('version')}")
    
    # Test configuración de extensiones
    print("\n📋 Configuración de Extensiones:")
    ext_config = config.get_file_extensions_config()
    print(f"Modo: {ext_config['mode']}")
    print(f"Whitelist: {ext_config['whitelist']}")
    print(f"Blacklist: {ext_config['blacklist']}")
    
    # Test validación de archivos
    print("\n🔍 Test Validación de Archivos:")
    test_files = ["video.mov", "archivo.mxf", "documento.txt", "programa.exe"]
    
    for filename in test_files:
        status = config.get_extension_status(filename)
        symbol = "✅" if status['allowed'] else "❌"
        print(f"  {symbol} {filename} - {status['reason']}")
    
    print("\n✅ ConfigManager v2.3 ready!")
