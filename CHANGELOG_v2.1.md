# CHANGELOG VERSIÓN 2.1 - JULIO 2025

## Gestor de Archivos Corporativo - Limpieza por Lista

### 🔧 CAMBIOS IMPLEMENTADOS

**Problema resuelto:** App no encontraba archivos con extensiones `.mxf` y `.mov` cuando el archivo de lista contenía solo nombres base.

**Solución v2.1:**
- ✅ **Búsqueda automática con extensiones** - Si no encuentra `35179`, busca `35179.mov` y `35179.mxf`
- ✅ **Eliminación directa** - Usa `os.remove()` para evitar errores con handlers mock
- ✅ **Fix en QnapHandler.check_file_exists()** - Implementado soporte extensiones automáticas
- ⚠️ **LIMITACIÓN IDENTIFICADA:** Handlers para PCs son simulados (no reales)

### 📋 FUNCIONALIDAD

**Antes v2.0:**
- Lista: `35179` → Busca exactamente `35179` → ❌ No encontrado

**Después v2.1:**
- Lista: `35179` → Busca `35179` → `35179.mov` → `35179.mxf` → ✅ Encontrado y eliminado

### 🧪 TESTING CONFIRMADO

**QNAPs (funcionando):**
- `M:\Video\35179.mxf` ✅ Encontrado y eliminado
- `M:\Video\35446.mxf` ✅ Encontrado y eliminado

**PCs (simulados):**
- Solo simulación con 33% probabilidad aleatoria
- `handler.delete_file_from_pc()` no implementado

### 🚨 PENDIENTE VERSIÓN 2.1

**Implementar handlers PC reales:**
1. Conexión SMB real a PCs
2. Búsqueda de archivos en rutas de red
3. Eliminación via protocolo SMB
4. Manejo de credenciales por PC

### 📦 COMPILACIÓN

**Archivo generado:** `LimpiezaPorLista.exe`
**Ubicación:** `dist/`
**Versión:** 2.1 - Julio 2025
**Estado:** QNAPs funcionales, PCs pendientes
