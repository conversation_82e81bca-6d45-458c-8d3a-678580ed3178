# CHANGELOG VERSIÓN 2.2 - JULIO 2025

## Gestor de Archivos Corporativo - Limpieza por Lista

### 🎯 NUEVA FUNCIONALIDAD: BÚSQUEDA RECURSIVA

**Cambio principal:** Implementación de búsqueda y eliminación recursiva en todas las subcarpetas.

### 🔧 CAMBIOS IMPLEMENTADOS

**v2.2 - Búsqueda Recursiva:**
- ✅ **Búsqueda recursiva QNAPs** - Usa `Path.rglob()` para buscar en todas las subcarpetas
- ✅ **Búsqueda recursiva PCs** - Usa `os.walk()` para buscar en rutas UNC recursivamente  
- ✅ **Eliminación automática** - Encuentra y elimina archivos en cualquier nivel de subcarpetas
- ✅ **Logging mejorado** - Muestra ruta completa donde se encontró el archivo
- ✅ **Rendimiento optimizado** - Se detiene al encontrar el primer archivo válido

### 📋 FUNCIONALIDAD

**Antes v2.1 (no recursiva):**
- Lista: `48832` → Busca solo en `M:\Video\48832.mov` → ❌ No encontrado si está en subcarpeta

**Después v2.2 (recursiva):**
- Lista: `48832` → Busca en:
  - `M:\Video\48832.mov`
  - `M:\Video\2025\48832.mov`  
  - `M:\Video\2025\julio\48832.mxf`
  - `\\**************\d\TEST\subfolder\48832.mov`
- ✅ Encontrado y eliminado en cualquier subcarpeta

### 🧪 TESTING ESPERADO

**QNAPs recursivos:**
- `M:\Video\archivo.mxf` ✅
- `M:\Video\2025\archivo.mov` ✅  
- `M:\Video\subfolder1\subfolder2\archivo.mxf` ✅

**PCs recursivos:**
- `\\hostname\share\archivo.mov` ✅
- `\\hostname\share\folder\archivo.mxf` ✅
- `\\hostname\share\deep\nested\folder\archivo.mov` ✅

### 🏗️ IMPLEMENTACIÓN TÉCNICA

**QNAPs:**
```python
patterns = [filename, f"{filename}.mov", f"{filename}.mxf"]
for pattern in patterns:
    for file_path in Path(search_path).rglob(pattern):
        if file_path.is_file():
            # Archivo encontrado recursivamente
```

**PCs:**
```python
for root, dirs, files in os.walk(unc_path):
    for file in files:
        if file.startswith(filename) and file.endswith(('.mov', '.mxf')):
            # Archivo encontrado recursivamente
```

### 📦 COMPILACIÓN

**Archivo generado:** `LimpiezaPorLista_v2.2.exe`
**Ubicación:** `dist/`
**Versión:** 2.2 - Julio 2025
**Características:** Búsqueda y eliminación recursiva completa

### ⚡ MEJORAS DE RENDIMIENTO

- **Optimización early-break:** Se detiene al encontrar el primer archivo
- **Patrón eficiente:** Búsqueda dirigida por extensiones conocidas
- **Logging selectivo:** Solo muestra rutas cuando encuentra archivos

---

## HISTORIAL DE VERSIONES

### v2.1 - Julio 2025
- ✅ Búsqueda automática extensiones .mov/.mxf
- ✅ Handlers PC reales con SMB
- ✅ Eliminación directa os.remove()

### v2.0 - Junio 2025  
- ✅ Versión base con QNAPs funcionales
- ✅ Sistema de handlers mock/real

### v2.2 - Julio 2025 (ACTUAL)
- ✅ **BÚSQUEDA RECURSIVA** en QNAPs y PCs
- ✅ Eliminación en todas las subcarpetas
- ✅ Logging con rutas completas
