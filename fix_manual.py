#!/usr/bin/env python3
"""
fix_manual.py
Corrector manual directo para el error específico de sintaxis

Este script hace una corrección quirúrgica del problema específico.
"""

from pathlib import Path
from datetime import datetime

def main():
    """Función principal de corrección manual"""
    
    print("🔧 CORRECTOR MANUAL DIRECTO")
    print("=" * 30)
    
    dashboard_file = Path("dashboard.py")
    
    if not dashboard_file.exists():
        print("❌ ERROR: dashboard.py no encontrado")
        input("Presiona Enter para salir...")
        return False
    
    # Leer todo el contenido
    print(f"📖 Leyendo archivo completo...")
    content = dashboard_file.read_text(encoding='utf-8')
    
    # Hacer backup
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = Path(f"dashboard_backup_manual_{timestamp}.py")
    backup_file.write_text(content, encoding='utf-8')
    print(f"💾 Backup: {backup_file.name}")
    
    # <PERSON>car y reemplazar el patrón problemático
    print(f"🔍 Buscando patrón problemático...")
    
    # Patrón problemático: función main() vacía
    old_pattern = '''def main():
    """Función principal para ejecutar el dashboard"""'''
    
    # Nuevo contenido completo
    new_pattern = '''def main():
    """Función principal para ejecutar el dashboard"""
    import sys
    
    # Configurar logging básico
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger('dashboard.main')
    
    try:
        logger.info("=== INICIANDO GESTOR DE ARCHIVOS CORPORATIVO ===")
        
        # Verificar dependencias críticas
        try:
            import tkinter
            import threading
            from pathlib import Path
        except ImportError as e:
            logger.error(f"Dependencia faltante: {e}")
            print(f"❌ Error: Dependencia faltante - {e}")
            input("Presione Enter para salir...")
            sys.exit(1)
        
        # Crear y ejecutar dashboard
        dashboard = GestorDashboard()
        dashboard.run()
        
    except KeyboardInterrupt:
        logger.info("Aplicación interrumpida por el usuario (Ctrl+C)")
        print("\\n🛑 Aplicación detenida por el usuario")
        
    except Exception as e:
        logger.error(f"Error fatal en main: {e}")
        print(f"❌ Error fatal: {e}")
        
        # Mostrar información de debug si es necesario
        if "--debug" in sys.argv:
            import traceback
            print("\\n🔍 Información de debug:")
            traceback.print_exc()
        
        input("Presione Enter para salir...")
        sys.exit(1)'''
    
    # Reemplazar
    if old_pattern in content:
        print(f"✅ Patrón encontrado - Reemplazando...")
        content = content.replace(old_pattern, new_pattern)
    else:
        print(f"⚠️  Patrón no encontrado - Intentando otra estrategia...")
        
        # Buscar solo la línea de la función
        lines = content.splitlines()
        for i, line in enumerate(lines):
            if line.strip() == 'def main():':
                print(f"📍 Función main() encontrada en línea {i+1}")
                
                # Reemplazar desde esta línea
                new_lines = lines[:i]  # Líneas antes
                
                # Agregar función main completa
                new_lines.extend([
                    'def main():',
                    '    """Función principal para ejecutar el dashboard"""',
                    '    import sys',
                    '    ',
                    '    # Configurar logging básico',
                    '    logging.basicConfig(',
                    '        level=logging.INFO,',
                    '        format=\'%(asctime)s - %(name)s - %(levelname)s - %(message)s\'',
                    '    )',
                    '    ',
                    '    logger = logging.getLogger(\'dashboard.main\')',
                    '    ',
                    '    try:',
                    '        logger.info("=== INICIANDO GESTOR DE ARCHIVOS CORPORATIVO ===")',
                    '        ',
                    '        # Verificar dependencias críticas',
                    '        try:',
                    '            import tkinter',
                    '            import threading',
                    '            from pathlib import Path',
                    '        except ImportError as e:',
                    '            logger.error(f"Dependencia faltante: {e}")',
                    '            print(f"❌ Error: Dependencia faltante - {e}")',
                    '            input("Presione Enter para salir...")',
                    '            sys.exit(1)',
                    '        ',
                    '        # Crear y ejecutar dashboard',
                    '        dashboard = GestorDashboard()',
                    '        dashboard.run()',
                    '        ',
                    '    except KeyboardInterrupt:',
                    '        logger.info("Aplicación interrumpida por el usuario (Ctrl+C)")',
                    '        print("\\n🛑 Aplicación detenida por el usuario")',
                    '        ',
                    '    except Exception as e:',
                    '        logger.error(f"Error fatal en main: {e}")',
                    '        print(f"❌ Error fatal: {e}")',
                    '        ',
                    '        # Mostrar información de debug si es necesario',
                    '        if "--debug" in sys.argv:',
                    '            import traceback',
                    '            print("\\n🔍 Información de debug:")',
                    '            traceback.print_exc()',
                    '        ',
                    '        input("Presione Enter para salir...")',
                    '        sys.exit(1)',
                    '',
                    '',
                    'if __name__ == "__main__":',
                    '    main()'
                ])
                
                content = '\\n'.join(new_lines)
                break
    
    # Asegurar que hay bloque if __name__ al final
    if 'if __name__ == "__main__":' not in content:
        print(f"➕ Agregando bloque if __name__...")
        content += '\\n\\nif __name__ == "__main__":\\n    main()\\n'
    
    # Escribir archivo corregido
    print(f"💾 Escribiendo archivo corregido...")
    dashboard_file.write_text(content, encoding='utf-8')
    
    # Estadísticas
    new_size = dashboard_file.stat().st_size / 1024
    new_lines = len(content.splitlines())
    
    print(f"\\n🎉 ¡CORRECCIÓN MANUAL COMPLETADA!")
    print(f"=" * 35)
    print(f"📏 Tamaño final: {new_size:.1f} KB")
    print(f"📊 Líneas finales: {new_lines:,}")
    
    # Verificación final
    print(f"\\n🔍 Verificación final...")
    if verify_syntax(dashboard_file):
        print(f"  ✅ ¡SINTAXIS PERFECTA!")
        print(f"  ✅ Archivo completamente funcional")
        
        print(f"\\n🚀 ¡ÉXITO TOTAL!")
        print(f"  Ejecuta: python dashboard.py")
        print(f"  🎉 Dashboard de 4,000+ líneas listo!")
        
        return True
    else:
        return False

def verify_syntax(file_path: Path) -> bool:
    """Verificar que la sintaxis sea correcta"""
    try:
        content = file_path.read_text(encoding='utf-8')
        compile(content, str(file_path), 'exec')
        return True
    except SyntaxError as e:
        print(f"    ❌ Error de sintaxis: {e}")
        print(f"    📍 Línea {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"    ❌ Error verificando: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        input("\\nPresiona Enter para continuar...")
        if not success:
            print("\\n💡 Si aún hay errores, revisa manualmente el archivo.")
            exit(1)
    except KeyboardInterrupt:
        print(f"\\n\\n🛑 Operación cancelada")
        exit(1)
    except Exception as e:
        print(f"\\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        input("Presiona Enter para salir...")
        exit(1)
