"""
dashboard_pieza1.py
PIEZA 1: Estructura base + imports + logo + inicialización
Gestor de Archivos Corporativo
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
from pathlib import Path
import logging
from typing import Dict, Any
from datetime import datetime

# Importaciones para imágenes
try:
    import PIL.Image
    import PIL.ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

class GestorDashboard:
    """Dashboard principal con interfaz por pestañas - VERSIÓN COMPLETA"""
    
    def __init__(self):
        """Inicializar dashboard"""
        self.setup_logging()
        self.setup_ui()  # Crear ventana principal PRIMERO
        self.setup_variables()  # Variables DESPUÉS de la ventana
        self.create_tabs()  # Crear pestañas AL FINAL
        self.setup_threads()  # Threads para auto-actualización
        self.load_saved_config()  # Cargar configuración guardada
        self.initial_connectivity_check()  # Check inicial de conectividad
        
        self.logger.info("Dashboard completo inicializado correctamente")
    
    def setup_logging(self):
        """Configurar sistema de logging"""
        try:
            from core.logger import gestor_logger
            self.logger = gestor_logger.get_logger('sistema')
        except ImportError:
            logging.basicConfig(level=logging.INFO)
            self.logger = logging.getLogger('dashboard')
    
    def setup_variables(self):
        """Configurar variables de estado"""
        # Estado de operaciones
        self.operaciones_activas = {'validacion': False, 'limpieza': False}
        
        # Variables de configuración - validación
        self.validacion_origen_var = tk.StringVar()
        self.validacion_rechazados_var = tk.StringVar() 
        self.validacion_dry_run_var = tk.BooleanVar(value=True)
        
        # Variables de configuración - limpieza
        self.limpieza_archivo_var = tk.StringVar()
        self.limpieza_dry_run_var = tk.BooleanVar(value=True)
        
        # Variables de estado y logs
        self.connectivity_status = {}
        self.log_category_var = tk.StringVar(value='sistema')
        self.auto_refresh_logs = tk.BooleanVar(value=False)
        
        # Variables de estadísticas
        self.last_operation_stats = {}
        
        # Variables para threads
        self.shutdown_threads = False
    
    def setup_ui(self):
        """Configurar interfaz de usuario principal"""
        # Ventana principal
        self.root = tk.Tk()
        self.root.title("Gestor de Archivos Corporativo v1.0")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 700)
        
        # Configurar icono de ventana
        self.setup_window_icon()
        
        # Configurar estilos
        self.setup_styles()
        
        # Crear estructura principal
        self.create_main_structure()
        
        # Configurar eventos de ventana
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_window_icon(self):
        """Configurar icono de la ventana"""
        try:
            logo_path = Path(__file__).parent.parent.parent / "logo_igson.png"
            if logo_path.exists() and PIL_AVAILABLE:
                # Cargar como icono de ventana
                icon_img = PIL.Image.open(logo_path)
                icon_img = icon_img.resize((32, 32), PIL.Image.Resampling.LANCZOS)
                self.window_icon = PIL.ImageTk.PhotoImage(icon_img)
                self.root.iconphoto(False, self.window_icon)
                self.logger.info("Icono de ventana cargado correctamente")
        except Exception as e:
            self.logger.debug(f"No se pudo cargar el icono de ventana: {e}")
    
    def setup_styles(self):
        """Configurar estilos personalizados mejorados"""
        style = ttk.Style()
        
        # Estilos para estado de conectividad
        style.configure('Online.TLabel', foreground='#2E7D32', font=('Arial', 9, 'bold'))  # Verde oscuro
        style.configure('Offline.TLabel', foreground='#C62828', font=('Arial', 9, 'bold'))  # Rojo oscuro
        style.configure('Warning.TLabel', foreground='#F57C00', font=('Arial', 9, 'bold'))  # Naranja
        style.configure('Testing.TLabel', foreground='#1976D2', font=('Arial', 9, 'bold'))  # Azul
        
        # Estilos para texto
        style.configure('Status.TLabel', font=('Arial', 9))
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Subtitle.TLabel', font=('Arial', 10))
        style.configure('Small.TLabel', font=('Arial', 8), foreground='gray')
        
        # Estilos para botones
        style.configure('Action.TButton', font=('Arial', 9, 'bold'))
        style.configure('Danger.TButton', font=('Arial', 9, 'bold'))
    
    def create_main_structure(self):
        """Crear estructura principal de la aplicación"""
        # Header con logo y título
        self.create_header()
        
        # Notebook principal
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        # Barra de estado
        self.setup_status_bar()
    
    def create_header(self):
        """Crear header con logo y título"""
        header_frame = ttk.Frame(self.root)
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Logo principal
        if PIL_AVAILABLE:
            try:
                logo_path = Path(__file__).parent.parent.parent / "logo_igson.png"
                if logo_path.exists():
                    img = PIL.Image.open(logo_path)
                    img = img.resize((64, 64), PIL.Image.Resampling.LANCZOS)
                    self.logo_main = PIL.ImageTk.PhotoImage(img)
                    
                    ttk.Label(header_frame, image=self.logo_main).pack(side=tk.LEFT, padx=10)
                    self.logger.info("Logo principal cargado correctamente")
            except Exception as e:
                self.logger.debug(f"Logo principal no disponible: {e}")
        
        # Información del título
        title_frame = ttk.Frame(header_frame)
        title_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=20)
        
        ttk.Label(title_frame, text="Gestor de Archivos Corporativo", 
                 style='Title.TLabel').pack(anchor=tk.W)
        ttk.Label(title_frame, text="Sistema de Validación y Limpieza de Archivos", 
                 style='Subtitle.TLabel').pack(anchor=tk.W)
        ttk.Label(title_frame, text="v1.0 - Desarrollado por Igson", 
                 style='Small.TLabel').pack(anchor=tk.W)
        
        # Información de estado en tiempo real
        status_info_frame = ttk.Frame(header_frame)
        status_info_frame.pack(side=tk.RIGHT, padx=10)
        
        # Reloj en tiempo real
        self.clock_label = ttk.Label(status_info_frame, text="", style='Status.TLabel')
        self.clock_label.pack(anchor=tk.E)
        
        # Contador de conexiones
        self.connection_summary_label = ttk.Label(status_info_frame, text="Conexiones: 0/6", style='Status.TLabel')
        self.connection_summary_label.pack(anchor=tk.E)
        
        # Iniciar actualización del reloj
        self.update_clock()
        
        # Separador
        ttk.Separator(self.root, orient='horizontal').pack(fill=tk.X, padx=10, pady=5)
    
    def setup_status_bar(self):
        """Configurar barra de estado avanzada"""
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=10, pady=5)
        
        # Logo pequeño en la barra
        if PIL_AVAILABLE:
            try:
                logo_path = Path(__file__).parent.parent.parent / "logo_igson.png"
                if logo_path.exists():
                    img = PIL.Image.open(logo_path)
                    img = img.resize((16, 16), PIL.Image.Resampling.LANCZOS)
                    self.logo_small = PIL.ImageTk.PhotoImage(img)
                    
                    ttk.Label(self.status_frame, image=self.logo_small).pack(side=tk.LEFT, padx=5)
            except Exception as e:
                self.logger.debug(f"Logo pequeño no disponible: {e}")
        
        # Mensaje de estado principal
        self.status_label = ttk.Label(self.status_frame, text="Sistema iniciado - Listo", style='Status.TLabel')
        self.status_label.pack(side=tk.LEFT, padx=10)
        
        # Frame para controles de la derecha
        right_controls = ttk.Frame(self.status_frame)
        right_controls.pack(side=tk.RIGHT)
        
        # Indicador de operaciones activas
        self.operations_indicator = ttk.Label(right_controls, text="⭕ Sin operaciones", style='Status.TLabel')
        self.operations_indicator.pack(side=tk.RIGHT, padx=5)
        
        # Botón de actualización general
        ttk.Button(right_controls, text="🔄 Actualizar", 
                  command=self.refresh_all).pack(side=tk.RIGHT, padx=5)
        
        # Botón de configuración
        ttk.Button(right_controls, text="⚙️ Config", 
                  command=self.show_config_dialog).pack(side=tk.RIGHT, padx=5)
    
    def update_clock(self):
        """Actualizar reloj en tiempo real"""
        try:
            current_time = datetime.now().strftime("%H:%M:%S")
            current_date = datetime.now().strftime("%Y-%m-%d")
            self.clock_label.config(text=f"{current_date} {current_time}")
            
            # Programar próxima actualización
            self.root.after(1000, self.update_clock)
        except Exception as e:
            self.logger.error(f"Error actualizando reloj: {e}")
    
    def update_status(self, message: str, level: str = "info"):
        """Actualizar mensaje de estado con nivel"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        status_message = f"[{timestamp}] {message}"
        
        # Colores según nivel
        colors = {
            "info": "black",
            "warning": "#F57C00",
            "error": "#C62828",
            "success": "#2E7D32"
        }
        
        self.status_label.config(text=status_message)
        if level in colors:
            self.status_label.config(foreground=colors[level])
        
        # Log del mensaje
        if level == "error":
            self.logger.error(message)
        elif level == "warning":
            self.logger.warning(message)
        else:
            self.logger.info(message)
    
    def update_operations_indicator(self):
        """Actualizar indicador de operaciones activas"""
        active_ops = [op for op, active in self.operaciones_activas.items() if active]
        
        if not active_ops:
            self.operations_indicator.config(text="⭕ Sin operaciones", foreground="gray")
        elif len(active_ops) == 1:
            op_names = {"validacion": "Validación", "limpieza": "Limpieza"}
            op_name = op_names.get(active_ops[0], active_ops[0])
            self.operations_indicator.config(text=f"🔄 {op_name}", foreground="#1976D2")
        else:
            self.operations_indicator.config(text=f"🔄 {len(active_ops)} operaciones", foreground="#1976D2")
    
    def update_connection_counter(self):
        """Actualizar contador de conexiones"""
        try:
            online_count = 0
            total_count = len(getattr(self, 'connectivity_labels', {}))
            
            for location_id, label in getattr(self, 'connectivity_labels', {}).items():
                if hasattr(label, 'cget') and "🟢" in label.cget('text'):
                    online_count += 1
            
            # Actualizar en header
            self.connection_summary_label.config(text=f"Conexiones: {online_count}/{total_count}")
            
            # Color según estado
            if online_count == total_count:
                color = "#2E7D32"  # Verde
            elif online_count > total_count // 2:
                color = "#F57C00"  # Naranja
            else:
                color = "#C62828"  # Rojo
            
            self.connection_summary_label.config(foreground=color)
            
        except Exception as e:
            self.logger.error(f"Error actualizando contador de conexiones: {e}")
    
    def show_config_dialog(self):
        """Mostrar diálogo de configuración"""
        config_window = tk.Toplevel(self.root)
        config_window.title("Configuración")
        config_window.geometry("400x300")
        config_window.transient(self.root)
        config_window.grab_set()
        
        ttk.Label(config_window, text="Configuración del Sistema", 
                 style='Header.TLabel').pack(pady=10)
        
        # Notebook para diferentes configuraciones
        config_notebook = ttk.Notebook(config_window)
        config_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Tab: General
        general_frame = ttk.Frame(config_notebook)
        config_notebook.add(general_frame, text="General")
        
        ttk.Label(general_frame, text="Configuración general del sistema").pack(pady=10)
        
        # Auto-refresh logs
        ttk.Checkbutton(general_frame, text="Auto-actualizar logs", 
                       variable=self.auto_refresh_logs).pack(anchor=tk.W, padx=10, pady=5)
        
        # Tab: Conexiones
        conn_frame = ttk.Frame(config_notebook)
        config_notebook.add(conn_frame, text="Conexiones")
        
        ttk.Label(conn_frame, text="Configuración de conexiones").pack(pady=10)
        ttk.Label(conn_frame, text="(Configuración avanzada en archivos JSON)").pack(pady=5)
        
        # Botones
        buttons_frame = ttk.Frame(config_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(buttons_frame, text="Cerrar", 
                  command=config_window.destroy).pack(side=tk.RIGHT, padx=5)
        ttk.Button(buttons_frame, text="Aplicar", 
                  command=lambda: self.apply_config(config_window)).pack(side=tk.RIGHT, padx=5)
    
    def apply_config(self, config_window):
        """Aplicar configuración"""
        try:
            # Aquí se aplicarían los cambios de configuración
            self.update_status("Configuración aplicada", "success")
            config_window.destroy()
        except Exception as e:
            self.update_status(f"Error aplicando configuración: {e}", "error")

# FIN PIEZA 1

    """
    dashboard_pieza2.py
    PIEZA 2: Conectividad real avanzada + dashboard tab + threads
    Gestor de Archivos Corporativo
    """

# CONTINUACIÓN DE LA CLASE GestorDashboard

    def setup_threads(self):
        """Configurar threads para auto-actualización"""
        # Thread para auto-refresh de logs
        self.log_refresh_thread = threading.Thread(target=self.auto_refresh_logs_worker, daemon=True)
        self.log_refresh_thread.start()
        
        # Thread para estado de conectividad periódico
        self.connectivity_thread = threading.Thread(target=self.connectivity_worker, daemon=True)
        self.connectivity_thread.start()
        
        # Thread para actualización de estadísticas
        self.stats_thread = threading.Thread(target=self.stats_worker, daemon=True)
        self.stats_thread.start()
        
        self.logger.info("Threads de background iniciados")
    
    def auto_refresh_logs_worker(self):
        """Worker para auto-actualización de logs"""
        while not self.shutdown_threads:
            try:
                time.sleep(5)  # Actualizar cada 5 segundos
                if self.auto_refresh_logs.get():
                    self.root.after(0, self.refresh_logs)
            except Exception as e:
                self.logger.error(f"Error en auto-refresh de logs: {e}")
                time.sleep(10)  # Esperar más tiempo si hay error
    
    def connectivity_worker(self):
        """Worker para monitoreo periódico de conectividad"""
        while not self.shutdown_threads:
            try:
                time.sleep(60)  # Verificar cada 60 segundos
                self.root.after(0, self.periodic_connectivity_check)
            except Exception as e:
                self.logger.error(f"Error en monitoreo de conectividad: {e}")
                time.sleep(30)
    
    def stats_worker(self):
        """Worker para actualización de estadísticas"""
        while not self.shutdown_threads:
            try:
                time.sleep(30)  # Actualizar cada 30 segundos
                self.root.after(0, self.update_system_stats)
            except Exception as e:
                self.logger.error(f"Error en stats worker: {e}")
                time.sleep(60)
    
    def initial_connectivity_check(self):
        """Check inicial de conectividad al arrancar"""
        self.update_status("Realizando check inicial de conectividad...", "info")
        
        def initial_check_worker():
            try:
                time.sleep(2)  # Esperar que la UI esté lista
                self.root.after(0, self.test_all_connections_silent)
            except Exception as e:
                self.logger.error(f"Error en check inicial: {e}")
        
        threading.Thread(target=initial_check_worker, daemon=True).start()
    
    def periodic_connectivity_check(self):
        """Check periódico de conectividad (menos intrusivo)"""
        try:
            from core.config_manager import config_manager
            
            locations = config_manager.get_enabled_locations()
            
            for location_id in locations:
                if location_id in getattr(self, 'connectivity_labels', {}):
                    # Solo actualizar si no está siendo testeado manualmente
                    current_text = self.connectivity_labels[location_id].cget('text')
                    if "🔄" not in current_text:  # No está en test manual
                        self.test_single_location_background(location_id)
            
            self.update_connection_counter()
            
        except Exception as e:
            self.logger.error(f"Error en check periódico: {e}")
    
    def test_single_location_background(self, location_id: str):
        """Test de una ubicación en background (no bloquea UI)"""
        def bg_test_worker():
            try:
                result = self.test_location_connectivity_real(location_id)
                self.root.after(0, lambda: self.update_location_status_quiet(location_id, result))
            except Exception as e:
                self.logger.debug(f"Error en test background {location_id}: {e}")
        
        threading.Thread(target=bg_test_worker, daemon=True).start()
    
    def update_location_status_quiet(self, location_id: str, result: Dict[str, Any]):
        """Actualizar estado de ubicación sin interrumpir UI"""
        try:
            if location_id not in getattr(self, 'connectivity_labels', {}):
                return
            
            status_label = self.connectivity_labels[location_id]
            details_label = getattr(self, 'connectivity_details', {}).get(location_id)
            
            # Actualizar estado interno
            self.connectivity_status[location_id] = result
            
            # Actualizar UI solo si cambió el estado
            current_text = status_label.cget('text')
            new_online = result['connected']
            current_online = "🟢" in current_text
            
            if new_online != current_online:  # Solo si cambió el estado
                if new_online:
                    status_label.config(text="🟢 ONLINE", foreground='#2E7D32')
                    if details_label:
                        details = result.get('details', 'Conectado')
                        details_label.config(text=details, foreground='#2E7D32')
                else:
                    status_label.config(text="🔴 OFFLINE", foreground='#C62828')
                    if details_label:
                        error_text = result.get('details', result.get('error', 'Error'))
                        details_label.config(text=error_text, foreground='#C62828')
                
                self.update_connection_counter()
        
        except Exception as e:
            self.logger.error(f"Error actualizando estado quiet {location_id}: {e}")
    
    def create_tabs(self):
        """Crear todas las pestañas"""
        self.create_dashboard_tab()
        self.create_validacion_tab()
        self.create_limpieza_tab()
        self.create_logs_tab()
        
        # Seleccionar la primera pestaña
        self.notebook.select(0)
    
    def create_dashboard_tab(self):
        """Crear pestaña de dashboard principal con conectividad avanzada"""
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="📊 Dashboard")
        
        # Frame principal con scroll
        main_canvas = tk.Canvas(dashboard_frame)
        scrollbar = ttk.Scrollbar(dashboard_frame, orient="vertical", command=main_canvas.yview)
        scrollable_frame = ttk.Frame(main_canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all"))
        )
        
        main_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        main_canvas.configure(yscrollcommand=scrollbar.set)
        
        # Pack scroll components
        main_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # === SECCIÓN: CONECTIVIDAD AVANZADA ===
        connectivity_frame = ttk.LabelFrame(scrollable_frame, text="🌐 Estado de Conectividad en Tiempo Real", padding="15")
        connectivity_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Controles superiores
        controls_frame = ttk.Frame(connectivity_frame)
        controls_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Button(controls_frame, text="🔍 Test Todas", style='Action.TButton',
                  command=self.test_all_connections).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(controls_frame, text="🔄 Actualizar", 
                  command=self.update_connectivity_status).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(controls_frame, text="📊 Reporte Detallado", 
                  command=self.show_detailed_report).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(controls_frame, text="⚙️ Configurar Conexiones", 
                  command=self.show_connection_config).pack(side=tk.RIGHT, padx=5)
        
        # Grid de ubicaciones mejorado
        self.connectivity_labels = {}
        self.connectivity_details = {}
        self.connectivity_buttons = {}
        
        locations_info = {
            'qnap1': {'name': 'QNAP 1', 'type': 'NAS', 'icon': '🏢'},
            'qnap2': {'name': 'QNAP 2', 'type': 'NAS', 'icon': '🏢'},
            'pc1': {'name': 'PC Oficina 1', 'type': 'PC', 'icon': '💻'},
            'pc2': {'name': 'PC Oficina 2', 'type': 'PC', 'icon': '💻'},
            'pc3': {'name': 'PC Remoto 1', 'type': 'PC', 'icon': '💻'},
            'pc4': {'name': 'PC Remoto 2', 'type': 'PC', 'icon': '💻'}
        }
        
        for i, (location_id, info) in enumerate(locations_info.items()):
            row = i // 2
            col = i % 2
            
            # Frame individual por ubicación
            loc_frame = ttk.LabelFrame(connectivity_frame, text=f"{info['icon']} {info['name']}", padding="10")
            loc_frame.grid(row=row, column=col, padx=10, pady=10, sticky='ew')
            
            # Fila 1: Estado y botón test
            status_row = ttk.Frame(loc_frame)
            status_row.pack(fill=tk.X, pady=(0, 5))
            
            status_label = ttk.Label(status_row, text="🔄 Verificando...", style='Testing.TLabel')
            status_label.pack(side=tk.LEFT)
            
            test_btn = ttk.Button(status_row, text="Test", width=8,
                                 command=lambda loc=location_id: self.test_individual_connection(loc))
            test_btn.pack(side=tk.RIGHT)
            
            # Fila 2: Detalles técnicos
            details_label = ttk.Label(loc_frame, text="Esperando conexión...", 
                                    font=('Arial', 8), foreground='gray')
            details_label.pack(anchor=tk.W)
            
            # Fila 3: Información adicional
            extra_info = ttk.Label(loc_frame, text=f"Tipo: {info['type']} | ID: {location_id}", 
                                 font=('Arial', 7), foreground='lightgray')
            extra_info.pack(anchor=tk.W, pady=(2, 0))
            
            # Guardar referencias
            self.connectivity_labels[location_id] = status_label
            self.connectivity_details[location_id] = details_label
            self.connectivity_buttons[location_id] = test_btn
        
        # Configurar grid weights para responsive design
        for col in range(2):
            connectivity_frame.columnconfigure(col, weight=1)
        
        # === SECCIÓN: ESTADÍSTICAS DEL SISTEMA ===
        stats_frame = ttk.LabelFrame(scrollable_frame, text="📈 Estadísticas del Sistema", padding="15")
        stats_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Notebook para diferentes tipos de información
        stats_notebook = ttk.Notebook(stats_frame)
        stats_notebook.pack(fill=tk.BOTH, expand=True)
        
        # Tab 1: Resumen del sistema
        summary_frame = ttk.Frame(stats_notebook)
        stats_notebook.add(summary_frame, text="💻 Sistema")
        
        self.system_info_text = tk.Text(summary_frame, height=8, wrap=tk.WORD, 
                                       font=('Consolas', 9), state='disabled')
        system_scroll = ttk.Scrollbar(summary_frame, orient=tk.VERTICAL, 
                                     command=self.system_info_text.yview)
        self.system_info_text.configure(yscrollcommand=system_scroll.set)
        
        self.system_info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        system_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Tab 2: Estadísticas de conectividad
        connectivity_stats_frame = ttk.Frame(stats_notebook)
        stats_notebook.add(connectivity_stats_frame, text="🌐 Conectividad")
        
        self.connectivity_stats_text = tk.Text(connectivity_stats_frame, height=8, wrap=tk.WORD,
                                             font=('Consolas', 9), state='disabled')
        conn_stats_scroll = ttk.Scrollbar(connectivity_stats_frame, orient=tk.VERTICAL,
                                        command=self.connectivity_stats_text.yview)
        self.connectivity_stats_text.configure(yscrollcommand=conn_stats_scroll.set)
        
        self.connectivity_stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        conn_stats_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Tab 3: Historial de operaciones
        operations_frame = ttk.Frame(stats_notebook)
        stats_notebook.add(operations_frame, text="⚙️ Operaciones")
        
        self.operations_stats_text = tk.Text(operations_frame, height=8, wrap=tk.WORD,
                                           font=('Consolas', 9), state='disabled')
        ops_scroll = ttk.Scrollbar(operations_frame, orient=tk.VERTICAL,
                                 command=self.operations_stats_text.yview)
        self.operations_stats_text.configure(yscrollcommand=ops_scroll.set)
        
        self.operations_stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        ops_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Actualizar información inicial
        self.update_system_info()
        self.update_connectivity_stats()
        self.update_operations_stats()
    
    def test_individual_connection(self, location_id: str):
        """Test de conexión individual con feedback visual mejorado"""
        def test_worker():
            try:
                # Actualizar UI - iniciando test
                self.root.after(0, lambda: self.set_location_testing_state(location_id, True))
                
                # Test real con timeout
                result = self.test_location_connectivity_real(location_id)
                
                # Actualizar UI con resultado
                self.root.after(0, lambda: self.update_individual_connection_display(location_id, result))
                
            except Exception as e:
                error_result = {
                    'connected': False,
                    'error': f"Error en test: {str(e)}",
                    'details': f"Excepción durante test: {str(e)[:50]}..."
                }
                self.root.after(0, lambda: self.update_individual_connection_display(location_id, error_result))
            finally:
                self.root.after(0, lambda: self.set_location_testing_state(location_id, False))
        
        threading.Thread(target=test_worker, daemon=True).start()
    
    def set_location_testing_state(self, location_id: str, testing: bool):
        """Establecer estado visual de testing"""
        try:
            if location_id in self.connectivity_labels:
                if testing:
                    self.connectivity_labels[location_id].config(text="🔄 Probando...", style='Testing.TLabel')
                    self.connectivity_details[location_id].config(text="Ejecutando test de conectividad...", foreground='#1976D2')
                    self.connectivity_buttons[location_id].config(state='disabled')
                else:
                    self.connectivity_buttons[location_id].config(state='normal')
        except Exception as e:
            self.logger.error(f"Error estableciendo estado testing {location_id}: {e}")
    
    def test_location_connectivity_real(self, location_id: str) -> Dict[str, Any]:
        """Test REAL y completo de conectividad para una ubicación"""
        try:
            from core.config_manager import config_manager
            from core.auth_manager import auth_manager
            from core.base_connection import BaseConnection
            
            location_config = config_manager.get_location_config(location_id)
            if not location_config:
                return {
                    'connected': False, 
                    'error': 'No configurado', 
                    'details': 'Ubicación no encontrada en configuración'
                }
            
            # Crear conexión real
            connection = BaseConnection(location_id, location_config, auth_manager)
            
            # Test de conectividad básica
            start_time = time.time()
            basic_result = connection.test_connection()
            basic_time = time.time() - start_time
            
            if not basic_result['success']:
                return {
                    'connected': False,
                    'error': basic_result.get('error', 'Test falló'),
                    'response_time': basic_time,
                    'details': f"Conexión falló en {basic_time:.2f}s: {basic_result.get('error', 'Unknown')}"
                }
            
            # Test de acceso de escritura
            write_start = time.time()
            write_result = connection.test_write_access()
            write_time = time.time() - write_start
            
            # Test de velocidad (opcional)
            speed_info = ""
            try:
                speed_test = connection.test_speed()
                if speed_test.get('success', False):
                    speed_info = f" | Velocidad: {speed_test.get('speed_mbps', 0):.1f} MB/s"
            except:
                pass  # Speed test es opcional
            
            total_time = basic_time + write_time
            
            return {
                'connected': True,
                'response_time': total_time,
                'method': basic_result.get('method', 'unknown'),
                'write_access': write_result.get('success', False),
                'details': f"OK vía {basic_result.get('method', 'unknown')} ({total_time:.2f}s)" + 
                          (f" | ✅ Escritura" if write_result.get('success', False) else f" | ⚠️ Solo lectura") + 
                          speed_info
            }
        
        except Exception as e:
            return {
                'connected': False, 
                'error': str(e),
                'details': f"Error: {str(e)[:80]}..."
            }
    
    def update_individual_connection_display(self, location_id: str, result: Dict[str, Any]):
        """Actualizar display individual con información detallada"""
        try:
            if location_id not in self.connectivity_labels:
                return
            
            status_label = self.connectivity_labels[location_id]
            details_label = self.connectivity_details[location_id]
            
            # Actualizar estado interno
            self.connectivity_status[location_id] = result
            
            if result['connected']:
                status_label.config(text="🟢 ONLINE", style='Online.TLabel')
                
                # Detalles técnicos mejorados
                details = result.get('details', 'Conectado correctamente')
                details_label.config(text=details, foreground='#2E7D32')
                
            else:
                status_label.config(text="🔴 OFFLINE", style='Offline.TLabel')
                
                error_details = result.get('details', result.get('error', 'Error desconocido'))
                details_label.config(text=error_details, foreground='#C62828')
            
            # Actualizar contadores
            self.update_connection_counter()
            self.update_connectivity_stats()
            
            # Log del resultado
            status = "CONECTADO" if result['connected'] else "DESCONECTADO"
            self.logger.info(f"Test {location_id}: {status} - {result.get('details', '')}")
            
        except Exception as e:
            self.logger.error(f"Error actualizando display {location_id}: {e}")
    
    def test_all_connections(self):
        """Test todas las conexiones con progreso visual"""
        self.update_status("Iniciando test de todas las conexiones...", "info")
        
        def test_all_worker():
            try:
                from core.config_manager import config_manager
                
                locations = config_manager.get_enabled_locations()
                results = []
                
                for i, location_id in enumerate(locations):
                    # Actualizar progreso
                    progress_msg = f"Probando conexiones... ({i+1}/{len(locations)})"
                    self.root.after(0, lambda msg=progress_msg: self.update_status(msg, "info"))
                    
                    # Test individual
                    self.root.after(0, lambda loc=location_id: self.set_location_testing_state(loc, True))
                    
                    result = self.test_location_connectivity_real(location_id)
                    results.append((location_id, result))
                    
                    # Actualizar UI
                    self.root.after(0, lambda loc=location_id, res=result: self.update_individual_connection_display(loc, res))
                    self.root.after(0, lambda loc=location_id: self.set_location_testing_state(loc, False))
                    
                    # Pequeña pausa entre tests
                    time.sleep(0.5)
                
                # Mostrar resultados finales
                self.root.after(0, lambda: self.show_connection_test_results(results))
                self.root.after(0, lambda: self.update_status("Test de conexiones completado", "success"))
                
            except Exception as e:
                error_msg = f"Error en test masivo: {e}"
                self.root.after(0, lambda: self.update_status(error_msg, "error"))
        
        threading.Thread(target=test_all_worker, daemon=True).start()
    
    def test_all_connections_silent(self):
        """Test todas las conexiones en modo silencioso (para arranque)"""
        def silent_test_worker():
            try:
                from core.config_manager import config_manager
                
                locations = config_manager.get_enabled_locations()
                
                for location_id in locations:
                    if location_id in self.connectivity_labels:
                        result = self.test_location_connectivity_real(location_id)
                        self.root.after(0, lambda loc=location_id, res=result: 
                                      self.update_location_status_quiet(loc, res))
                        time.sleep(0.2)  # Pausa corta
                
                self.root.after(0, lambda: self.update_status("Check inicial completado", "success"))
                
            except Exception as e:
                self.logger.error(f"Error en test silencioso: {e}")
        
        threading.Thread(target=silent_test_worker, daemon=True).start()

# FIN PIEZA 2
    """
    dashboard_pieza3a.py
    PIEZA 3A: Pestaña de Validación - UI y estructura
    Gestor de Archivos Corporativo
    """

# CONTINUACIÓN DE LA CLASE GestorDashboard - PESTAÑA VALIDACIÓN

    def create_validacion_tab(self):
        """Crear pestaña de validación de duplicados avanzada"""
        validacion_frame = ttk.Frame(self.notebook)
        self.notebook.add(validacion_frame, text="🔍 Validación")
        
        # Header de la pestaña
        header_frame = ttk.Frame(validacion_frame)
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(header_frame, text="🔍 Validación de Duplicados", 
                 style='Title.TLabel').pack(side=tk.LEFT)
        
        # Botón de ayuda
        ttk.Button(header_frame, text="❓ Ayuda", 
                  command=self.show_validacion_help).pack(side=tk.RIGHT, padx=5)
        
        # Frame principal con scroll
        main_canvas = tk.Canvas(validacion_frame)
        scrollbar_val = ttk.Scrollbar(validacion_frame, orient="vertical", command=main_canvas.yview)
        scrollable_val_frame = ttk.Frame(main_canvas)
        
        scrollable_val_frame.bind(
            "<Configure>",
            lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all"))
        )
        
        main_canvas.create_window((0, 0), window=scrollable_val_frame, anchor="nw")
        main_canvas.configure(yscrollcommand=scrollbar_val.set)
        
        main_canvas.pack(side="left", fill="both", expand=True)
        scrollbar_val.pack(side="right", fill="y")
        
        # === SECCIÓN: CONFIGURACIÓN ===
        config_frame = ttk.LabelFrame(scrollable_val_frame, text="⚙️ Configuración de Validación", padding="15")
        config_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Grid para configuración
        config_grid = ttk.Frame(config_frame)
        config_grid.pack(fill=tk.X)
        
        # Carpeta origen
        ttk.Label(config_grid, text="📁 Carpeta Origen:", style='Status.TLabel').grid(row=0, column=0, sticky='w', pady=5)
        origen_frame = ttk.Frame(config_grid)
        origen_frame.grid(row=0, column=1, sticky='ew', padx=5)
        
        self.validacion_origen_entry = ttk.Entry(origen_frame, textvariable=self.validacion_origen_var, font=('Consolas', 9))
        self.validacion_origen_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Button(origen_frame, text="📂", width=3, 
                  command=self.select_validacion_origen).pack(side=tk.RIGHT, padx=(5,0))
        
        ttk.Button(origen_frame, text="👁️", width=3, 
                  command=self.preview_origen_folder).pack(side=tk.RIGHT, padx=(2,0))
        
        # Carpeta rechazados
        ttk.Label(config_grid, text="🗂️ Carpeta Rechazados:", style='Status.TLabel').grid(row=1, column=0, sticky='w', pady=5)
        rechazados_frame = ttk.Frame(config_grid)
        rechazados_frame.grid(row=1, column=1, sticky='ew', padx=5)
        
        self.validacion_rechazados_entry = ttk.Entry(rechazados_frame, textvariable=self.validacion_rechazados_var, font=('Consolas', 9))
        self.validacion_rechazados_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Button(rechazados_frame, text="📂", width=3, 
                  command=self.select_validacion_rechazados).pack(side=tk.RIGHT, padx=(5,0))
        
        ttk.Button(rechazados_frame, text="📁+", width=3, 
                  command=self.create_rechazados_folder).pack(side=tk.RIGHT, padx=(2,0))
        
        # Configurar grid weights
        config_grid.columnconfigure(1, weight=1)
        
        # Opciones avanzadas
        options_frame = ttk.LabelFrame(config_frame, text="🔧 Opciones Avanzadas", padding="10")
        options_frame.pack(fill=tk.X, pady=(15, 0))
        
        # Fila 1 de opciones
        options_row1 = ttk.Frame(options_frame)
        options_row1.pack(fill=tk.X, pady=2)
        
        ttk.Checkbutton(options_row1, text="🧪 Modo Simulación (Dry Run)", 
                       variable=self.validacion_dry_run_var).pack(side=tk.LEFT, padx=10)
        
        self.validacion_backup_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_row1, text="💾 Crear backup antes de mover", 
                       variable=self.validacion_backup_var).pack(side=tk.LEFT, padx=10)
        
        # Fila 2 de opciones
        options_row2 = ttk.Frame(options_frame)
        options_row2.pack(fill=tk.X, pady=2)
        
        self.validacion_log_detailed_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_row2, text="📝 Log detallado", 
                       variable=self.validacion_log_detailed_var).pack(side=tk.LEFT, padx=10)
        
        self.validacion_verify_integrity_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(options_row2, text="🔍 Verificar integridad de archivos", 
                       variable=self.validacion_verify_integrity_var).pack(side=tk.LEFT, padx=10)
        
        # Botones de configuración
        config_buttons = ttk.Frame(config_frame)
        config_buttons.pack(fill=tk.X, pady=(15, 0))
        
        ttk.Button(config_buttons, text="💾 Guardar Config", 
                  command=self.save_validacion_config).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(config_buttons, text="📂 Cargar Config", 
                  command=self.load_validacion_config).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(config_buttons, text="🔄 Restaurar Defecto", 
                  command=self.reset_validacion_config).pack(side=tk.LEFT, padx=5)
        
        # === SECCIÓN: EJECUCIÓN ===
        execution_frame = ttk.LabelFrame(scrollable_val_frame, text="🚀 Ejecución de Validación", padding="15")
        execution_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Botones principales
        main_buttons = ttk.Frame(execution_frame)
        main_buttons.pack(fill=tk.X, pady=(0, 10))
        
        self.validacion_execute_btn = ttk.Button(main_buttons, text="🚀 Ejecutar Validación", 
                                               style='Action.TButton', command=self.execute_validacion)
        self.validacion_execute_btn.pack(side=tk.LEFT, padx=5)
        
        self.validacion_stop_btn = ttk.Button(main_buttons, text="⏹️ Detener", 
                                            state='disabled', command=self.stop_validacion)
        self.validacion_stop_btn.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(main_buttons, text="🔍 Previsualizar", 
                  command=self.preview_validacion).pack(side=tk.LEFT, padx=5)
        
        # Progreso
        progress_frame = ttk.Frame(execution_frame)
        progress_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(progress_frame, text="Progreso:").pack(side=tk.LEFT)
        
        self.validacion_progress = ttk.Progressbar(progress_frame, mode='determinate')
        self.validacion_progress.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))
        
        self.validacion_progress_label = ttk.Label(progress_frame, text="0%")
        self.validacion_progress_label.pack(side=tk.RIGHT, padx=(10, 0))
        
        # === SECCIÓN: RESULTADOS ===
        results_frame = ttk.LabelFrame(scrollable_val_frame, text="📊 Resultados y Estadísticas", padding="15")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Notebook para diferentes vistas de resultados
        results_notebook = ttk.Notebook(results_frame)
        results_notebook.pack(fill=tk.BOTH, expand=True)
        
        # Tab: Resumen
        summary_tab = ttk.Frame(results_notebook)
        results_notebook.add(summary_tab, text="📋 Resumen")
        
        self.validacion_summary_text = tk.Text(summary_tab, height=6, wrap=tk.WORD, 
                                             font=('Consolas', 9), state='disabled')
        summary_scroll = ttk.Scrollbar(summary_tab, orient=tk.VERTICAL, 
                                     command=self.validacion_summary_text.yview)
        self.validacion_summary_text.configure(yscrollcommand=summary_scroll.set)
        
        self.validacion_summary_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        summary_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Tab: Log detallado
        log_tab = ttk.Frame(results_notebook)
        results_notebook.add(log_tab, text="📝 Log Detallado")
        
        self.validacion_log_text = tk.Text(log_tab, height=6, wrap=tk.WORD, 
                                         font=('Consolas', 8), state='disabled')
        log_scroll = ttk.Scrollbar(log_tab, orient=tk.VERTICAL, 
                                 command=self.validacion_log_text.yview)
        self.validacion_log_text.configure(yscrollcommand=log_scroll.set)
        
        self.validacion_log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Tab: Estadísticas
        stats_tab = ttk.Frame(results_notebook)
        results_notebook.add(stats_tab, text="📈 Estadísticas")
        
        self.validacion_stats_text = tk.Text(stats_tab, height=6, wrap=tk.WORD, 
                                           font=('Consolas', 9), state='disabled')
        stats_scroll = ttk.Scrollbar(stats_tab, orient=tk.VERTICAL, 
                                   command=self.validacion_stats_text.yview)
        self.validacion_stats_text.configure(yscrollcommand=stats_scroll.set)
        
        self.validacion_stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        stats_scroll.pack(side=tk.RIGHT, fill=tk.Y)
    
    def show_validacion_help(self):
        """Mostrar ayuda para validación"""
        help_window = tk.Toplevel(self.root)
        help_window.title("Ayuda - Validación de Duplicados")
        help_window.geometry("600x500")
        help_window.transient(self.root)
        
        help_text = tk.Text(help_window, wrap=tk.WORD, font=('Arial', 10))
        help_scroll = ttk.Scrollbar(help_window, orient=tk.VERTICAL, command=help_text.yview)
        help_text.configure(yscrollcommand=help_scroll.set)
        
        help_content = """
🔍 VALIDACIÓN DE DUPLICADOS - AYUDA

DESCRIPCIÓN:
La validación de duplicados busca archivos en la carpeta origen que ya existen en las ubicaciones QNAP y los mueve a una carpeta de rechazados.

CONFIGURACIÓN:
• Carpeta Origen: Donde están los archivos a verificar
• Carpeta Rechazados: Donde se moverán los duplicados encontrados

OPCIONES AVANZADAS:
• 🧪 Modo Simulación: Solo muestra qué haría sin realizar cambios
• 💾 Crear backup: Copia de seguridad antes de mover archivos
• 📝 Log detallado: Registro completo de todas las operaciones
• 🔍 Verificar integridad: Compara checksums de archivos

PROCESO:
1. Escanea archivos en carpeta origen
2. Busca duplicados en ubicaciones QNAP
3. Mueve duplicados a carpeta rechazados
4. Genera reporte detallado

RECOMENDACIONES:
• Use siempre modo simulación primero
• Verifique que las rutas sean correctas
• Revise los logs para confirmar resultados
        """
        
        help_text.insert(1.0, help_content.strip())
        help_text.config(state='disabled')
        
        help_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        help_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
        ttk.Button(help_window, text="Cerrar", command=help_window.destroy).pack(pady=10)

# FIN PIEZA 3A

    """
    dashboard_pieza3b.py
    PIEZA 3B: Pestaña de Limpieza - UI y estructura
    Gestor de Archivos Corporativo
    """

# CONTINUACIÓN DE LA CLASE GestorDashboard - PESTAÑA LIMPIEZA

    def create_limpieza_tab(self):
        """Crear pestaña de limpieza por lista avanzada"""
        limpieza_frame = ttk.Frame(self.notebook)
        self.notebook.add(limpieza_frame, text="🗑️ Limpieza")
        
        # Header de la pestaña
        header_frame = ttk.Frame(limpieza_frame)
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(header_frame, text="🗑️ Limpieza por Lista", 
                 style='Title.TLabel').pack(side=tk.LEFT)
        
        # Botón de ayuda
        ttk.Button(header_frame, text="❓ Ayuda", 
                  command=self.show_limpieza_help).pack(side=tk.RIGHT, padx=5)
        
        # Frame principal con scroll
        main_canvas_lim = tk.Canvas(limpieza_frame)
        scrollbar_lim = ttk.Scrollbar(limpieza_frame, orient="vertical", command=main_canvas_lim.yview)
        scrollable_lim_frame = ttk.Frame(main_canvas_lim)
        
        scrollable_lim_frame.bind(
            "<Configure>",
            lambda e: main_canvas_lim.configure(scrollregion=main_canvas_lim.bbox("all"))
        )
        
        main_canvas_lim.create_window((0, 0), window=scrollable_lim_frame, anchor="nw")
        main_canvas_lim.configure(yscrollcommand=scrollbar_lim.set)
        
        main_canvas_lim.pack(side="left", fill="both", expand=True)
        scrollbar_lim.pack(side="right", fill="y")
        
        # === SECCIÓN: CONFIGURACIÓN ===
        config_frame_lim = ttk.LabelFrame(scrollable_lim_frame, text="⚙️ Configuración de Limpieza", padding="15")
        config_frame_lim.pack(fill=tk.X, padx=10, pady=10)
        
        # Archivo de lista
        ttk.Label(config_frame_lim, text="📄 Archivo de Lista:", style='Status.TLabel').pack(anchor=tk.W, pady=(0, 5))
        
        archivo_frame = ttk.Frame(config_frame_lim)
        archivo_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.limpieza_archivo_entry = ttk.Entry(archivo_frame, textvariable=self.limpieza_archivo_var, font=('Consolas', 9))
        self.limpieza_archivo_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Button(archivo_frame, text="📂", width=3, 
                  command=self.select_limpieza_file).pack(side=tk.RIGHT, padx=(5,0))
        
        ttk.Button(archivo_frame, text="👁️", width=3, 
                  command=self.preview_list_file).pack(side=tk.RIGHT, padx=(2,0))
        
        ttk.Button(archivo_frame, text="✏️", width=3, 
                  command=self.edit_list_file).pack(side=tk.RIGHT, padx=(2,0))
        
        # Información del archivo
        self.file_info_frame = ttk.Frame(config_frame_lim)
        self.file_info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.file_info_label = ttk.Label(self.file_info_frame, text="📄 Seleccione un archivo de lista", 
                                       style='Small.TLabel')
        self.file_info_label.pack(anchor=tk.W)
        
        # Ubicaciones objetivo
        locations_frame = ttk.LabelFrame(config_frame_lim, text="🌐 Ubicaciones Objetivo", padding="10")
        locations_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Checkboxes para ubicaciones
        self.limpieza_locations = {}
        locations_grid = ttk.Frame(locations_frame)
        locations_grid.pack(fill=tk.X)
        
        locations_list = ['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4']
        for i, location in enumerate(locations_list):
            var = tk.BooleanVar(value=True)
            self.limpieza_locations[location] = var
            
            row = i // 3
            col = i % 3
            
            cb = ttk.Checkbutton(locations_grid, text=location.upper(), variable=var)
            cb.grid(row=row, column=col, sticky='w', padx=10, pady=2)
        
        # Configurar grid weights
        for col in range(3):
            locations_grid.columnconfigure(col, weight=1)
        
        # Botones de selección rápida
        quick_select_frame = ttk.Frame(locations_frame)
        quick_select_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(quick_select_frame, text="✅ Todas", width=8,
                  command=self.select_all_locations).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(quick_select_frame, text="❌ Ninguna", width=8,
                  command=self.deselect_all_locations).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(quick_select_frame, text="🏢 Solo QNAPs", width=10,
                  command=self.select_only_qnaps).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(quick_select_frame, text="💻 Solo PCs", width=10,
                  command=self.select_only_pcs).pack(side=tk.LEFT, padx=5)
        
        # Opciones avanzadas
        options_frame_lim = ttk.LabelFrame(config_frame_lim, text="🔧 Opciones Avanzadas", padding="10")
        options_frame_lim.pack(fill=tk.X, pady=(10, 0))
        
        # Fila 1 de opciones
        options_row1_lim = ttk.Frame(options_frame_lim)
        options_row1_lim.pack(fill=tk.X, pady=2)
        
        ttk.Checkbutton(options_row1_lim, text="🧪 Modo Simulación (Dry Run)", 
                       variable=self.limpieza_dry_run_var).pack(side=tk.LEFT, padx=10)
        
        self.limpieza_backup_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(options_row1_lim, text="💾 Crear backup antes de eliminar", 
                       variable=self.limpieza_backup_var).pack(side=tk.LEFT, padx=10)
        
        # Fila 2 de opciones
        options_row2_lim = ttk.Frame(options_frame_lim)
        options_row2_lim.pack(fill=tk.X, pady=2)
        
        self.limpieza_parallel_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_row2_lim, text="⚡ Procesamiento paralelo", 
                       variable=self.limpieza_parallel_var).pack(side=tk.LEFT, padx=10)
        
        self.limpieza_confirm_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_row2_lim, text="❓ Confirmar antes de eliminar", 
                       variable=self.limpieza_confirm_var).pack(side=tk.LEFT, padx=10)
        
        # Fila 3 de opciones
        options_row3_lim = ttk.Frame(options_frame_lim)
        options_row3_lim.pack(fill=tk.X, pady=2)
        
        self.limpieza_verify_before_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_row3_lim, text="🔍 Verificar existencia antes", 
                       variable=self.limpieza_verify_before_var).pack(side=tk.LEFT, padx=10)
        
        self.limpieza_skip_errors_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(options_row3_lim, text="⏭️ Continuar si hay errores", 
                       variable=self.limpieza_skip_errors_var).pack(side=tk.LEFT, padx=10)
        
        # === SECCIÓN: EJECUCIÓN ===
        execution_frame_lim = ttk.LabelFrame(scrollable_lim_frame, text="🚀 Ejecución de Limpieza", padding="15")
        execution_frame_lim.pack(fill=tk.X, padx=10, pady=10)
        
        # Botones principales
        main_buttons_lim = ttk.Frame(execution_frame_lim)
        main_buttons_lim.pack(fill=tk.X, pady=(0, 10))
        
        self.limpieza_execute_btn = ttk.Button(main_buttons_lim, text="🚀 Ejecutar Limpieza", 
                                             style='Action.TButton', command=self.execute_limpieza)
        self.limpieza_execute_btn.pack(side=tk.LEFT, padx=5)
        
        self.limpieza_stop_btn = ttk.Button(main_buttons_lim, text="⏹️ Detener", 
                                          state='disabled', command=self.stop_limpieza)
        self.limpieza_stop_btn.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(main_buttons_lim, text="🔍 Análisis Previo", 
                  command=self.analyze_limpieza).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(main_buttons_lim, text="💾 Guardar Config", 
                  command=self.save_limpieza_config).pack(side=tk.RIGHT, padx=5)
        
        # Progreso con detalles por ubicación
        progress_frame_lim = ttk.Frame(execution_frame_lim)
        progress_frame_lim.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(progress_frame_lim, text="Progreso General:").pack(anchor=tk.W)
        
        progress_row = ttk.Frame(progress_frame_lim)
        progress_row.pack(fill=tk.X, pady=(5, 0))
        
        self.limpieza_progress = ttk.Progressbar(progress_row, mode='determinate')
        self.limpieza_progress.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.limpieza_progress_label = ttk.Label(progress_row, text="0%")
        self.limpieza_progress_label.pack(side=tk.RIGHT, padx=(10, 0))
        
        # Progreso individual por ubicación
        self.location_progress_frame = ttk.LabelFrame(execution_frame_lim, text="📊 Progreso por Ubicación", padding="5")
        self.location_progress_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.location_progress_labels = {}
        for i, location in enumerate(locations_list):
            loc_frame = ttk.Frame(self.location_progress_frame)
            loc_frame.pack(fill=tk.X, pady=1)
            
            # Checkbox para habilitar/deshabilitar durante ejecución
            loc_enabled_var = tk.BooleanVar(value=True)
            ttk.Checkbutton(loc_frame, text=f"{location.upper()}:", 
                           variable=loc_enabled_var, width=8).pack(side=tk.LEFT)
            
            progress_bar = ttk.Progressbar(loc_frame, mode='determinate', length=120)
            progress_bar.pack(side=tk.LEFT, padx=(5, 10))
            
            status_label = ttk.Label(loc_frame, text="Esperando...", width=12)
            status_label.pack(side=tk.LEFT)
            
            files_label = ttk.Label(loc_frame, text="0/0", width=8, style='Small.TLabel')
            files_label.pack(side=tk.LEFT)
            
            self.location_progress_labels[location] = {
                'enabled': loc_enabled_var,
                'progress': progress_bar,
                'status': status_label,
                'files': files_label
            }
        
        # === SECCIÓN: RESULTADOS ===
        results_frame_lim = ttk.LabelFrame(scrollable_lim_frame, text="📊 Resultados y Estadísticas", padding="15")
        results_frame_lim.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Notebook para diferentes vistas de resultados
        results_notebook_lim = ttk.Notebook(results_frame_lim)
        results_notebook_lim.pack(fill=tk.BOTH, expand=True)
        
        # Tab: Resumen
        summary_tab_lim = ttk.Frame(results_notebook_lim)
        results_notebook_lim.add(summary_tab_lim, text="📋 Resumen")
        
        self.limpieza_summary_text = tk.Text(summary_tab_lim, height=6, wrap=tk.WORD, 
                                           font=('Consolas', 9), state='disabled')
        summary_scroll_lim = ttk.Scrollbar(summary_tab_lim, orient=tk.VERTICAL, 
                                         command=self.limpieza_summary_text.yview)
        self.limpieza_summary_text.configure(yscrollcommand=summary_scroll_lim.set)
        
        self.limpieza_summary_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        summary_scroll_lim.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Tab: Por ubicación
        location_tab = ttk.Frame(results_notebook_lim)
        results_notebook_lim.add(location_tab, text="🌐 Por Ubicación")
        
        self.limpieza_location_text = tk.Text(location_tab, height=6, wrap=tk.WORD, 
                                            font=('Consolas', 8), state='disabled')
        location_scroll = ttk.Scrollbar(location_tab, orient=tk.VERTICAL, 
                                      command=self.limpieza_location_text.yview)
        self.limpieza_location_text.configure(yscrollcommand=location_scroll.set)
        
        self.limpieza_location_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        location_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Tab: Log detallado
        log_tab_lim = ttk.Frame(results_notebook_lim)
        results_notebook_lim.add(log_tab_lim, text="📝 Log Detallado")
        
        self.limpieza_log_text = tk.Text(log_tab_lim, height=6, wrap=tk.WORD, 
                                       font=('Consolas', 8), state='disabled')
        log_scroll_lim = ttk.Scrollbar(log_tab_lim, orient=tk.VERTICAL, 
                                     command=self.limpieza_log_text.yview)
        self.limpieza_log_text.configure(yscrollcommand=log_scroll_lim.set)
        
        self.limpieza_log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scroll_lim.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Tab: Estadísticas avanzadas
        advanced_stats_tab = ttk.Frame(results_notebook_lim)
        results_notebook_lim.add(advanced_stats_tab, text="📈 Estadísticas")
        
        self.limpieza_advanced_stats_text = tk.Text(advanced_stats_tab, height=6, wrap=tk.WORD, 
                                                  font=('Consolas', 9), state='disabled')
        advanced_stats_scroll = ttk.Scrollbar(advanced_stats_tab, orient=tk.VERTICAL, 
                                            command=self.limpieza_advanced_stats_text.yview)
        self.limpieza_advanced_stats_text.configure(yscrollcommand=advanced_stats_scroll.set)
        
        self.limpieza_advanced_stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        advanced_stats_scroll.pack(side=tk.RIGHT, fill=tk.Y)
    
    def show_limpieza_help(self):
        """Mostrar ayuda para limpieza"""
        help_window = tk.Toplevel(self.root)
        help_window.title("Ayuda - Limpieza por Lista")
        help_window.geometry("600x500")
        help_window.transient(self.root)
        
        help_text = tk.Text(help_window, wrap=tk.WORD, font=('Arial', 10))
        help_scroll = ttk.Scrollbar(help_window, orient=tk.VERTICAL, command=help_text.yview)
        help_text.configure(yscrollcommand=help_scroll.set)
        
        help_content = """
🗑️ LIMPIEZA POR LISTA - AYUDA

DESCRIPCIÓN:
La limpieza por lista elimina archivos específicos de múltiples ubicaciones usando una lista en archivo de texto.

CONFIGURACIÓN:
• Archivo de Lista: Archivo .txt con nombres de archivos (uno por línea)
• Ubicaciones: Seleccione dónde buscar y eliminar archivos

FORMATO DEL ARCHIVO:
archivo1.pdf
imagen.jpg
documento.docx
video.mp4

UBICACIONES OBJETIVO:
• Seleccione las ubicaciones donde buscar archivos
• Use botones de selección rápida para mayor comodidad
• Puede cambiar selección durante la ejecución

OPCIONES AVANZADAS:
• 🧪 Modo Simulación: Solo muestra qué eliminaría sin hacerlo
• 💾 Crear backup: Copia de seguridad antes de eliminar
• ⚡ Procesamiento paralelo: Proceso simultáneo en ubicaciones
• ❓ Confirmar: Pedir confirmación antes de eliminar
• 🔍 Verificar existencia: Comprobar que el archivo existe antes
• ⏭️ Continuar si hay errores: No detener por errores individuales

PROCESO:
1. Lee y valida archivo de lista
2. Busca archivos en ubicaciones seleccionadas
3. Elimina archivos encontrados (con opciones de backup)
4. Genera estadísticas detalladas por ubicación

RECOMENDACIONES:
• Use modo simulación para verificar primero
• Revise la lista de archivos cuidadosamente
• Active backup para archivos importantes
• Verifique permisos en ubicaciones remotas
• Use análisis previo para validar configuración
        """
        
        help_text.insert(1.0, help_content.strip())
        help_text.config(state='disabled')
        
        help_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        help_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
        ttk.Button(help_window, text="Cerrar", command=help_window.destroy).pack(pady=10)
    
    # === MÉTODOS DE SELECCIÓN RÁPIDA ===
    
    def select_all_locations(self):
        """Seleccionar todas las ubicaciones"""
        for var in self.limpieza_locations.values():
            var.set(True)
        self.update_status("Todas las ubicaciones seleccionadas", "info")
    
    def deselect_all_locations(self):
        """Deseleccionar todas las ubicaciones"""
        for var in self.limpieza_locations.values():
            var.set(False)
        self.update_status("Todas las ubicaciones deseleccionadas", "warning")
    
    def select_only_qnaps(self):
        """Seleccionar solo ubicaciones QNAP"""
        for location, var in self.limpieza_locations.items():
            var.set(location.startswith('qnap'))
        self.update_status("Solo QNAPs seleccionados", "info")
    
    def select_only_pcs(self):
        """Seleccionar solo ubicaciones PC"""
        for location, var in self.limpieza_locations.items():
            var.set(location.startswith('pc'))
        self.update_status("Solo PCs seleccionados", "info")

# FIN PIEZA 3B

    """
    dashboard_pieza3c.py
    PIEZA 3C: Métodos de soporte para Validación
    Gestor de Archivos Corporativo
    """

# CONTINUACIÓN DE LA CLASE GestorDashboard - MÉTODOS DE SOPORTE VALIDACIÓN

    def select_validacion_origen(self):
        """Seleccionar carpeta origen para validación"""
        folder = filedialog.askdirectory(title="Seleccionar Carpeta Origen")
        if folder:
            self.validacion_origen_var.set(folder)
            self.update_status(f"Carpeta origen seleccionada: {Path(folder).name}", "success")
            self.validate_validacion_config()
    
    def select_validacion_rechazados(self):
        """Seleccionar carpeta rechazados para validación"""
        folder = filedialog.askdirectory(title="Seleccionar Carpeta Rechazados")
        if folder:
            self.validacion_rechazados_var.set(folder)
            self.update_status(f"Carpeta rechazados seleccionada: {Path(folder).name}", "success")
            self.validate_validacion_config()
    
    def create_rechazados_folder(self):
        """Crear carpeta rechazados automáticamente"""
        origen = self.validacion_origen_var.get().strip()
        if not origen:
            messagebox.showwarning("Advertencia", "Seleccione primero la carpeta origen")
            return
        
        # Crear carpeta rechazados en el mismo directorio padre
        parent_dir = Path(origen).parent
        rechazados_path = parent_dir / "rechazados"
        
        try:
            rechazados_path.mkdir(parents=True, exist_ok=True)
            self.validacion_rechazados_var.set(str(rechazados_path))
            self.update_status(f"Carpeta rechazados creada: {rechazados_path.name}", "success")
            self.validate_validacion_config()
        except Exception as e:
            messagebox.showerror("Error", f"Error creando carpeta rechazados: {e}")
    
    def preview_origen_folder(self):
        """Previsualizar contenido de carpeta origen"""
        origen = self.validacion_origen_var.get().strip()
        if not origen or not Path(origen).exists():
            messagebox.showwarning("Advertencia", "Carpeta origen no válida")
            return
        
        preview_window = tk.Toplevel(self.root)
        preview_window.title(f"Contenido - {Path(origen).name}")
        preview_window.geometry("700x500")
        preview_window.transient(self.root)
        preview_window.grab_set()
        
        # Frame principal
        main_frame = ttk.Frame(preview_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Información superior
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(info_frame, text=f"📁 Carpeta: {origen}", 
                 style='Header.TLabel').pack(anchor=tk.W)
        
        # Área de archivos con doble scroll
        files_frame = ttk.Frame(main_frame)
        files_frame.pack(fill=tk.BOTH, expand=True)
        
        files_text = tk.Text(files_frame, wrap=tk.NONE, font=('Consolas', 9))
        files_scroll_v = ttk.Scrollbar(files_frame, orient=tk.VERTICAL, command=files_text.yview)
        files_scroll_h = ttk.Scrollbar(files_frame, orient=tk.HORIZONTAL, command=files_text.xview)
        files_text.configure(yscrollcommand=files_scroll_v.set, xscrollcommand=files_scroll_h.set)
        
        # Cargar contenido en thread separado
        def load_content():
            try:
                files_text.insert(1.0, "🔄 Cargando contenido...\n")
                files_text.update()
                
                files = list(Path(origen).iterdir())
                file_list = [f for f in files if f.is_file()]
                dir_list = [f for f in files if f.is_dir()]
                
                # Calcular tamaño total
                total_size = 0
                for file_path in file_list:
                    try:
                        total_size += file_path.stat().st_size
                    except:
                        pass
                
                # Limpiar y escribir información
                files_text.delete(1.0, tk.END)
                
                files_text.insert(1.0, f"=== CONTENIDO DE {origen} ===\n\n")
                files_text.insert(tk.END, f"📊 ESTADÍSTICAS:\n")
                files_text.insert(tk.END, f"   Archivos: {len(file_list)}\n")
                files_text.insert(tk.END, f"   Carpetas: {len(dir_list)}\n")
                files_text.insert(tk.END, f"   Tamaño total: {total_size / (1024*1024):.2f} MB\n\n")
                
                if dir_list:
                    files_text.insert(tk.END, f"📁 CARPETAS ({len(dir_list)}):\n")
                    for dir_path in sorted(dir_list):
                        files_text.insert(tk.END, f"   📁 {dir_path.name}/\n")
                    files_text.insert(tk.END, "\n")
                
                if file_list:
                    files_text.insert(tk.END, f"📄 ARCHIVOS ({len(file_list)}):\n")
                    for file_path in sorted(file_list):
                        try:
                            size = file_path.stat().st_size / (1024*1024)  # MB
                            if size < 0.01:
                                size_str = f"{file_path.stat().st_size} B"
                            else:
                                size_str = f"{size:.2f} MB"
                            
                            files_text.insert(tk.END, f"   📄 {file_path.name:<50} {size_str:>10}\n")
                        except Exception as e:
                            files_text.insert(tk.END, f"   📄 {file_path.name:<50} {'Error':>10}\n")
                else:
                    files_text.insert(tk.END, "ℹ️ No hay archivos en esta carpeta\n")
                    
            except Exception as e:
                files_text.delete(1.0, tk.END)
                files_text.insert(1.0, f"❌ Error leyendo carpeta: {e}\n")
            
            files_text.config(state='disabled')
        
        # Ejecutar carga en thread
        threading.Thread(target=load_content, daemon=True).start()
        
        # Layout de scrollbars
        files_text.grid(row=0, column=0, sticky='nsew')
        files_scroll_v.grid(row=0, column=1, sticky='ns')
        files_scroll_h.grid(row=1, column=0, sticky='ew')
        
        files_frame.grid_rowconfigure(0, weight=1)
        files_frame.grid_columnconfigure(0, weight=1)
        
        # Botón cerrar
        ttk.Button(preview_window, text="Cerrar", 
                  command=preview_window.destroy).pack(pady=10)
    
    def preview_validacion(self):
        """Previsualizar qué haría la validación"""
        origen = self.validacion_origen_var.get().strip()
        if not origen or not Path(origen).exists():
            messagebox.showwarning("Configuración Incompleta", "Configure una carpeta origen válida")
            return
        
        preview_window = tk.Toplevel(self.root)
        preview_window.title("Previsión - Validación de Duplicados")
        preview_window.geometry("800x600")
        preview_window.transient(self.root)
        
        # Frame principal
        main_frame = ttk.Frame(preview_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Información de configuración
        config_frame = ttk.LabelFrame(main_frame, text="Configuración", padding="10")
        config_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(config_frame, text=f"📁 Origen: {origen}").pack(anchor=tk.W)
        ttk.Label(config_frame, text=f"🗂️ Rechazados: {self.validacion_rechazados_var.get()}").pack(anchor=tk.W)
        ttk.Label(config_frame, text=f"🧪 Modo: {'Simulación' if self.validacion_dry_run_var.get() else 'Real'}").pack(anchor=tk.W)
        
        # Área de previsión
        preview_frame = ttk.LabelFrame(main_frame, text="Previsión", padding="10")
        preview_frame.pack(fill=tk.BOTH, expand=True)
        
        preview_text = tk.Text(preview_frame, wrap=tk.WORD, font=('Consolas', 9))
        preview_scroll = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=preview_text.yview)
        preview_text.configure(yscrollcommand=preview_scroll.set)
        
        preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        preview_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Botones
        buttons_frame = ttk.Frame(preview_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(buttons_frame, text="🔄 Actualizar", 
                  command=lambda: self.refresh_validacion_preview(preview_text)).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(buttons_frame, text="Cerrar", 
                  command=preview_window.destroy).pack(side=tk.RIGHT, padx=5)
        
        # Cargar previsión inicial
        self.refresh_validacion_preview(preview_text)
    
    def refresh_validacion_preview(self, preview_text):
        """Actualizar previsión de validación"""
        preview_text.delete(1.0, tk.END)
        preview_text.insert(1.0, "🔍 PREVISIÓN DE VALIDACIÓN\n\n")
        preview_text.insert(tk.END, "🔄 Analizando archivos...\n")
        preview_text.update()
        
        def preview_worker():
            try:
                origen = self.validacion_origen_var.get().strip()
                files = list(Path(origen).glob('*'))
                file_list = [f for f in files if f.is_file()]
                
                preview_text.insert(tk.END, f"\n📊 ESTADÍSTICAS:\n")
                preview_text.insert(tk.END, f"   Total archivos encontrados: {len(file_list)}\n")
                
                if len(file_list) == 0:
                    preview_text.insert(tk.END, "\n⚠️ No hay archivos para procesar\n")
                    return
                
                # Mostrar primeros archivos
                preview_text.insert(tk.END, f"\n📄 ARCHIVOS A PROCESAR (primeros 20):\n")
                for i, file_path in enumerate(file_list[:20], 1):
                    size = file_path.stat().st_size / (1024*1024)
                    preview_text.insert(tk.END, f"{i:3d}. {file_path.name:<40} ({size:.2f} MB)\n")
                
                if len(file_list) > 20:
                    preview_text.insert(tk.END, f"\n... y {len(file_list) - 20} archivos más\n")
                
                # Información sobre el proceso
                preview_text.insert(tk.END, f"\n🔍 PROCESO QUE SE EJECUTARÁ:\n")
                preview_text.insert(tk.END, f"1. Conectar a ubicaciones QNAP\n")
                preview_text.insert(tk.END, f"2. Buscar duplicados de cada archivo\n")
                preview_text.insert(tk.END, f"3. Mover duplicados a carpeta rechazados\n")
                preview_text.insert(tk.END, f"4. Generar reporte detallado\n")
                
                if self.validacion_dry_run_var.get():
                    preview_text.insert(tk.END, f"\n🧪 MODO SIMULACIÓN ACTIVO\n")
                    preview_text.insert(tk.END, f"   No se realizarán cambios reales\n")
                
                preview_text.insert(tk.END, f"\nℹ️ Use 'Ejecutar Validación' para comenzar el proceso real\n")
                
            except Exception as e:
                preview_text.insert(tk.END, f"\n❌ Error en previsión: {e}\n")
        
        threading.Thread(target=preview_worker, daemon=True).start()
    
    def validate_validacion_config(self):
        """Validar configuración de validación"""
        origen = self.validacion_origen_var.get().strip()
        rechazados = self.validacion_rechazados_var.get().strip()
        
        issues = []
        
        if not origen:
            issues.append("📁 Carpeta origen no configurada")
        elif not Path(origen).exists():
            issues.append("📁 Carpeta origen no existe")
        elif not Path(origen).is_dir():
            issues.append("📁 Carpeta origen no es un directorio")
        
        if not rechazados:
            issues.append("🗂️ Carpeta rechazados no configurada")
        elif origen and rechazados and Path(origen).resolve() == Path(rechazados).resolve():
            issues.append("🗂️ Carpeta rechazados no puede ser igual a origen")
        
        # Actualizar UI según validación
        if issues:
            self.update_status(f"Configuración incompleta: {issues[0]}", "warning")
            if hasattr(self, 'validacion_execute_btn'):
                self.validacion_execute_btn.config(state='disabled')
        else:
            self.update_status("Configuración de validación válida", "success")
            if hasattr(self, 'validacion_execute_btn'):
                self.validacion_execute_btn.config(state='normal')
        
        return len(issues) == 0
    
    def save_validacion_config(self):
        """Guardar configuración de validación"""
        try:
            from core.config_manager import config_manager
            
            config = {
                'carpeta_origen': self.validacion_origen_var.get(),
                'carpeta_rechazados': self.validacion_rechazados_var.get(),
                'dry_run': self.validacion_dry_run_var.get(),
                'backup': getattr(self, 'validacion_backup_var', tk.BooleanVar()).get(),
                'log_detailed': getattr(self, 'validacion_log_detailed_var', tk.BooleanVar()).get(),
                'verify_integrity': getattr(self, 'validacion_verify_integrity_var', tk.BooleanVar()).get()
            }
            
            config_manager.save_user_config('validacion', config)
            messagebox.showinfo("Configuración", "Configuración de validación guardada correctamente")
            self.update_status("Configuración guardada", "success")
            
        except Exception as e:
            messagebox.showerror("Error", f"Error guardando configuración: {e}")
            self.update_status(f"Error guardando configuración: {e}", "error")
    
    def load_validacion_config(self):
        """Cargar configuración de validación"""
        try:
            from core.config_manager import config_manager
            
            config = config_manager.get_user_config('validacion', {})
            
            if config:
                self.validacion_origen_var.set(config.get('carpeta_origen', ''))
                self.validacion_rechazados_var.set(config.get('carpeta_rechazados', ''))
                self.validacion_dry_run_var.set(config.get('dry_run', True))
                
                if hasattr(self, 'validacion_backup_var'):
                    self.validacion_backup_var.set(config.get('backup', True))
                if hasattr(self, 'validacion_log_detailed_var'):
                    self.validacion_log_detailed_var.set(config.get('log_detailed', True))
                if hasattr(self, 'validacion_verify_integrity_var'):
                    self.validacion_verify_integrity_var.set(config.get('verify_integrity', False))
                
                messagebox.showinfo("Configuración", "Configuración cargada correctamente")
                self.update_status("Configuración cargada", "success")
                self.validate_validacion_config()
            else:
                messagebox.showinfo("Configuración", "No hay configuración guardada")
                
        except Exception as e:
            messagebox.showerror("Error", f"Error cargando configuración: {e}")
            self.update_status(f"Error cargando configuración: {e}", "error")
    
    def reset_validacion_config(self):
        """Restaurar configuración de validación a valores por defecto"""
        if messagebox.askyesno("Confirmar", "¿Restaurar configuración a valores por defecto?"):
            self.validacion_origen_var.set('')
            self.validacion_rechazados_var.set('')
            self.validacion_dry_run_var.set(True)
            
            if hasattr(self, 'validacion_backup_var'):
                self.validacion_backup_var.set(True)
            if hasattr(self, 'validacion_log_detailed_var'):
                self.validacion_log_detailed_var.set(True)
            if hasattr(self, 'validacion_verify_integrity_var'):
                self.validacion_verify_integrity_var.set(False)
            
            self.update_status("Configuración restaurada a valores por defecto", "info")
            self.validate_validacion_config()
    
    def update_validacion_progress(self, progress: int, message: str = ""):
        """Actualizar progreso de validación"""
        try:
            if hasattr(self, 'validacion_progress'):
                self.validacion_progress['value'] = progress
                self.validacion_progress_label.config(text=f"{progress}%")
                
                if message:
                    self.update_status(message, "info")
                
                self.root.update_idletasks()
        except Exception as e:
            self.logger.error(f"Error actualizando progreso validación: {e}")
    
    def show_validacion_results(self, text: str, tab: str = "summary"):
        """Mostrar resultados de validación en la pestaña especificada"""
        try:
            if tab == "summary" and hasattr(self, 'validacion_summary_text'):
                widget = self.validacion_summary_text
            elif tab == "log" and hasattr(self, 'validacion_log_text'):
                widget = self.validacion_log_text
            elif tab == "stats" and hasattr(self, 'validacion_stats_text'):
                widget = self.validacion_stats_text
            else:
                return
            
            widget.config(state='normal')
            widget.delete(1.0, tk.END)
            widget.insert(1.0, text)
            widget.see(tk.END)
            widget.config(state='disabled')
            
        except Exception as e:
            self.logger.error(f"Error mostrando resultados validación: {e}")
    
    def append_validacion_log(self, message: str):
        """Agregar mensaje al log de validación"""
        try:
            if hasattr(self, 'validacion_log_text'):
                self.validacion_log_text.config(state='normal')
                timestamp = datetime.now().strftime("%H:%M:%S")
                self.validacion_log_text.insert(tk.END, f"[{timestamp}] {message}\n")
                self.validacion_log_text.see(tk.END)
                self.validacion_log_text.config(state='disabled')
        except Exception as e:
            self.logger.error(f"Error agregando log validación: {e}")

# FIN PIEZA 3C

    """
    dashboard_pieza3d.py
    PIEZA 3D: Métodos de soporte para Limpieza
    Gestor de Archivos Corporativo
    """

# CONTINUACIÓN DE LA CLASE GestorDashboard - MÉTODOS DE SOPORTE LIMPIEZA

    def select_limpieza_file(self):
        """Seleccionar archivo de lista para limpieza"""
        file_path = filedialog.askopenfilename(
            title="Seleccionar Archivo de Lista",
            filetypes=[("Archivos de texto", "*.txt"), ("Todos los archivos", "*.*")]
        )
        if file_path:
            self.limpieza_archivo_var.set(file_path)
            self.update_file_info(file_path)
            self.update_status(f"Archivo de lista seleccionado: {Path(file_path).name}", "success")
            self.validate_limpieza_config()
    
    def update_file_info(self, file_path: str):
        """Actualizar información del archivo de lista"""
        try:
            path = Path(file_path)
            if path.exists():
                size_kb = path.stat().st_size / 1024
                
                # Contar líneas en thread separado para archivos grandes
                def count_lines():
                    try:
                        with open(path, 'r', encoding='utf-8', errors='ignore') as f:
                            lines = len(f.readlines())
                        
                        info_text = f"📄 {path.name} | {size_kb:.1f} KB | {lines} líneas"
                        self.root.after(0, lambda: self.file_info_label.config(text=info_text, foreground='#2E7D32'))
                    except Exception as e:
                        error_text = f"❌ Error: {str(e)[:50]}..."
                        self.root.after(0, lambda: self.file_info_label.config(text=error_text, foreground='#C62828'))
                
                if size_kb > 100:  # Archivo grande, usar thread
                    self.file_info_label.config(text="🔄 Analizando archivo...", foreground='#1976D2')
                    threading.Thread(target=count_lines, daemon=True).start()
                else:
                    count_lines()  # Archivo pequeño, ejecutar directamente
            else:
                self.file_info_label.config(text="❌ Archivo no encontrado", foreground='#C62828')
        except Exception as e:
            self.file_info_label.config(text=f"❌ Error: {str(e)[:50]}...", foreground='#C62828')
    
    def edit_list_file(self):
        """Editar archivo de lista en ventana integrada"""
        file_path = self.limpieza_archivo_var.get().strip()
        
        edit_window = tk.Toplevel(self.root)
        edit_window.title(f"Editor - {Path(file_path).name if file_path else 'Nuevo archivo'}")
        edit_window.geometry("700x600")
        edit_window.transient(self.root)
        edit_window.grab_set()
        
        # Header con información
        header_frame = ttk.Frame(edit_window)
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(header_frame, text="✏️ Editor de Lista de Archivos", 
                 style='Header.TLabel').pack(side=tk.LEFT)
        
        # Frame de herramientas
        tools_frame = ttk.LabelFrame(edit_window, text="🛠️ Herramientas", padding="5")
        tools_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # Botones de herramientas
        ttk.Button(tools_frame, text="📋 Pegar desde Clipboard", 
                  command=lambda: self.paste_from_clipboard(edit_text)).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(tools_frame, text="🧹 Limpiar Líneas Vacías", 
                  command=lambda: self.clean_empty_lines(edit_text)).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(tools_frame, text="🔤 Remover Duplicados", 
                  command=lambda: self.remove_duplicates(edit_text)).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(tools_frame, text="📊 Validar", 
                  command=lambda: self.validate_list_content(edit_text)).pack(side=tk.RIGHT, padx=5)
        
        # Área de edición con números de línea
        edit_frame = ttk.Frame(edit_window)
        edit_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        # Frame para texto y line numbers
        text_frame = ttk.Frame(edit_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        # Line numbers
        line_numbers = tk.Text(text_frame, width=4, padx=3, takefocus=0,
                              border=0, state='disabled', wrap='none', font=('Consolas', 10))
        line_numbers.pack(side=tk.LEFT, fill=tk.Y)
        
        # Main text editor
        edit_text = tk.Text(text_frame, wrap=tk.NONE, font=('Consolas', 10), undo=True)
        edit_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Scrollbars
        edit_scroll_v = ttk.Scrollbar(edit_frame, orient=tk.VERTICAL)
        edit_scroll_v.pack(side=tk.RIGHT, fill=tk.Y)
        
        edit_scroll_h = ttk.Scrollbar(edit_frame, orient=tk.HORIZONTAL)
        edit_scroll_h.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Configurar scrollbars
        edit_text.configure(yscrollcommand=edit_scroll_v.set, xscrollcommand=edit_scroll_h.set)
        edit_scroll_v.configure(command=self.sync_scroll_y(edit_text, line_numbers))
        edit_scroll_h.configure(command=edit_text.xview)
        
        # Sincronizar line numbers
        def update_line_numbers(*args):
            line_numbers.config(state='normal')
            line_numbers.delete(1.0, tk.END)
            
            content = edit_text.get(1.0, tk.END)
            lines = content.split('\n')
            
            for i in range(len(lines)):
                line_numbers.insert(tk.END, f"{i+1:3d}\n")
            
            line_numbers.config(state='disabled')
        
        edit_text.bind('<KeyRelease>', update_line_numbers)
        edit_text.bind('<Button-1>', update_line_numbers)
        
        # Cargar contenido si existe
        try:
            if file_path and Path(file_path).exists():
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    edit_text.insert(1.0, content)
            else:
                edit_text.insert(1.0, "# Lista de archivos a eliminar\n# Un archivo por línea\n\n")
            
            update_line_numbers()
        except Exception as e:
            edit_text.insert(1.0, f"# Error cargando archivo: {e}\n\n")
        
        # Status bar
        status_frame = ttk.Frame(edit_window)
        status_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        status_label = ttk.Label(status_frame, text="Listo para editar", style='Small.TLabel')
        status_label.pack(side=tk.LEFT)
        
        # Botones principales
        buttons_frame = ttk.Frame(edit_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        def save_file():
            try:
                content = edit_text.get(1.0, tk.END)
                save_path = file_path
                
                if not save_path:
                    save_path = filedialog.asksaveasfilename(
                        title="Guardar archivo de lista",
                        defaultextension=".txt",
                        filetypes=[("Archivos de texto", "*.txt")]
                    )
                
                if save_path:
                    with open(save_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    self.limpieza_archivo_var.set(save_path)
                    self.update_file_info(save_path)
                    messagebox.showinfo("Éxito", "Archivo guardado correctamente")
                    edit_window.destroy()
            except Exception as e:
                messagebox.showerror("Error", f"Error guardando archivo: {e}")
        
        def save_as_file():
            try:
                content = edit_text.get(1.0, tk.END)
                save_path = filedialog.asksaveasfilename(
                    title="Guardar como archivo de lista",
                    defaultextension=".txt",
                    filetypes=[("Archivos de texto", "*.txt")]
                )
                
                if save_path:
                    with open(save_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    self.limpieza_archivo_var.set(save_path)
                    self.update_file_info(save_path)
                    messagebox.showinfo("Éxito", "Archivo guardado correctamente")
                    edit_window.destroy()
            except Exception as e:
                messagebox.showerror("Error", f"Error guardando archivo: {e}")
        
        ttk.Button(buttons_frame, text="💾 Guardar", command=save_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="💾 Guardar Como", command=save_as_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="❌ Cancelar", command=edit_window.destroy).pack(side=tk.RIGHT, padx=5)
    
    def sync_scroll_y(self, text_widget, line_widget):
        """Sincronizar scroll vertical entre widgets"""
        def scroll_command(*args):
            text_widget.yview(*args)
            line_widget.yview(*args)
        return scroll_command
    
    def paste_from_clipboard(self, text_widget):
        """Pegar contenido del clipboard"""
        try:
            clipboard_content = self.root.clipboard_get()
            text_widget.insert(tk.INSERT, clipboard_content)
        except Exception:
            messagebox.showwarning("Clipboard", "No hay contenido válido en el clipboard")
    
    def clean_empty_lines(self, text_widget):
        """Limpiar líneas vacías del editor"""
        content = text_widget.get(1.0, tk.END)
        lines = content.split('\n')
        clean_lines = [line.strip() for line in lines if line.strip()]
        
        text_widget.delete(1.0, tk.END)
        text_widget.insert(1.0, '\n'.join(clean_lines))
    
    def remove_duplicates(self, text_widget):
        """Remover líneas duplicadas"""
        content = text_widget.get(1.0, tk.END)
        lines = content.split('\n')
        unique_lines = []
        seen = set()
        
        for line in lines:
            clean_line = line.strip()
            if clean_line and clean_line not in seen:
                unique_lines.append(clean_line)
                seen.add(clean_line)
        
        text_widget.delete(1.0, tk.END)
        text_widget.insert(1.0, '\n'.join(unique_lines))
    
    def validate_list_content(self, text_widget):
        """Validar contenido de la lista"""
        content = text_widget.get(1.0, tk.END)
        lines = content.split('\n')
        
        valid_count = 0
        invalid_count = 0
        empty_count = 0
        
        for line in lines:
            clean_line = line.strip()
            if not clean_line or clean_line.startswith('#'):
                empty_count += 1
            elif self.is_valid_filename(clean_line):
                valid_count += 1
            else:
                invalid_count += 1
        
        message = f"Validación de lista:\n\n"
        message += f"✅ Archivos válidos: {valid_count}\n"
        message += f"❌ Nombres inválidos: {invalid_count}\n"
        message += f"⚪ Líneas vacías/comentarios: {empty_count}\n"
        
        if invalid_count > 0:
            message += f"\n⚠️ Hay {invalid_count} nombres de archivo inválidos"
        
        messagebox.showinfo("Validación", message)
    
    def is_valid_filename(self, filename: str) -> bool:
        """Verificar si es un nombre de archivo válido"""
        import re
        
        # Caracteres no permitidos en Windows
        invalid_chars = r'[<>:"/\\|?*]'
        if re.search(invalid_chars, filename):
            return False
        
        # Nombres reservados en Windows
        reserved_names = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 
                         'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 
                         'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9']
        
        name_without_ext = filename.split('.')[0].upper()
        if name_without_ext in reserved_names:
            return False
        
        return True
    
    def analyze_limpieza(self):
        """Analizar archivo de lista antes de ejecutar limpieza"""
        archivo = self.limpieza_archivo_var.get().strip()
        if not archivo or not Path(archivo).exists():
            messagebox.showwarning("Configuración Incompleta", "Seleccione un archivo de lista válido")
            return
        
        analyze_window = tk.Toplevel(self.root)
        analyze_window.title("Análisis - Limpieza por Lista")
        analyze_window.geometry("900x700")
        analyze_window.transient(self.root)
        
        # Header
        header_frame = ttk.Frame(analyze_window)
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(header_frame, text="🔍 Análisis de Limpieza", 
                 style='Title.TLabel').pack(side=tk.LEFT)
        
        # Notebook para diferentes análisis
        analyze_notebook = ttk.Notebook(analyze_window)
        analyze_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Tab 1: Resumen general
        summary_frame = ttk.Frame(analyze_notebook)
        analyze_notebook.add(summary_frame, text="📊 Resumen")
        
        summary_text = tk.Text(summary_frame, wrap=tk.WORD, font=('Consolas', 9))
        summary_scroll = ttk.Scrollbar(summary_frame, orient=tk.VERTICAL, command=summary_text.yview)
        summary_text.configure(yscrollcommand=summary_scroll.set)
        
        summary_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        summary_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Tab 2: Archivos válidos
        valid_frame = ttk.Frame(analyze_notebook)
        analyze_notebook.add(valid_frame, text="✅ Válidos")
        
        valid_text = tk.Text(valid_frame, wrap=tk.WORD, font=('Consolas', 9))
        valid_scroll = ttk.Scrollbar(valid_frame, orient=tk.VERTICAL, command=valid_text.yview)
        valid_text.configure(yscrollcommand=valid_scroll.set)
        
        valid_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        valid_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Tab 3: Archivos inválidos
        invalid_frame = ttk.Frame(analyze_notebook)
        analyze_notebook.add(invalid_frame, text="❌ Inválidos")
        
        invalid_text = tk.Text(invalid_frame, wrap=tk.WORD, font=('Consolas', 9))
        invalid_scroll = ttk.Scrollbar(invalid_frame, orient=tk.VERTICAL, command=invalid_text.yview)
        invalid_text.configure(yscrollcommand=invalid_scroll.set)
        
        invalid_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        invalid_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Botones
        buttons_frame = ttk.Frame(analyze_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(buttons_frame, text="🔄 Actualizar", 
                  command=lambda: self.refresh_limpieza_analysis(archivo, summary_text, valid_text, invalid_text)).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(buttons_frame, text="Cerrar", 
                  command=analyze_window.destroy).pack(side=tk.RIGHT, padx=5)
        
        # Ejecutar análisis inicial
        self.refresh_limpieza_analysis(archivo, summary_text, valid_text, invalid_text)
    
    def refresh_limpieza_analysis(self, archivo, summary_text, valid_text, invalid_text):
        """Actualizar análisis de limpieza"""
        # Limpiar widgets
        for widget in [summary_text, valid_text, invalid_text]:
            widget.delete(1.0, tk.END)
            widget.insert(1.0, "🔄 Analizando...\n")
            widget.update()
        
        def analyze_worker():
            try:
                from core.utils import read_text_file, validate_file_list
                
                # Leer archivo
                lines = read_text_file(Path(archivo))
                if lines is None:
                    for widget in [summary_text, valid_text, invalid_text]:
                        self.root.after(0, lambda w=widget: w.delete(1.0, tk.END))
                        self.root.after(0, lambda w=widget: w.insert(1.0, "❌ Error leyendo archivo\n"))
                    return
                
                # Validar
                validation = validate_file_list(lines)
                
                # Ubicaciones seleccionadas
                selected_locations = [loc for loc, var in self.limpieza_locations.items() if var.get()]
                
                # Generar resumen
                summary = []
                summary.append(f"🔍 ANÁLISIS DE LIMPIEZA\n")
                summary.append(f"=" * 50 + "\n\n")
                summary.append(f"📄 Archivo: {archivo}\n")
                summary.append(f"📊 Total líneas: {len(lines)}\n")
                summary.append(f"✅ Archivos válidos: {len(validation['valid'])}\n")
                summary.append(f"❌ Nombres inválidos: {len(validation['invalid_names'])}\n")
                summary.append(f"⚪ Líneas vacías: {len(validation['empty_lines'])}\n\n")
                
                summary.append(f"🌐 UBICACIONES SELECCIONADAS ({len(selected_locations)}):\n")
                for loc in selected_locations:
                    summary.append(f"  • {loc.upper()}\n")
                
                summary.append(f"\n🔧 OPCIONES CONFIGURADAS:\n")
                summary.append(f"  • Modo simulación: {'Sí' if self.limpieza_dry_run_var.get() else 'No'}\n")
                summary.append(f"  • Crear backup: {'Sí' if getattr(self, 'limpieza_backup_var', tk.BooleanVar()).get() else 'No'}\n")
                summary.append(f"  • Procesamiento paralelo: {'Sí' if getattr(self, 'limpieza_parallel_var', tk.BooleanVar()).get() else 'No'}\n")
                summary.append(f"  • Confirmar eliminación: {'Sí' if getattr(self, 'limpieza_confirm_var', tk.BooleanVar()).get() else 'No'}\n")
                
                if len(validation['valid']) > 0:
                    summary.append(f"\n✅ LISTO PARA EJECUTAR\n")
                else:
                    summary.append(f"\n⚠️ NO HAY ARCHIVOS VÁLIDOS PARA PROCESAR\n")
                
                # Actualizar widgets
                self.root.after(0, lambda: self.update_analysis_widget(summary_text, ''.join(summary)))
                
                # Archivos válidos
                valid_content = []
                valid_content.append(f"✅ ARCHIVOS VÁLIDOS ({len(validation['valid'])})\n")
                valid_content.append("=" * 40 + "\n\n")
                
                for i, filename in enumerate(validation['valid'], 1):
                    valid_content.append(f"{i:4d}. {filename}\n")
                
                self.root.after(0, lambda: self.update_analysis_widget(valid_text, ''.join(valid_content)))
                
                # Archivos inválidos
                invalid_content = []
                if validation['invalid_names']:
                    invalid_content.append(f"❌ NOMBRES INVÁLIDOS ({len(validation['invalid_names'])})\n")
                    invalid_content.append("=" * 40 + "\n\n")
                    
                    for i, filename in enumerate(validation['invalid_names'], 1):
                        invalid_content.append(f"{i:4d}. {filename}\n")
                else:
                    invalid_content.append("✅ NO HAY NOMBRES INVÁLIDOS\n")
                
                self.root.after(0, lambda: self.update_analysis_widget(invalid_text, ''.join(invalid_content)))
                
            except Exception as e:
                error_msg = f"❌ Error en análisis: {e}\n"
                for widget in [summary_text, valid_text, invalid_text]:
                    self.root.after(0, lambda w=widget: self.update_analysis_widget(w, error_msg))
        
        threading.Thread(target=analyze_worker, daemon=True).start()
    
    def update_analysis_widget(self, widget, content):
        """Actualizar contenido de widget de análisis"""
        widget.delete(1.0, tk.END)
        widget.insert(1.0, content)
    
    def validate_limpieza_config(self):
        """Validar configuración de limpieza"""
        archivo = self.limpieza_archivo_var.get().strip()
        selected_locations = [loc for loc, var in self.limpieza_locations.items() if var.get()]
        
        issues = []
        
        if not archivo:
            issues.append("📄 Archivo de lista no seleccionado")
        elif not Path(archivo).exists():
            issues.append("📄 Archivo de lista no existe")
        
        if not selected_locations:
            issues.append("🌐 No hay ubicaciones seleccionadas")
        
        # Actualizar UI según validación
        if issues:
            self.update_status(f"Configuración incompleta: {issues[0]}", "warning")
            if hasattr(self, 'limpieza_execute_btn'):
                self.limpieza_execute_btn.config(state='disabled')
        else:
            self.update_status("Configuración de limpieza válida", "success")
            if hasattr(self, 'limpieza_execute_btn'):
                self.limpieza_execute_btn.config(state='normal')
        
        return len(issues) == 0
    
    def save_limpieza_config(self):
        """Guardar configuración de limpieza"""
        try:
            from core.config_manager import config_manager
            
            # Obtener ubicaciones seleccionadas
            selected_locations = {}
            for loc, var in self.limpieza_locations.items():
                selected_locations[loc] = var.get()
            
            config = {
                'archivo_lista': self.limpieza_archivo_var.get(),
                'dry_run': self.limpieza_dry_run_var.get(),
                'ubicaciones_seleccionadas': selected_locations,
                'backup': getattr(self, 'limpieza_backup_var', tk.BooleanVar()).get(),
                'paralelo': getattr(self, 'limpieza_parallel_var', tk.BooleanVar()).get(),
                'confirmar': getattr(self, 'limpieza_confirm_var', tk.BooleanVar()).get(),
                'verificar_antes': getattr(self, 'limpieza_verify_before_var', tk.BooleanVar()).get(),
                'continuar_errores': getattr(self, 'limpieza_skip_errors_var', tk.BooleanVar()).get()
            }
            
            config_manager.save_user_config('limpieza', config)
            messagebox.showinfo("Configuración", "Configuración de limpieza guardada correctamente")
            self.update_status("Configuración guardada", "success")
            
        except Exception as e:
            messagebox.showerror("Error", f"Error guardando configuración: {e}")
            self.update_status(f"Error guardando configuración: {e}", "error")
    
    def load_saved_config(self):
        """Cargar configuración guardada al iniciar"""
        try:
            from core.config_manager import config_manager
            
            # Cargar configuración de validación
            validacion_config = config_manager.get_user_config('validacion', {})
            if validacion_config:
                self.validacion_origen_var.set(validacion_config.get('carpeta_origen', ''))
                self.validacion_rechazados_var.set(validacion_config.get('carpeta_rechazados', ''))
                self.validacion_dry_run_var.set(validacion_config.get('dry_run', True))
            
            # Cargar configuración de limpieza
            limpieza_config = config_manager.get_user_config('limpieza', {})
            if limpieza_config:
                self.limpieza_archivo_var.set(limpieza_config.get('archivo_lista', ''))
                self.limpieza_dry_run_var.set(limpieza_config.get('dry_run', True))
                
                # Cargar ubicaciones seleccionadas
                ubicaciones_config = limpieza_config.get('ubicaciones_seleccionadas', {})
                for loc, var in getattr(self, 'limpieza_locations', {}).items():
                    var.set(ubicaciones_config.get(loc, True))
                
                # Actualizar info del archivo si existe
                archivo = limpieza_config.get('archivo_lista', '')
                if archivo:
                    self.update_file_info(archivo)
            
        except Exception as e:
            self.logger.warning(f"Error cargando configuración guardada: {e}")

# FIN PIEZA 3D

    """
    dashboard_pieza4.py
    PIEZA 4: Sistema de Logs Avanzado + Pestaña Logs completa
    Gestor de Archivos Corporativo
    """

# CONTINUACIÓN DE LA CLASE GestorDashboard - SISTEMA DE LOGS AVANZADO

    def create_logs_tab(self):
        """Crear pestaña de logs avanzada"""
        logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(logs_frame, text="📝 Logs")
        
        # Header con controles principales
        header_frame = ttk.Frame(logs_frame)
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(header_frame, text="📝 Sistema de Logs", 
                 style='Title.TLabel').pack(side=tk.LEFT)
        
        # Controles de tiempo real
        realtime_frame = ttk.Frame(header_frame)
        realtime_frame.pack(side=tk.RIGHT)
        
        self.logs_realtime_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(realtime_frame, text="🔴 En Vivo", 
                       variable=self.logs_realtime_var,
                       command=self.toggle_realtime_logs).pack(side=tk.RIGHT, padx=5)
        
        # Frame principal con múltiples vistas
        main_logs_frame = ttk.Frame(logs_frame)
        main_logs_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        # Panel de control lateral
        control_panel = ttk.LabelFrame(main_logs_frame, text="🎛️ Control", padding="10")
        control_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # Categorías de logs
        ttk.Label(control_panel, text="📂 Categoría:", style='Status.TLabel').pack(anchor=tk.W, pady=(0, 5))
        
        self.log_categories = ['sistema', 'limpieza', 'validacion', 'conectividad', 'todos']
        category_frame = ttk.Frame(control_panel)
        category_frame.pack(fill=tk.X, pady=(0, 10))
        
        for category in self.log_categories:
            rb = ttk.Radiobutton(category_frame, text=category.capitalize(), 
                                variable=self.log_category_var, value=category,
                                command=self.refresh_logs)
            rb.pack(anchor=tk.W, pady=1)
        
        # Niveles de log
        ttk.Separator(control_panel, orient='horizontal').pack(fill=tk.X, pady=10)
        ttk.Label(control_panel, text="📊 Nivel:", style='Status.TLabel').pack(anchor=tk.W, pady=(0, 5))
        
        self.log_level_var = tk.StringVar(value='todos')
        log_levels = ['todos', 'debug', 'info', 'warning', 'error']
        
        for level in log_levels:
            rb = ttk.Radiobutton(control_panel, text=level.upper(), 
                                variable=self.log_level_var, value=level,
                                command=self.refresh_logs)
            rb.pack(anchor=tk.W, pady=1)
        
        # Filtros avanzados
        ttk.Separator(control_panel, orient='horizontal').pack(fill=tk.X, pady=10)
        ttk.Label(control_panel, text="🔍 Filtros:", style='Status.TLabel').pack(anchor=tk.W, pady=(0, 5))
        
        # Filtro por texto
        ttk.Label(control_panel, text="Texto:", style='Small.TLabel').pack(anchor=tk.W)
        self.log_filter_var = tk.StringVar()
        self.log_filter_entry = ttk.Entry(control_panel, textvariable=self.log_filter_var, width=15)
        self.log_filter_entry.pack(fill=tk.X, pady=(0, 5))
        self.log_filter_entry.bind('<KeyRelease>', lambda e: self.root.after(500, self.refresh_logs))
        
        # Filtro por fecha
        ttk.Label(control_panel, text="Fecha:", style='Small.TLabel').pack(anchor=tk.W, pady=(5, 0))
        self.log_date_var = tk.StringVar(value='hoy')
        date_options = ['hoy', 'ayer', '7_dias', '30_dias', 'todos']
        date_combo = ttk.Combobox(control_panel, textvariable=self.log_date_var,
                                 values=date_options, state='readonly', width=12)
        date_combo.pack(fill=tk.X, pady=(0, 10))
        date_combo.bind('<<ComboboxSelected>>', lambda e: self.refresh_logs())
        
        # Opciones de visualización
        ttk.Separator(control_panel, orient='horizontal').pack(fill=tk.X, pady=10)
        ttk.Label(control_panel, text="👁️ Vista:", style='Status.TLabel').pack(anchor=tk.W, pady=(0, 5))
        
        self.log_show_timestamps_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_panel, text="Timestamps", 
                       variable=self.log_show_timestamps_var,
                       command=self.refresh_logs).pack(anchor=tk.W, pady=1)
        
        self.log_show_levels_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_panel, text="Niveles", 
                       variable=self.log_show_levels_var,
                       command=self.refresh_logs).pack(anchor=tk.W, pady=1)
        
        self.log_show_modules_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_panel, text="Módulos", 
                       variable=self.log_show_modules_var,
                       command=self.refresh_logs).pack(anchor=tk.W, pady=1)
        
        self.log_word_wrap_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_panel, text="Word Wrap", 
                       variable=self.log_word_wrap_var,
                       command=self.toggle_word_wrap).pack(anchor=tk.W, pady=1)
        
        # Controles de cantidad
        ttk.Separator(control_panel, orient='horizontal').pack(fill=tk.X, pady=10)
        ttk.Label(control_panel, text="📏 Cantidad:", style='Status.TLabel').pack(anchor=tk.W, pady=(0, 5))
        
        self.log_lines_var = tk.StringVar(value='100')
        lines_options = ['50', '100', '500', '1000', 'todas']
        lines_combo = ttk.Combobox(control_panel, textvariable=self.log_lines_var,
                                  values=lines_options, state='readonly', width=12)
        lines_combo.pack(fill=tk.X, pady=(0, 10))
        lines_combo.bind('<<ComboboxSelected>>', lambda e: self.refresh_logs())
        
        # Botones de acción
        ttk.Separator(control_panel, orient='horizontal').pack(fill=tk.X, pady=10)
        
        ttk.Button(control_panel, text="🔄 Actualizar", 
                  command=self.refresh_logs).pack(fill=tk.X, pady=2)
        
        ttk.Button(control_panel, text="🧹 Limpiar Vista", 
                  command=self.clear_log_display).pack(fill=tk.X, pady=2)
        
        ttk.Button(control_panel, text="💾 Exportar", 
                  command=self.export_logs).pack(fill=tk.X, pady=2)
        
        ttk.Button(control_panel, text="🗑️ Limpiar Logs", 
                  command=self.clear_log_files).pack(fill=tk.X, pady=2)
        
        ttk.Button(control_panel, text="📊 Estadísticas", 
                  command=self.show_log_statistics).pack(fill=tk.X, pady=2)
        
        # Área principal de logs con pestañas
        logs_content_frame = ttk.Frame(main_logs_frame)
        logs_content_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Notebook para diferentes vistas de logs
        self.logs_notebook = ttk.Notebook(logs_content_frame)
        self.logs_notebook.pack(fill=tk.BOTH, expand=True)
        
        # Tab 1: Vista principal
        main_log_frame = ttk.Frame(self.logs_notebook)
        self.logs_notebook.add(main_log_frame, text="📄 Principal")
        
        self.logs_text = tk.Text(main_log_frame, wrap=tk.WORD, font=('Consolas', 9),
                                state='disabled', cursor='arrow')
        logs_scroll = ttk.Scrollbar(main_log_frame, orient=tk.VERTICAL, command=self.logs_text.yview)
        logs_h_scroll = ttk.Scrollbar(main_log_frame, orient=tk.HORIZONTAL, command=self.logs_text.xview)
        self.logs_text.configure(yscrollcommand=logs_scroll.set, xscrollcommand=logs_h_scroll.set)
        
        self.logs_text.grid(row=0, column=0, sticky='nsew')
        logs_scroll.grid(row=0, column=1, sticky='ns')
        logs_h_scroll.grid(row=1, column=0, sticky='ew')
        
        main_log_frame.grid_rowconfigure(0, weight=1)
        main_log_frame.grid_columnconfigure(0, weight=1)
        
        # Configurar tags para colores de niveles
        self.setup_log_tags()
        
        # Tab 2: Vista en tiempo real
        realtime_frame = ttk.Frame(self.logs_notebook)
        self.logs_notebook.add(realtime_frame, text="🔴 Tiempo Real")
        
        self.realtime_logs_text = tk.Text(realtime_frame, wrap=tk.WORD, font=('Consolas', 9),
                                         state='disabled', cursor='arrow', bg='#001122', fg='#00ff00')
        realtime_scroll = ttk.Scrollbar(realtime_frame, orient=tk.VERTICAL, command=self.realtime_logs_text.yview)
        self.realtime_logs_text.configure(yscrollcommand=realtime_scroll.set)
        
        self.realtime_logs_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        realtime_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Tab 3: Búsqueda avanzada
        search_frame = ttk.Frame(self.logs_notebook)
        self.logs_notebook.add(search_frame, text="🔍 Búsqueda")
        
        # Controles de búsqueda
        search_controls = ttk.Frame(search_frame)
        search_controls.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(search_controls, text="Buscar:", style='Status.TLabel').pack(side=tk.LEFT)
        self.search_entry = ttk.Entry(search_controls, width=30)
        self.search_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        ttk.Button(search_controls, text="🔍 Buscar", 
                  command=self.search_logs).pack(side=tk.LEFT, padx=5)
        
        self.search_case_var = tk.BooleanVar()
        ttk.Checkbutton(search_controls, text="Case sensitive", 
                       variable=self.search_case_var).pack(side=tk.LEFT, padx=5)
        
        # Resultados de búsqueda
        self.search_results_text = tk.Text(search_frame, wrap=tk.WORD, font=('Consolas', 9),
                                          state='disabled', cursor='arrow')
        search_results_scroll = ttk.Scrollbar(search_frame, orient=tk.VERTICAL, 
                                            command=self.search_results_text.yview)
        self.search_results_text.configure(yscrollcommand=search_results_scroll.set)
        
        self.search_results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=(0, 10))
        search_results_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=(0, 10))
        
        # Status bar para logs
        logs_status_frame = ttk.Frame(logs_frame)
        logs_status_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.logs_status_label = ttk.Label(logs_status_frame, text="Logs cargados", style='Small.TLabel')
        self.logs_status_label.pack(side=tk.LEFT)
        
        self.logs_count_label = ttk.Label(logs_status_frame, text="", style='Small.TLabel')
        self.logs_count_label.pack(side=tk.RIGHT)
        
        # Cargar logs iniciales
        self.refresh_logs()
    
    def setup_log_tags(self):
        """Configurar tags para colores de niveles de log"""
        self.logs_text.tag_configure('DEBUG', foreground='#808080')
        self.logs_text.tag_configure('INFO', foreground='#0000FF')
        self.logs_text.tag_configure('WARNING', foreground='#FF8C00')
        self.logs_text.tag_configure('ERROR', foreground='#FF0000', font=('Consolas', 9, 'bold'))
        self.logs_text.tag_configure('CRITICAL', foreground='#8B0000', font=('Consolas', 9, 'bold'))
        
        # Tags para búsqueda
        self.logs_text.tag_configure('SEARCH_HIGHLIGHT', background='#FFFF00')
        self.search_results_text.tag_configure('SEARCH_HIGHLIGHT', background='#FFFF00')
    
    def toggle_realtime_logs(self):
        """Activar/desactivar logs en tiempo real"""
        if self.logs_realtime_var.get():
            self.start_realtime_logs()
            self.logs_notebook.select(1)  # Cambiar a tab de tiempo real
        else:
            self.stop_realtime_logs()
    
    def start_realtime_logs(self):
        """Iniciar monitoreo de logs en tiempo real"""
        self.realtime_logs_active = True
        self.update_status("Logs en tiempo real activados", "info")
        
        def realtime_worker():
            try:
                from core.logger import gestor_logger
                
                # Obtener archivos de log actuales
                log_files = gestor_logger.get_log_files()
                file_positions = {}
                
                # Inicializar posiciones
                for category, file_path in log_files.items():
                    try:
                        file_positions[file_path] = Path(file_path).stat().st_size if Path(file_path).exists() else 0
                    except:
                        file_positions[file_path] = 0
                
                while self.realtime_logs_active and not self.shutdown_threads:
                    try:
                        new_lines = []
                        
                        for category, file_path in log_files.items():
                            if not Path(file_path).exists():
                                continue
                            
                            current_size = Path(file_path).stat().st_size
                            last_position = file_positions.get(file_path, 0)
                            
                            if current_size > last_position:
                                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                    f.seek(last_position)
                                    new_content = f.read()
                                    new_lines.extend([(category, line) for line in new_content.split('\n') if line.strip()])
                                
                                file_positions[file_path] = current_size
                        
                        if new_lines:
                            self.root.after(0, lambda: self.append_realtime_logs(new_lines))
                        
                        time.sleep(1)  # Check cada segundo
                        
                    except Exception as e:
                        self.logger.error(f"Error en realtime logs: {e}")
                        time.sleep(5)
                        
            except Exception as e:
                self.logger.error(f"Error fatal en realtime worker: {e}")
        
        self.realtime_thread = threading.Thread(target=realtime_worker, daemon=True)
        self.realtime_thread.start()
    
    def stop_realtime_logs(self):
        """Detener monitoreo de logs en tiempo real"""
        self.realtime_logs_active = False
        self.update_status("Logs en tiempo real desactivados", "info")
    
    def append_realtime_logs(self, new_lines):
        """Agregar nuevas líneas a logs en tiempo real"""
        try:
            self.realtime_logs_text.config(state='normal')
            
            for category, line in new_lines:
                timestamp = datetime.now().strftime("%H:%M:%S")
                formatted_line = f"[{timestamp}] [{category.upper()}] {line}\n"
                
                self.realtime_logs_text.insert(tk.END, formatted_line)
                
                # Mantener solo últimas 1000 líneas
                line_count = int(self.realtime_logs_text.index('end-1c').split('.')[0])
                if line_count > 1000:
                    self.realtime_logs_text.delete(1.0, f"{line_count-1000}.0")
            
            # Auto-scroll al final
            self.realtime_logs_text.see(tk.END)
            self.realtime_logs_text.config(state='disabled')
            
        except Exception as e:
            self.logger.error(f"Error agregando logs en tiempo real: {e}")
    
    def toggle_word_wrap(self):
        """Activar/desactivar word wrap"""
        wrap_mode = tk.WORD if self.log_word_wrap_var.get() else tk.NONE
        self.logs_text.config(wrap=wrap_mode)
        self.search_results_text.config(wrap=wrap_mode)
    
    def refresh_logs(self):
        """Actualizar vista de logs con filtros aplicados"""
        try:
            self.logs_text.config(state='normal')
            self.logs_text.delete(1.0, tk.END)
            
            # Mostrar mensaje de carga
            self.logs_text.insert(1.0, "🔄 Cargando logs...\n")
            self.logs_text.config(state='disabled')
            self.logs_text.update()
            
            def load_logs_worker():
                try:
                    from core.logger import gestor_logger
                    
                    category = self.log_category_var.get()
                    level_filter = self.log_level_var.get()
                    text_filter = self.log_filter_var.get().strip()
                    date_filter = self.log_date_var.get()
                    max_lines = self.log_lines_var.get()
                    
                    log_files = gestor_logger.get_log_files()
                    all_lines = []
                    
                    # Determinar archivos a leer
                    if category == 'todos':
                        files_to_read = log_files.values()
                    elif category in log_files:
                        files_to_read = [log_files[category]]
                    else:
                        files_to_read = []
                    
                    # Leer archivos
                    for file_path in files_to_read:
                        if not Path(file_path).exists():
                            continue
                        
                        try:
                            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                lines = f.readlines()
                                
                            # Aplicar filtros
                            filtered_lines = self.apply_log_filters(lines, level_filter, text_filter, date_filter)
                            all_lines.extend(filtered_lines)
                            
                        except Exception as e:
                            all_lines.append(f"❌ Error leyendo {file_path}: {e}\n")
                    
                    # Limitar número de líneas
                    if max_lines != 'todas':
                        max_count = int(max_lines)
                        if len(all_lines) > max_count:
                            all_lines = all_lines[-max_count:]
                    
                    # Formatear y mostrar
                    formatted_content = self.format_log_lines(all_lines)
                    
                    self.root.after(0, lambda: self.display_log_content(formatted_content, len(all_lines)))
                    
                except Exception as e:
                    error_msg = f"❌ Error cargando logs: {e}\n"
                    self.root.after(0, lambda: self.display_log_content(error_msg, 0))
            
            threading.Thread(target=load_logs_worker, daemon=True).start()
            
        except Exception as e:
            self.logs_text.config(state='normal')
            self.logs_text.delete(1.0, tk.END)
            self.logs_text.insert(1.0, f"❌ Error: {e}\n")
            self.logs_text.config(state='disabled')
    
    def apply_log_filters(self, lines, level_filter, text_filter, date_filter):
        """Aplicar filtros a las líneas de log"""
        from datetime import datetime, timedelta
        
        filtered_lines = []
        today = datetime.now().date()
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Filtro por nivel
            if level_filter != 'todos':
                if level_filter.upper() not in line:
                    continue
            
            # Filtro por texto
            if text_filter:
                if text_filter.lower() not in line.lower():
                    continue
            
            # Filtro por fecha
            if date_filter != 'todos':
                try:
                    # Extraer fecha de la línea (formato: YYYY-MM-DD)
                    date_part = line.split(' - ')[0]
                    log_date = datetime.strptime(date_part, '%Y-%m-%d %H:%M:%S').date()
                    
                    if date_filter == 'hoy' and log_date != today:
                        continue
                    elif date_filter == 'ayer' and log_date != today - timedelta(days=1):
                        continue
                    elif date_filter == '7_dias' and (today - log_date).days > 7:
                        continue
                    elif date_filter == '30_dias' and (today - log_date).days > 30:
                        continue
                        
                except:
                    # Si no puede parsear la fecha, incluir la línea
                    pass
            
            filtered_lines.append(line)
        
        return filtered_lines
    
    def format_log_lines(self, lines):
        """Formatear líneas de log para mostrar"""
        formatted_lines = []
        
        for line in lines:
            if not line.strip():
                continue
            
            # Aplicar opciones de visualización
            formatted_line = line
            
            if not self.log_show_timestamps_var.get():
                # Remover timestamp (primera parte antes del primer -)
                parts = line.split(' - ', 1)
                if len(parts) > 1:
                    formatted_line = parts[1]
            
            if not self.log_show_levels_var.get():
                # Remover nivel de log
                for level in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']:
                    formatted_line = formatted_line.replace(f' - {level} - ', ' - ')
            
            if not self.log_show_modules_var.get():
                # Remover módulo (parte después del nivel)
                parts = formatted_line.split(' - ')
                if len(parts) >= 3:
                    formatted_line = f"{parts[0]} - {' - '.join(parts[2:])}"
            
            formatted_lines.append(formatted_line)
        
        return '\n'.join(formatted_lines)
    
    def display_log_content(self, content, line_count):
        """Mostrar contenido de logs en la interfaz"""
        try:
            self.logs_text.config(state='normal')
            self.logs_text.delete(1.0, tk.END)
            
            if content.strip():
                self.logs_text.insert(1.0, content)
                
                # Aplicar colores por nivel
                self.apply_log_colors()
                
                # Auto-scroll al final
                self.logs_text.see(tk.END)
            else:
                self.logs_text.insert(1.0, "ℹ️ No hay logs que coincidan con los filtros aplicados\n")
            
            self.logs_text.config(state='disabled')
            
            # Actualizar status
            self.logs_count_label.config(text=f"{line_count} líneas")
            
        except Exception as e:
            self.logger.error(f"Error mostrando logs: {e}")
    
    def apply_log_colors(self):
        """Aplicar colores a diferentes niveles de log"""
        try:
            content = self.logs_text.get(1.0, tk.END)
            lines = content.split('\n')
            
            for i, line in enumerate(lines):
                line_start = f"{i+1}.0"
                line_end = f"{i+1}.end"
                
                if 'DEBUG' in line:
                    self.logs_text.tag_add('DEBUG', line_start, line_end)
                elif 'INFO' in line:
                    self.logs_text.tag_add('INFO', line_start, line_end)
                elif 'WARNING' in line:
                    self.logs_text.tag_add('WARNING', line_start, line_end)
                elif 'ERROR' in line:
                    self.logs_text.tag_add('ERROR', line_start, line_end)
                elif 'CRITICAL' in line:
                    self.logs_text.tag_add('CRITICAL', line_start, line_end)
                    
        except Exception as e:
            self.logger.debug(f"Error aplicando colores: {e}")

# FIN PIEZA 4

    """
    dashboard_pieza5a.py
    PIEZA 5A: Métodos de logs avanzados - búsqueda, exportar, estadísticas
    Gestor de Archivos Corporativo
    """

# CONTINUACIÓN DE LA CLASE GestorDashboard - MÉTODOS DE LOGS AVANZADOS

    def search_logs(self):
        """Buscar texto en logs"""
        search_term = self.search_entry.get().strip()
        if not search_term:
            messagebox.showwarning("Búsqueda", "Ingrese un término de búsqueda")
            return
        
        self.search_results_text.config(state='normal')
        self.search_results_text.delete(1.0, tk.END)
        self.search_results_text.insert(1.0, f"🔍 Buscando '{search_term}'...\n")
        self.search_results_text.config(state='disabled')
        self.search_results_text.update()
        
        def search_worker():
            try:
                from core.logger import gestor_logger
                
                log_files = gestor_logger.get_log_files()
                results = []
                case_sensitive = self.search_case_var.get()
                
                for category, file_path in log_files.items():
                    if not Path(file_path).exists():
                        continue
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            lines = f.readlines()
                        
                        for line_num, line in enumerate(lines, 1):
                            search_line = line if case_sensitive else line.lower()
                            search_target = search_term if case_sensitive else search_term.lower()
                            
                            if search_target in search_line:
                                results.append((category, line_num, line.strip()))
                    
                    except Exception as e:
                        results.append(('ERROR', 0, f"Error leyendo {file_path}: {e}"))
                
                # Mostrar resultados
                search_content = f"🔍 RESULTADOS DE BÚSQUEDA: '{search_term}'\n"
                search_content += "=" * 60 + "\n\n"
                
                if results:
                    search_content += f"📊 Encontradas {len(results)} coincidencias:\n\n"
                    
                    for category, line_num, line in results[:100]:  # Máximo 100 resultados
                        search_content += f"[{category.upper()}:{line_num}] {line}\n"
                    
                    if len(results) > 100:
                        search_content += f"\n... y {len(results) - 100} coincidencias más\n"
                else:
                    search_content += "❌ No se encontraron coincidencias\n"
                
                self.root.after(0, lambda: self.display_search_results(search_content))
                
            except Exception as e:
                error_msg = f"❌ Error en búsqueda: {e}\n"
                self.root.after(0, lambda: self.display_search_results(error_msg))
        
        threading.Thread(target=search_worker, daemon=True).start()
    
    def display_search_results(self, content):
        """Mostrar resultados de búsqueda"""
        self.search_results_text.config(state='normal')
        self.search_results_text.delete(1.0, tk.END)
        self.search_results_text.insert(1.0, content)
        
        # Highlight términos de búsqueda
        search_term = self.search_entry.get().strip()
        if search_term:
            self.highlight_search_term(self.search_results_text, search_term)
        
        self.search_results_text.config(state='disabled')
    
    def highlight_search_term(self, text_widget, search_term):
        """Resaltar término de búsqueda en texto"""
        try:
            content = text_widget.get(1.0, tk.END)
            start_pos = '1.0'
            
            while True:
                pos = text_widget.search(search_term, start_pos, tk.END, nocase=not self.search_case_var.get())
                if not pos:
                    break
                
                end_pos = f"{pos}+{len(search_term)}c"
                text_widget.tag_add('SEARCH_HIGHLIGHT', pos, end_pos)
                start_pos = end_pos
                
        except Exception as e:
            self.logger.debug(f"Error resaltando búsqueda: {e}")
    
    def export_logs(self):
        """Exportar logs filtrados a archivo"""
        try:
            export_path = filedialog.asksaveasfilename(
                title="Exportar Logs",
                defaultextension=".txt",
                filetypes=[("Archivos de texto", "*.txt"), ("Todos los archivos", "*.*")]
            )
            
            if not export_path:
                return
            
            # Obtener contenido actual filtrado
            content = self.logs_text.get(1.0, tk.END)
            
            # Agregar metadatos
            header = f"=== EXPORT DE LOGS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===\n"
            header += f"Categoría: {self.log_category_var.get()}\n"
            header += f"Nivel: {self.log_level_var.get()}\n"
            header += f"Filtro: {self.log_filter_var.get() or 'Ninguno'}\n"
            header += f"Fecha: {self.log_date_var.get()}\n"
            header += f"Líneas: {self.log_lines_var.get()}\n"
            header += "=" * 60 + "\n\n"
            
            full_content = header + content
            
            with open(export_path, 'w', encoding='utf-8') as f:
                f.write(full_content)
            
            messagebox.showinfo("Exportar", f"Logs exportados correctamente a:\n{export_path}")
            self.update_status(f"Logs exportados: {Path(export_path).name}", "success")
            
        except Exception as e:
            messagebox.showerror("Error", f"Error exportando logs: {e}")
    
    def clear_log_files(self):
        """Limpiar archivos de log físicos"""
        if not messagebox.askyesno("Confirmar", 
                                   "¿Está seguro de que desea limpiar TODOS los archivos de log?\n\n"
                                   "Esta acción no se puede deshacer."):
            return
        
        try:
            from core.logger import gestor_logger
            
            log_files = gestor_logger.get_log_files()
            cleared_count = 0
            
            for category, file_path in log_files.items():
                try:
                    if Path(file_path).exists():
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(f"# Log limpiado el {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        cleared_count += 1
                except Exception as e:
                    self.logger.error(f"Error limpiando {file_path}: {e}")
            
            messagebox.showinfo("Limpiar Logs", f"Se limpiaron {cleared_count} archivos de log")
            self.update_status(f"{cleared_count} archivos de log limpiados", "success")
            self.refresh_logs()
            
        except Exception as e:
            messagebox.showerror("Error", f"Error limpiando logs: {e}")
    
    def show_log_statistics(self):
        """Mostrar estadísticas de logs"""
        stats_window = tk.Toplevel(self.root)
        stats_window.title("📊 Estadísticas de Logs")
        stats_window.geometry("600x500")
        stats_window.transient(self.root)
        
        stats_text = tk.Text(stats_window, wrap=tk.WORD, font=('Consolas', 9))
        stats_scroll = ttk.Scrollbar(stats_window, orient=tk.VERTICAL, command=stats_text.yview)
        stats_text.configure(yscrollcommand=stats_scroll.set)
        
        stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        stats_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
        # Calcular estadísticas en thread
        def calc_stats():
            try:
                from core.logger import gestor_logger
                
                stats_text.insert(1.0, "📊 Calculando estadísticas...\n")
                stats_text.update()
                
                log_files = gestor_logger.get_log_files()
                total_lines = 0
                total_size = 0
                level_counts = {'DEBUG': 0, 'INFO': 0, 'WARNING': 0, 'ERROR': 0, 'CRITICAL': 0}
                category_stats = {}
                
                for category, file_path in log_files.items():
                    if not Path(file_path).exists():
                        continue
                    
                    try:
                        file_size = Path(file_path).stat().st_size
                        total_size += file_size
                        
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            lines = f.readlines()
                        
                        file_lines = len(lines)
                        total_lines += file_lines
                        
                        category_stats[category] = {
                            'lines': file_lines,
                            'size': file_size,
                            'levels': {'DEBUG': 0, 'INFO': 0, 'WARNING': 0, 'ERROR': 0, 'CRITICAL': 0}
                        }
                        
                        for line in lines:
                            for level in level_counts.keys():
                                if level in line:
                                    level_counts[level] += 1
                                    category_stats[category]['levels'][level] += 1
                                    break
                    
                    except Exception as e:
                        category_stats[category] = {'error': str(e)}
                
                # Generar reporte
                report = []
                report.append("📊 ESTADÍSTICAS DE LOGS\n")
                report.append("=" * 50 + "\n\n")
                
                report.append("📈 RESUMEN GENERAL:\n")
                report.append(f"  Total líneas: {total_lines:,}\n")
                report.append(f"  Tamaño total: {total_size / (1024*1024):.2f} MB\n")
                report.append(f"  Archivos: {len([f for f in log_files.values() if Path(f).exists()])}\n\n")
                
                report.append("📊 POR NIVEL:\n")
                for level, count in level_counts.items():
                    percentage = (count / total_lines * 100) if total_lines > 0 else 0
                    report.append(f"  {level:<8}: {count:,} ({percentage:.1f}%)\n")
                
                report.append("\n📁 POR CATEGORÍA:\n")
                for category, stats in category_stats.items():
                    if 'error' in stats:
                        report.append(f"  {category.upper()}: ❌ Error - {stats['error']}\n")
                    else:
                        size_mb = stats['size'] / (1024*1024)
                        report.append(f"  {category.upper()}:\n")
                        report.append(f"    Líneas: {stats['lines']:,}\n")
                        report.append(f"    Tamaño: {size_mb:.2f} MB\n")
                        
                        # Niveles por categoría
                        for level, count in stats['levels'].items():
                            if count > 0:
                                report.append(f"    {level}: {count}\n")
                        report.append("\n")
                
                stats_content = ''.join(report)
                stats_text.delete(1.0, tk.END)
                stats_text.insert(1.0, stats_content)
                
            except Exception as e:
                stats_text.delete(1.0, tk.END)
                stats_text.insert(1.0, f"❌ Error calculando estadísticas: {e}\n")
        
        threading.Thread(target=calc_stats, daemon=True).start()
        
        ttk.Button(stats_window, text="Cerrar", command=stats_window.destroy).pack(pady=10)
    
    def clear_log_display(self):
        """Limpiar vista de logs"""
        self.logs_text.config(state='normal')
        self.logs_text.delete(1.0, tk.END)
        self.logs_text.insert(1.0, "Vista limpiada\n")
        self.logs_text.config(state='disabled')
        
        self.logs_count_label.config(text="0 líneas")
        self.update_status("Vista de logs limpiada", "info")
    
    def update_connectivity_stats(self):
        """Actualizar estadísticas de conectividad"""
        try:
            stats_content = []
            stats_content.append("🌐 ESTADÍSTICAS DE CONECTIVIDAD\n")
            stats_content.append("=" * 40 + "\n\n")
            
            online_count = 0
            total_count = len(getattr(self, 'connectivity_labels', {}))
            response_times = []
            
            for location_id, status in self.connectivity_status.items():
                if status.get('connected', False):
                    online_count += 1
                    if 'response_time' in status:
                        response_times.append(status['response_time'])
                
                stats_content.append(f"📍 {location_id.upper()}:\n")
                
                if status.get('connected', False):
                    stats_content.append(f"   Estado: 🟢 ONLINE\n")
                    if 'response_time' in status:
                        stats_content.append(f"   Tiempo respuesta: {status['response_time']:.2f}s\n")
                    if 'method' in status:
                        stats_content.append(f"   Método: {status['method']}\n")
                    if 'write_access' in status:
                        access = "✅ Lectura/Escritura" if status['write_access'] else "⚠️ Solo lectura"
                        stats_content.append(f"   Acceso: {access}\n")
                else:
                    stats_content.append(f"   Estado: 🔴 OFFLINE\n")
                    if 'error' in status:
                        stats_content.append(f"   Error: {status['error']}\n")
                
                stats_content.append("\n")
            
            # Estadísticas generales
            stats_content.append("📊 RESUMEN:\n")
            stats_content.append(f"   Ubicaciones online: {online_count}/{total_count}\n")
            availability = (online_count / total_count * 100) if total_count > 0 else 0
            stats_content.append(f"   Disponibilidad: {availability:.1f}%\n")
            
            if response_times:
                avg_time = sum(response_times) / len(response_times)
                min_time = min(response_times)
                max_time = max(response_times)
                
                stats_content.append(f"   Tiempo promedio: {avg_time:.2f}s\n")
                stats_content.append(f"   Tiempo mínimo: {min_time:.2f}s\n")
                stats_content.append(f"   Tiempo máximo: {max_time:.2f}s\n")
            
            # Actualizar widget
            if hasattr(self, 'connectivity_stats_text'):
                self.connectivity_stats_text.config(state='normal')
                self.connectivity_stats_text.delete(1.0, tk.END)
                self.connectivity_stats_text.insert(1.0, ''.join(stats_content))
                self.connectivity_stats_text.config(state='disabled')
                
        except Exception as e:
            self.logger.error(f"Error actualizando estadísticas conectividad: {e}")
    
    def update_operations_stats(self):
        """Actualizar estadísticas de operaciones"""
        try:
            stats_content = []
            stats_content.append("⚙️ ESTADÍSTICAS DE OPERACIONES\n")
            stats_content.append("=" * 40 + "\n\n")
            
            if hasattr(self, 'last_operation_stats') and self.last_operation_stats:
                for operation, stats in self.last_operation_stats.items():
                    stats_content.append(f"🔧 {operation.upper()}:\n")
                    
                    if operation == 'validacion':
                        stats_content.append(f"   Archivos analizados: {stats.get('archivos_analizados', 0)}\n")
                        stats_content.append(f"   Duplicados encontrados: {stats.get('duplicados_encontrados', 0)}\n")
                        stats_content.append(f"   Archivos movidos: {stats.get('archivos_movidos', 0)}\n")
                        
                    elif operation == 'limpieza':
                        stats_content.append(f"   Archivos válidos: {stats.get('archivos_validos', 0)}\n")
                        stats_content.append(f"   Total eliminados: {stats.get('total_archivos_eliminados', 0)}\n")
                        stats_content.append(f"   Ubicaciones procesadas: {stats.get('ubicaciones_conectadas', 0)}\n")
                    
                    duracion = stats.get('duracion_total', 0)
                    stats_content.append(f"   Duración: {duracion:.2f}s\n")
                    stats_content.append(f"   Errores: {stats.get('errores', 0)}\n\n")
            else:
                stats_content.append("ℹ️ No hay operaciones completadas aún\n")
            
            # Actualizar widget
            if hasattr(self, 'operations_stats_text'):
                self.operations_stats_text.config(state='normal')
                self.operations_stats_text.delete(1.0, tk.END)
                self.operations_stats_text.insert(1.0, ''.join(stats_content))
                self.operations_stats_text.config(state='disabled')
                
        except Exception as e:
            self.logger.error(f"Error actualizando estadísticas operaciones: {e}")
    
    def update_system_stats(self):
        """Actualizar estadísticas del sistema (worker thread)"""
        try:
            import psutil
            
            # CPU y memoria
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Actualizar connectivity y operations stats
            self.update_connectivity_stats()
            self.update_operations_stats()
            
        except Exception as e:
            self.logger.error(f"Error en stats worker: {e}")

# FIN PIEZA 5A

    """
    dashboard_pieza5b.py
    PIEZA 5B: Métodos de ejecución principales - execute_validacion y execute_limpieza
    Gestor de Archivos Corporativo
    """

# CONTINUACIÓN DE LA CLASE GestorDashboard - MÉTODOS DE EJECUCIÓN PRINCIPALES

    def execute_validacion(self):
        """Ejecutar operación de validación completa"""
        # Validar configuración antes de ejecutar
        if not self.validate_validacion_config():
            return
        
        origen = self.validacion_origen_var.get().strip()
        rechazados = self.validacion_rechazados_var.get().strip()
        dry_run = self.validacion_dry_run_var.get()
        
        # Confirmar ejecución si no es dry run
        if not dry_run:
            if not messagebox.askyesno("Confirmar Ejecución", 
                                       "¿Ejecutar validación en modo REAL?\n\n"
                                       "Se moverán archivos duplicados."):
                return
        
        # Preparar UI para ejecución
        self.validacion_execute_btn.config(state='disabled')
        self.validacion_stop_btn.config(state='normal')
        self.operaciones_activas['validacion'] = True
        self.update_operations_indicator()
        
        mode_text = "SIMULACIÓN" if dry_run else "REAL"
        self.update_status(f"Ejecutando validación en modo {mode_text}...", "info")
        
        # Limpiar resultados previos
        self.show_validacion_results("", "summary")
        self.show_validacion_results("", "log")
        self.show_validacion_results("", "stats")
        
        # Inicializar progreso
        self.update_validacion_progress(0, "Iniciando validación...")
        
        def validacion_worker():
            try:
                from apps.validacion_duplicados import ValidacionDuplicados
                
                # Crear instancia de validación
                validacion = ValidacionDuplicados()
                
                # Configurar parámetros
                config = {
                    'carpeta_origen': origen,
                    'carpeta_rechazados': rechazados,
                    'dry_run': dry_run,
                    'backup': getattr(self, 'validacion_backup_var', tk.BooleanVar()).get(),
                    'log_detailed': getattr(self, 'validacion_log_detailed_var', tk.BooleanVar()).get(),
                    'verify_integrity': getattr(self, 'validacion_verify_integrity_var', tk.BooleanVar()).get()
                }
                
                # Callbacks para progreso
                def on_progress(progress, message=""):
                    self.root.after(0, lambda: self.update_validacion_progress(progress, message))
                
                def on_log(message):
                    self.root.after(0, lambda: self.append_validacion_log(message))
                
                def on_file_processed(filename, action, details=""):
                    log_msg = f"📄 {filename}: {action}"
                    if details:
                        log_msg += f" - {details}"
                    self.root.after(0, lambda: self.append_validacion_log(log_msg))
                
                # Ejecutar validación
                self.logger.info(f"Iniciando validación: {config}")
                resultado = validacion.ejecutar_validacion(config, 
                                                         progress_callback=on_progress,
                                                         log_callback=on_log,
                                                         file_callback=on_file_processed)
                
                # Procesar resultado
                self.logger.info(f"Validación completada: {resultado}")
                
                # Formatear y mostrar resultados
                summary_text = self.format_validacion_results(resultado)
                stats_text = self.format_validacion_stats(resultado)
                
                self.root.after(0, lambda: self.show_validacion_results(summary_text, "summary"))
                self.root.after(0, lambda: self.show_validacion_results(stats_text, "stats"))
                
                # Actualizar estadísticas globales
                if not hasattr(self, 'last_operation_stats'):
                    self.last_operation_stats = {}
                self.last_operation_stats['validacion'] = resultado
                self.root.after(0, self.update_operations_stats)
                
                # Finalizar operación
                self.root.after(0, self.finish_validacion)
                
                success_msg = f"Validación completada - {resultado.get('archivos_movidos', 0)} archivos procesados"
                self.root.after(0, lambda: self.update_status(success_msg, "success"))
                
            except Exception as e:
                error_msg = f"❌ Error ejecutando validación: {e}"
                self.logger.error(error_msg)
                self.root.after(0, lambda: self.show_validacion_results(error_msg, "summary"))
                self.root.after(0, lambda: self.update_status(error_msg, "error"))
                self.root.after(0, self.finish_validacion)
                
                # Log del error detallado
                import traceback
                self.logger.error(f"Stacktrace: {traceback.format_exc()}")
        
        # Ejecutar en thread separado
        threading.Thread(target=validacion_worker, daemon=True).start()
    
    def execute_limpieza(self):
        """Ejecutar operación de limpieza completa"""
        # Validar configuración antes de ejecutar
        if not self.validate_limpieza_config():
            return
        
        archivo_lista = self.limpieza_archivo_var.get().strip()
        dry_run = self.limpieza_dry_run_var.get()
        selected_locations = [loc for loc, var in self.limpieza_locations.items() if var.get()]
        
        # Confirmar ejecución si no es dry run
        if not dry_run:
            confirmation_msg = f"¿Ejecutar limpieza en modo REAL?\n\n"
            confirmation_msg += f"Se eliminarán archivos en {len(selected_locations)} ubicaciones:\n"
            for loc in selected_locations:
                confirmation_msg += f"• {loc.upper()}\n"
            
            if not messagebox.askyesno("Confirmar Ejecución", confirmation_msg):
                return
        
        # Preparar UI para ejecución
        self.limpieza_execute_btn.config(state='disabled')
        self.limpieza_stop_btn.config(state='normal')
        self.operaciones_activas['limpieza'] = True
        self.update_operations_indicator()
        
        # Inicializar progreso por ubicación
        for location in selected_locations:
            if location in self.location_progress_labels:
                self.location_progress_labels[location]['progress']['value'] = 0
                self.location_progress_labels[location]['status'].config(text="Preparando...")
                self.location_progress_labels[location]['files'].config(text="0/0")
        
        mode_text = "SIMULACIÓN" if dry_run else "REAL"
        self.update_status(f"Ejecutando limpieza en modo {mode_text}...", "info")
        
        # Limpiar resultados previos
        self.show_limpieza_results("", "summary")
        self.show_limpieza_results("", "location")
        self.show_limpieza_results("", "log")
        
        # Progreso general inicial
        self.update_limpieza_progress(0, None, "Iniciando limpieza...")
        
        def limpieza_worker():
            try:
                from apps.limpieza_lista import LimpiezaLista
                
                # Crear instancia de limpieza
                limpieza = LimpiezaLista()
                
                # Configurar parámetros
                config = {
                    'archivo_lista': archivo_lista,
                    'ubicaciones_seleccionadas': selected_locations,
                    'dry_run': dry_run,
                    'backup': getattr(self, 'limpieza_backup_var', tk.BooleanVar()).get(),
                    'paralelo': getattr(self, 'limpieza_parallel_var', tk.BooleanVar()).get(),
                    'confirmar': getattr(self, 'limpieza_confirm_var', tk.BooleanVar()).get(),
                    'verificar_antes': getattr(self, 'limpieza_verify_before_var', tk.BooleanVar()).get(),
                    'continuar_errores': getattr(self, 'limpieza_skip_errors_var', tk.BooleanVar()).get()
                }
                
                # Callbacks para progreso
                def on_progress(progress, location=None, message=""):
                    self.root.after(0, lambda: self.update_limpieza_progress(progress, location, message))
                
                def on_location_progress(location, progress, found, processed, status):
                    self.root.after(0, lambda: self.update_location_progress(location, progress, found, processed, status))
                
                def on_log(message):
                    self.root.after(0, lambda: self.append_limpieza_log(message))
                
                def on_location_start(location):
                    self.root.after(0, lambda: self.update_location_progress(location, 0, 0, 0, "Conectando..."))
                
                def on_location_complete(location, stats):
                    found = stats.get('archivos_encontrados', 0)
                    deleted = stats.get('archivos_eliminados', 0) 
                    status = f"✅ {deleted}/{found}"
                    self.root.after(0, lambda: self.update_location_progress(location, 100, found, deleted, status))
                
                def on_file_processed(location, filename, action, success):
                    icon = "✅" if success else "❌"
                    log_msg = f"📍 {location.upper()}: {icon} {filename} - {action}"
                    self.root.after(0, lambda: self.append_limpieza_log(log_msg))
                
                # Ejecutar limpieza
                self.logger.info(f"Iniciando limpieza: {config}")
                resultado = limpieza.ejecutar_limpieza(config,
                                                     progress_callback=on_progress,
                                                     location_callback=on_location_progress,
                                                     log_callback=on_log,
                                                     location_start_callback=on_location_start,
                                                     location_complete_callback=on_location_complete,
                                                     file_callback=on_file_processed)
                
                # Procesar resultado
                self.logger.info(f"Limpieza completada: {resultado}")
                
                # Formatear y mostrar resultados
                summary_text = self.format_limpieza_results(resultado)
                location_text = self.format_limpieza_by_location(resultado)
                
                self.root.after(0, lambda: self.show_limpieza_results(summary_text, "summary"))
                self.root.after(0, lambda: self.show_limpieza_results(location_text, "location"))
                
                # Actualizar estadísticas globales
                if not hasattr(self, 'last_operation_stats'):
                    self.last_operation_stats = {}
                self.last_operation_stats['limpieza'] = resultado
                self.root.after(0, self.update_operations_stats)
                
                # Finalizar operación
                self.root.after(0, self.finish_limpieza)
                
                total_eliminados = resultado.get('total_archivos_eliminados', 0)
                total_encontrados = resultado.get('total_archivos_encontrados', 0)
                success_msg = f"Limpieza completada - {total_eliminados}/{total_encontrados} archivos procesados"
                self.root.after(0, lambda: self.update_status(success_msg, "success"))
                
            except Exception as e:
                error_msg = f"❌ Error ejecutando limpieza: {e}"
                self.logger.error(error_msg)
                self.root.after(0, lambda: self.show_limpieza_results(error_msg, "summary"))
                self.root.after(0, lambda: self.update_status(error_msg, "error"))
                self.root.after(0, self.finish_limpieza)
                
                # Log del error detallado
                import traceback
                self.logger.error(f"Stacktrace: {traceback.format_exc()}")
        
        # Ejecutar en thread separado
        threading.Thread(target=limpieza_worker, daemon=True).start()
    
    def stop_validacion(self):
        """Detener operación de validación"""
        if messagebox.askyesno("Confirmar", "¿Detener la operación de validación?\n\nLos archivos ya procesados permanecerán en su estado actual."):
            self.operaciones_activas['validacion'] = False
            self.finish_validacion()
            self.update_status("Validación detenida por el usuario", "warning")
            self.append_validacion_log("🛑 Operación detenida por el usuario")
    
    def stop_limpieza(self):
        """Detener operación de limpieza"""
        if messagebox.askyesno("Confirmar", "¿Detener la operación de limpieza?\n\nLos archivos ya procesados permanecerán eliminados."):
            self.operaciones_activas['limpieza'] = False
            self.finish_limpieza()
            self.update_status("Limpieza detenida por el usuario", "warning")
            self.append_limpieza_log("🛑 Operación detenida por el usuario")
    
    def finish_validacion(self):
        """Finalizar operación de validación"""
        self.validacion_execute_btn.config(state='normal')
        self.validacion_stop_btn.config(state='disabled')
        self.operaciones_activas['validacion'] = False
        self.update_operations_indicator()
        self.update_validacion_progress(100, "Validación completada")
        
        # Log de finalización
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.append_validacion_log(f"🏁 Operación finalizada: {timestamp}")
    
    def finish_limpieza(self):
        """Finalizar operación de limpieza"""
        self.limpieza_execute_btn.config(state='normal')
        self.limpieza_stop_btn.config(state='disabled')
        self.operaciones_activas['limpieza'] = False
        self.update_operations_indicator()
        self.update_limpieza_progress(100, None, "Limpieza completada")
        
        # Actualizar todas las ubicaciones a completadas si no estaban ya
        for location in self.location_progress_labels:
            if "✅" not in self.location_progress_labels[location]['status'].cget('text'):
                self.location_progress_labels[location]['status'].config(text="Terminado")
                self.location_progress_labels[location]['progress']['value'] = 100
        
        # Log de finalización
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.append_limpieza_log(f"🏁 Operación finalizada: {timestamp}")
    
    def format_validacion_stats(self, stats: Dict[str, Any]) -> str:
        """Formatear estadísticas detalladas de validación"""
        if not stats:
            return "❌ No hay estadísticas disponibles\n"
        
        result = "📈 ESTADÍSTICAS DETALLADAS - VALIDACIÓN\n"
        result += "=" * 50 + "\n\n"
        
        # Tiempos de ejecución
        duracion = stats.get('duracion_total', 0)
        result += f"⏱️ RENDIMIENTO:\n"
        result += f"   Duración total: {duracion:.2f} segundos\n"
        
        if duracion > 0:
            archivos = stats.get('archivos_analizados', 0)
            if archivos > 0:
                result += f"   Velocidad: {archivos/duracion:.1f} archivos/segundo\n"
        
        result += f"\n📊 RESUMEN EJECUTIVO:\n"
        result += f"   Archivos analizados: {stats.get('archivos_analizados', 0)}\n"
        result += f"   Duplicados detectados: {stats.get('duplicados_encontrados', 0)}\n"
        result += f"   Archivos movidos: {stats.get('archivos_movidos', 0)}\n"
        result += f"   Errores encontrados: {stats.get('errores', 0)}\n"
        
        # Tasa de éxito
        analizados = stats.get('archivos_analizados', 0)
        movidos = stats.get('archivos_movidos', 0)
        if analizados > 0:
            tasa_exito = (movidos / analizados) * 100
            result += f"   Tasa de procesamiento: {tasa_exito:.1f}%\n"
        
        # Información de ubicaciones verificadas
        ubicaciones_stats = stats.get('ubicaciones_verificadas', {})
        if ubicaciones_stats:
            result += f"\n🌐 UBICACIONES VERIFICADAS:\n"
            for ubicacion_id, ubicacion_info in ubicaciones_stats.items():
                status = "✅" if ubicacion_info.get('conectado', False) else "❌"
                result += f"   {status} {ubicacion_id.upper()}: "
                if ubicacion_info.get('conectado', False):
                    archivos_verificados = ubicacion_info.get('archivos_encontrados', 0)
                    result += f"{archivos_verificados} archivos verificados\n"
                else:
                    result += f"Error de conexión\n"
        
        # Detalles de archivos si es modo simulación
        if stats.get('dry_run', False):
            result += f"\n🧪 MODO SIMULACIÓN:\n"
            result += f"   No se realizaron cambios reales\n"
            result += f"   Todos los duplicados serían movidos a:\n"
            result += f"   {stats.get('carpeta_rechazados', 'N/A')}\n"
        
        return result
    
    def format_limpieza_by_location(self, stats: Dict[str, Any]) -> str:
        """Formatear resultados de limpieza por ubicación"""
        if not stats:
            return "❌ No hay estadísticas por ubicación\n"
        
        result = "🌐 RESULTADOS POR UBICACIÓN\n"
        result += "=" * 40 + "\n\n"
        
        ubicaciones_stats = stats.get('estadisticas_por_ubicacion', {})
        
        for ubicacion_id, ubicacion_stats in ubicaciones_stats.items():
            result += f"📍 {ubicacion_id.upper()}:\n"
            
            if ubicacion_stats.get('conectado', False):
                result += f"   Estado: ✅ Conectado\n"
                result += f"   Archivos encontrados: {ubicacion_stats.get('archivos_encontrados', 0)}\n"
                result += f"   Archivos eliminados: {ubicacion_stats.get('archivos_eliminados', 0)}\n"
                result += f"   Errores: {ubicacion_stats.get('errores', 0)}\n"
                result += f"   Tiempo: {ubicacion_stats.get('tiempo_procesamiento', 0):.2f}s\n"
                
                # Detalles de archivos procesados
                archivos_procesados = ubicacion_stats.get('archivos_procesados', [])
                if archivos_procesados:
                    result += f"   Archivos procesados ({len(archivos_procesados)}):\n"
                    for archivo_info in archivos_procesados[:5]:  # Solo primeros 5
                        nombre = archivo_info.get('nombre', 'N/A')
                        accion = archivo_info.get('accion', 'N/A')
                        exito = archivo_info.get('exito', False)
                        status_icon = "✅" if exito else "❌"
                        result += f"     {status_icon} {nombre} - {accion}\n"
                    
                    if len(archivos_procesados) > 5:
                        result += f"     ... y {len(archivos_procesados) - 5} más\n"
                        
            else:
                result += f"   Estado: ❌ Error de conexión\n"
                error_msg = ubicacion_stats.get('error', 'Error desconocido')
                result += f"   Error: {error_msg}\n"
            
            result += "\n"
        
        # Resumen general
        total_encontrados = sum(loc.get('archivos_encontrados', 0) for loc in ubicaciones_stats.values())
        total_eliminados = sum(loc.get('archivos_eliminados', 0) for loc in ubicaciones_stats.values())
        total_errores = sum(loc.get('errores', 0) for loc in ubicaciones_stats.values())
        
        result += "📊 RESUMEN GENERAL:\n"
        result += f"   Total encontrados: {total_encontrados}\n"
        result += f"   Total eliminados: {total_eliminados}\n"
        result += f"   Total errores: {total_errores}\n"
        
        if total_encontrados > 0:
            efectividad = (total_eliminados / total_encontrados) * 100
            result += f"   Efectividad: {efectividad:.1f}%\n"
        
        return result

# FIN PIEZA 5B

    """
    dashboard_pieza5c.py
    PIEZA 5C: Métodos auxiliares finales + cierre de aplicación
    Gestor de Archivos Corporativo
    """

# CONTINUACIÓN DE LA CLASE GestorDashboard - MÉTODOS AUXILIARES FINALES

    def update_limpieza_progress(self, progress: int, location: str = None, message: str = ""):
        """Actualizar progreso de limpieza"""
        try:
            if hasattr(self, 'limpieza_progress'):
                self.limpieza_progress['value'] = progress
                self.limpieza_progress_label.config(text=f"{progress}%")
                
                if message:
                    self.update_status(message, "info")
                
                self.root.update_idletasks()
        except Exception as e:
            self.logger.error(f"Error actualizando progreso limpieza: {e}")
    
    def update_location_progress(self, location: str, progress: int, found: int, processed: int, status: str):
        """Actualizar progreso individual por ubicación"""
        try:
            if location in self.location_progress_labels:
                widgets = self.location_progress_labels[location]
                widgets['progress']['value'] = progress
                widgets['status'].config(text=status[:12])  # Truncar para que quepa
                widgets['files'].config(text=f"{processed}/{found}")
                
                # Color según estado
                if "✅" in status:
                    widgets['status'].config(foreground='#2E7D32')
                elif "❌" in status:
                    widgets['status'].config(foreground='#C62828')
                elif "Conectando" in status or "Preparando" in status:
                    widgets['status'].config(foreground='#1976D2')
                else:
                    widgets['status'].config(foreground='black')
                    
        except Exception as e:
            self.logger.error(f"Error actualizando progreso ubicación {location}: {e}")
    
    def show_limpieza_results(self, text: str, tab: str):
        """Mostrar resultados de limpieza en pestaña específica"""
        try:
            widget_map = {
                "summary": getattr(self, 'limpieza_summary_text', None),
                "location": getattr(self, 'limpieza_location_text', None),
                "log": getattr(self, 'limpieza_log_text', None)
            }
            
            widget = widget_map.get(tab)
            if widget:
                widget.config(state='normal')
                widget.delete(1.0, tk.END)
                widget.insert(1.0, text)
                widget.see(tk.END)
                widget.config(state='disabled')
                
        except Exception as e:
            self.logger.error(f"Error mostrando resultados limpieza: {e}")
    
    def append_limpieza_log(self, message: str):
        """Agregar mensaje al log de limpieza"""
        try:
            if hasattr(self, 'limpieza_log_text'):
                self.limpieza_log_text.config(state='normal')
                timestamp = datetime.now().strftime("%H:%M:%S")
                self.limpieza_log_text.insert(tk.END, f"[{timestamp}] {message}\n")
                self.limpieza_log_text.see(tk.END)
                self.limpieza_log_text.config(state='disabled')
        except Exception as e:
            self.logger.error(f"Error agregando log limpieza: {e}")
    
    def show_connection_config(self):
        """Mostrar configuración de conexiones"""
        config_window = tk.Toplevel(self.root)
        config_window.title("⚙️ Configuración de Conexiones")
        config_window.geometry("700x600")
        config_window.transient(self.root)
        config_window.grab_set()
        
        # Header
        header_frame = ttk.Frame(config_window)
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(header_frame, text="⚙️ Configuración de Conexiones", 
                 style='Title.TLabel').pack(side=tk.LEFT)
        
        # Info
        info_frame = ttk.LabelFrame(config_window, text="ℹ️ Información", padding="10")
        info_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        info_text = """
Esta ventana permite ver la configuración actual de conexiones.
Para modificar las configuraciones, edite los archivos JSON en la carpeta 'config/'.

Archivo principal: config/config.json
Credenciales: Se gestionan automáticamente por el sistema
        """
        
        ttk.Label(info_frame, text=info_text.strip(), justify=tk.LEFT).pack()
        
        # Configuración actual
        config_frame = ttk.LabelFrame(config_window, text="📋 Configuración Actual", padding="10")
        config_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        config_text = tk.Text(config_frame, wrap=tk.WORD, font=('Consolas', 9), state='disabled')
        config_scroll = ttk.Scrollbar(config_frame, orient=tk.VERTICAL, command=config_text.yview)
        config_text.configure(yscrollcommand=config_scroll.set)
        
        config_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        config_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Cargar configuración
        def load_config():
            try:
                from core.config_manager import config_manager
                
                config_content = []
                config_content.append("📋 CONFIGURACIÓN DE CONEXIONES\n")
                config_content.append("=" * 50 + "\n\n")
                
                locations = config_manager.get_enabled_locations()
                config_content.append(f"📊 Ubicaciones habilitadas: {len(locations)}\n\n")
                
                for location_id in locations:
                    location_config = config_manager.get_location_config(location_id)
                    if location_config:
                        config_content.append(f"📍 {location_id.upper()}:\n")
                        config_content.append(f"   Tipo: {location_config.get('type', 'N/A')}\n")
                        config_content.append(f"   Ruta de red: {location_config.get('network_path', 'N/A')}\n")
                        config_content.append(f"   Ruta simbólica: {location_config.get('symlink_path', 'N/A')}\n")
                        config_content.append(f"   Habilitado: {location_config.get('enabled', False)}\n")
                        
                        # Info de credenciales (sin mostrar contraseñas)
                        creds = location_config.get('credentials', {})
                        if creds:
                            config_content.append(f"   Usuario: {creds.get('username', 'N/A')}\n")
                            config_content.append(f"   Dominio: {creds.get('domain', 'N/A')}\n")
                            config_content.append(f"   Contraseña: {'***' if creds.get('password_encrypted') else 'No configurada'}\n")
                        
                        config_content.append("\n")
                
                config_text.config(state='normal')
                config_text.delete(1.0, tk.END)
                config_text.insert(1.0, ''.join(config_content))
                config_text.config(state='disabled')
                
            except Exception as e:
                config_text.config(state='normal')
                config_text.delete(1.0, tk.END)
                config_text.insert(1.0, f"❌ Error cargando configuración: {e}\n")
                config_text.config(state='disabled')
        
        load_config()
        
        # Botones
        buttons_frame = ttk.Frame(config_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(buttons_frame, text="🔄 Actualizar", command=load_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Cerrar", command=config_window.destroy).pack(side=tk.RIGHT, padx=5)
    
    def show_connection_test_results(self, results):
        """Mostrar resultados de test de conexiones"""
        results_window = tk.Toplevel(self.root)
        results_window.title("📊 Resultados de Test")
        results_window.geometry("600x500")
        results_window.transient(self.root)
        
        # Header
        header_frame = ttk.Frame(results_window)
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(header_frame, text="📊 Resultados del Test de Conexiones", 
                 style='Header.TLabel').pack()
        
        # Resumen
        summary_frame = ttk.LabelFrame(results_window, text="📈 Resumen", padding="10")
        summary_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        online_count = sum(1 for _, result in results if result['connected'])
        total_count = len(results)
        
        ttk.Label(summary_frame, text=f"Ubicaciones online: {online_count}/{total_count}").pack(anchor=tk.W)
        
        if online_count > 0:
            response_times = [r['response_time'] for _, r in results if r['connected'] and 'response_time' in r]
            if response_times:
                avg_time = sum(response_times) / len(response_times)
                ttk.Label(summary_frame, text=f"Tiempo promedio: {avg_time:.2f}s").pack(anchor=tk.W)
        
        # Detalles
        details_frame = ttk.LabelFrame(results_window, text="📋 Detalles", padding="10")
        details_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        details_text = tk.Text(details_frame, wrap=tk.WORD, font=('Consolas', 9))
        details_scroll = ttk.Scrollbar(details_frame, orient=tk.VERTICAL, command=details_text.yview)
        details_text.configure(yscrollcommand=details_scroll.set)
        
        details_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        details_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Generar reporte detallado
        report_content = []
        report_content.append("📊 REPORTE DETALLADO DE CONEXIONES\n")
        report_content.append("=" * 50 + "\n\n")
        
        for location_id, result in results:
            status = "✅ ONLINE" if result['connected'] else "❌ OFFLINE"
            report_content.append(f"📍 {location_id.upper()}: {status}\n")
            
            if result['connected']:
                if 'response_time' in result:
                    report_content.append(f"   Tiempo respuesta: {result['response_time']:.2f}s\n")
                if 'method' in result:
                    report_content.append(f"   Método conexión: {result['method']}\n")
                if 'write_access' in result:
                    access = "✅ Lectura/Escritura" if result['write_access'] else "⚠️ Solo lectura"
                    report_content.append(f"   Acceso: {access}\n")
            else:
                error = result.get('error', 'Error desconocido')
                report_content.append(f"   Error: {error}\n")
            
            report_content.append("\n")
        
        details_text.insert(1.0, ''.join(report_content))
        details_text.config(state='disabled')
        
        # Botones
        buttons_frame = ttk.Frame(results_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        def export_results():
            try:
                export_path = filedialog.asksaveasfilename(
                    title="Exportar Resultados",
                    defaultextension=".txt",
                    filetypes=[("Archivos de texto", "*.txt")]
                )
                
                if export_path:
                    with open(export_path, 'w', encoding='utf-8') as f:
                        f.write(''.join(report_content))
                    messagebox.showinfo("Exportar", "Resultados exportados correctamente")
            except Exception as e:
                messagebox.showerror("Error", f"Error exportando: {e}")
        
        ttk.Button(buttons_frame, text="💾 Exportar", command=export_results).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Cerrar", command=results_window.destroy).pack(side=tk.RIGHT, padx=5)
        
        self.update_status("Test de conexiones completado", "success")
    
    def show_detailed_report(self):
        """Mostrar reporte detallado de conectividad"""
        report_window = tk.Toplevel(self.root)
        report_window.title("📊 Reporte Detallado de Conectividad")
        report_window.geometry("800x600")
        report_window.transient(self.root)
        
        # Text widget para el reporte
        report_text = tk.Text(report_window, wrap=tk.WORD, font=('Consolas', 10))
        report_scroll = ttk.Scrollbar(report_window, orient=tk.VERTICAL, command=report_text.yview)
        report_text.configure(yscrollcommand=report_scroll.set)
        
        report_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        report_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
        # Generar reporte
        report = self.generate_connectivity_report()
        report_text.insert(1.0, report)
        report_text.config(state='disabled')
        
        # Botón cerrar
        ttk.Button(report_window, text="Cerrar", command=report_window.destroy).pack(pady=10)
    
    def generate_connectivity_report(self) -> str:
        """Generar reporte detallado de conectividad"""
        report = []
        report.append("="*80)
        report.append("REPORTE DETALLADO DE CONECTIVIDAD")
        report.append("Gestor de Archivos Corporativo v1.0")
        report.append("="*80)
        report.append(f"Generado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Estadísticas generales
        online_count = 0
        total_count = len(getattr(self, 'connectivity_labels', {}))
        
        for location_id, label in getattr(self, 'connectivity_labels', {}).items():
            if hasattr(label, 'cget') and "🟢" in label.cget('text'):
                online_count += 1
        
        report.append("RESUMEN EJECUTIVO:")
        report.append(f"- Total ubicaciones configuradas: {total_count}")
        report.append(f"- Ubicaciones online: {online_count}")
        report.append(f"- Ubicaciones offline: {total_count - online_count}")
        availability = (online_count/total_count)*100 if total_count > 0 else 0
        report.append(f"- Porcentaje disponibilidad: {availability:.1f}%")
        report.append("")
        
        # Detalles por ubicación
        report.append("DETALLES POR UBICACIÓN:")
        report.append("-" * 50)
        
        for location_id in getattr(self, 'connectivity_labels', {}):
            if hasattr(self, 'connectivity_labels'):
                status_text = self.connectivity_labels[location_id].cget('text')
                
                report.append(f"\n{location_id.upper()}:")
                report.append(f"  Estado: {status_text}")
                
                if hasattr(self, 'connectivity_details') and location_id in self.connectivity_details:
                    details_text = self.connectivity_details[location_id].cget('text')
                    report.append(f"  Detalles: {details_text}")
                
                # Información adicional del estado interno
                if hasattr(self, 'connectivity_status') and location_id in self.connectivity_status:
                    status_data = self.connectivity_status[location_id]
                    if status_data.get('connected', False):
                        if 'response_time' in status_data:
                            report.append(f"  Tiempo respuesta: {status_data['response_time']:.2f}s")
                        if 'method' in status_data:
                            report.append(f"  Método conexión: {status_data['method']}")
                        if 'write_access' in status_data:
                            access = "✅ Lectura/Escritura" if status_data['write_access'] else "⚠️ Solo lectura"
                            report.append(f"  Acceso: {access}")
        
        report.append("")
        report.append("="*80)
        
        return "\n".join(report)
    
    def on_closing(self):
        """Manejar cierre de la aplicación"""
        try:
            # Verificar si hay operaciones activas
            if any(self.operaciones_activas.values()):
                active_ops = [op for op, active in self.operaciones_activas.items() if active]
                op_text = ", ".join(active_ops)
                
                if not messagebox.askokcancel("Operaciones Activas", 
                    f"Hay operaciones en ejecución: {op_text}\n\n"
                    f"¿Cerrar la aplicación de todas formas?\n"
                    f"Las operaciones en curso se detendrán."):
                    return
            
            # Detener threads
            self.shutdown_threads = True
            
            # Detener logs en tiempo real si están activos
            if hasattr(self, 'realtime_logs_active'):
                self.realtime_logs_active = False
            
            # Guardar configuración automáticamente
            try:
                if hasattr(self, 'validacion_origen_var') and self.validacion_origen_var.get():
                    self.save_validacion_config()
                if hasattr(self, 'limpieza_archivo_var') and self.limpieza_archivo_var.get():
                    self.save_limpieza_config()
            except:
                pass  # No es crítico si falla el guardado
            
            # Log de cierre
            self.logger.info("=== APLICACIÓN CERRADA POR EL USUARIO ===")
            
            # Cerrar ventana
            self.root.destroy()
            
        except Exception as e:
            self.logger.error(f"Error en cierre de aplicación: {e}")
            # Forzar cierre si hay error
            self.root.destroy()
    
    def run(self):
        """Ejecutar la aplicación principal"""
        try:
            self.logger.info("Iniciando bucle principal de la aplicación")
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
            
        except Exception as e:
            self.logger.error(f"Error en ejecución del dashboard: {e}")
            messagebox.showerror("Error Fatal", 
                               f"Error ejecutando la aplicación:\n{e}\n\n"
                               f"Consulte los logs para más detalles.")
        finally:
            self.logger.info("=== GESTOR DE ARCHIVOS CORPORATIVO FINALIZADO ===")

# === MÉTODOS ESTÁTICOS Y UTILITARIOS ===

def main():
    """Función principal para ejecutar el dashboard"""
    import sys
    
    # Configurar logging básico
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger('dashboard.main')
    
    try:
        logger.info("=== INICIANDO GESTOR DE ARCHIVOS CORPORATIVO ===")
        
        # Verificar dependencias críticas
        try:
            import tkinter
            import threading
            from pathlib import Path
        except ImportError as e:
            logger.error(f"Dependencia faltante: {e}")
            print(f"❌ Error: Dependencia faltante - {e}")
            input("Presione Enter para salir...")
            sys.exit(1)
        
        # Crear y ejecutar dashboard
        dashboard = GestorDashboard()
        dashboard.run()
        
    except KeyboardInterrupt:
        logger.info("Aplicación interrumpida por el usuario (Ctrl+C)")
        print("\n🛑 Aplicación detenida por el usuario")
        
    except Exception as e:
        logger.error(f"Error fatal en main: {e}")
        print(f"❌ Error fatal: {e}")
        
        # Mostrar información de debug si es necesario
        if "--debug" in sys.argv:
            import traceback
            print("\n🔍 Información de debug:")
            traceback.print_exc()
        
        input("Presione Enter para salir...")
        sys.exit(1)

if __name__ == "__main__":
    main()

# FIN PIEZA 5C - DASHBOARD COMPLETO

