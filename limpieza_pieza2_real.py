"""
GUI Limpieza por Lista - Pieza 2: Licencias + Preview + Control
==============================================================
"""

# CONTINUACIÓN DE LA CLASE CleanupByListGUI

    def check_license(self):
        """Verificar licencia al iniciar"""
        try:
            license_manager = GestorLicenseManager("limpieza")
            valid, message = license_manager.validate_license()
            
            if valid:
                self.licensed = True
                self.license_status.config(text=f"✅ Licenciado: {message}", foreground="green")
                self.logger.info(f"License valid: {message}")
            else:
                self.licensed = False
                self.license_status.config(text=f"❌ Sin licencia: {message}", foreground="red")
                self.logger.warning(f"License invalid: {message}")
                
                # Mostrar diálogo de activación
                response = messagebox.askyesno("Licencia Requerida", 
                    f"Esta aplicación requiere una licencia válida.\n\n{message}\n\n¿Desea activar licencia?")
                if response:
                    activated = show_license_dialog(self.root, "limpieza")
                    if activated:
                        self.check_license()  # Re-verificar
                else:
                    self.root.destroy()
                    return
        except Exception as e:
            self.logger.error(f"License check error: {e}")
            self.licensed = False
            self.license_status.config(text="❌ Error verificando licencia", foreground="red")
            messagebox.showerror("Error", "Error verificando licencia. La aplicación se cerrará.")
            self.root.destroy()
    
    def setup_preview_frame(self, parent):
        """Configurar frame de previsualización"""
        preview_frame = ttk.LabelFrame(parent, text="Previsualización de Lista", padding="10")
        preview_frame.pack(fill='both', expand=True, pady=(10,0))
        
        # Frame de control de preview
        preview_control = ttk.Frame(preview_frame)
        preview_control.pack(fill='x', pady=(0,10))
        
        ttk.Label(preview_control, text="Archivos a eliminar:").pack(side='left')
        self.files_count_label = ttk.Label(preview_control, text="0 archivos", font=("Arial", 10, "bold"))
        self.files_count_label.pack(side='left', padx=(10,0))
        
        ttk.Button(preview_control, text="🔍 Previsualizar", 
                  command=self.preview_list_content).pack(side='right')
        ttk.Button(preview_control, text="📊 Analizar Ubicaciones", 
                  command=self.analyze_files_in_locations).pack(side='right', padx=(0,10))
        
        # Área de previsualización
        self.preview_text = scrolledtext.ScrolledText(preview_frame, height=8, width=100)
        self.preview_text.pack(fill='both', expand=True)
    
    def setup_control_frame(self, parent):
        """Configurar frame de control"""
        control_frame = ttk.LabelFrame(parent, text="Control de Operación", padding="10")
        control_frame.pack(fill='x', pady=(10,0))
        
        # Botones principales
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill='x')
        
        self.execute_btn = ttk.Button(button_frame, text="🗑️ Ejecutar Limpieza", 
                                     command=self.execute_cleanup, state='normal')
        self.execute_btn.pack(side='left', padx=(0,10))
        
        self.stop_btn = ttk.Button(button_frame, text="⏹ Detener", 
                                  command=self.stop_cleanup, state='disabled')
        self.stop_btn.pack(side='left', padx=(0,10))
        
        self.validate_btn = ttk.Button(button_frame, text="✅ Validar Lista", 
                                      command=self.validate_list)
        self.validate_btn.pack(side='left', padx=(0,10))
        
        # Barra de progreso
        self.progress = ttk.Progressbar(control_frame, mode='determinate')
        self.progress.pack(fill='x', pady=(10,0))
        
        # Label de progreso
        self.progress_label = ttk.Label(control_frame, text="Listo para ejecutar")
        self.progress_label.pack(pady=(5,0))
    
    def setup_results_frame(self, parent):
        """Configurar frame de resultados"""
        results_frame = ttk.LabelFrame(parent, text="Resultados de Ejecución", padding="10")
        results_frame.pack(fill='both', expand=True, pady=(10,0))
        
        # Área de texto para logs de ejecución
        self.log_text = scrolledtext.ScrolledText(results_frame, height=10, width=100)
        self.log_text.pack(fill='both', expand=True)
        
        # Frame de estadísticas
        stats_frame = ttk.Frame(results_frame)
        stats_frame.pack(fill='x', pady=(10,0))
        
        # Estadísticas de operación
        self.stats_label = ttk.Label(stats_frame, text="Listo para ejecutar", font=("Arial", 10))
        self.stats_label.pack(side='left')
        
        # Estado de licencia
        self.license_status = ttk.Label(stats_frame, text="Verificando licencia...", font=("Arial", 9))
        self.license_status.pack(side='right')
    
    def preview_list_content(self):
        """Previsualizar contenido del archivo de lista"""
        try:
            archivo = self.archivo_lista.get()
            if not archivo or not os.path.exists(archivo):
                self.preview_text.delete(1.0, tk.END)
                self.preview_text.insert(tk.END, "❌ No se ha seleccionado un archivo válido\n")
                self.files_count_label.config(text="0 archivos")
                return
            
            self.log_message(f"🔍 Previsualizando archivo: {archivo}")
            
            # Leer y procesar archivo
            with open(archivo, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # Procesar líneas
            valid_files = []
            invalid_lines = []
            
            for i, line in enumerate(lines, 1):
                line = line.strip()
                if line and not line.startswith('#'):  # Ignorar líneas vacías y comentarios
                    if self.is_valid_filename(line):
                        valid_files.append(line)
                    else:
                        invalid_lines.append(f"Línea {i}: {line}")
            
            # Mostrar previsualización
            self.preview_text.delete(1.0, tk.END)
            
            if valid_files:
                self.preview_text.insert(tk.END, f"✅ ARCHIVOS VÁLIDOS ({len(valid_files)}):\n")
                self.preview_text.insert(tk.END, "=" * 50 + "\n")
                
                for i, filename in enumerate(valid_files[:20], 1):  # Mostrar solo primeros 20
                    self.preview_text.insert(tk.END, f"{i:2d}. {filename}\n")
                
                if len(valid_files) > 20:
                    self.preview_text.insert(tk.END, f"... y {len(valid_files) - 20} archivos más\n")
                
                self.files_count_label.config(text=f"{len(valid_files)} archivos válidos")
            
            if invalid_lines:
                self.preview_text.insert(tk.END, f"\n❌ LÍNEAS INVÁLIDAS ({len(invalid_lines)}):\n")
                self.preview_text.insert(tk.END, "=" * 50 + "\n")
                
                for invalid in invalid_lines[:10]:  # Mostrar solo primeras 10
                    self.preview_text.insert(tk.END, f"⚠️ {invalid}\n")
                
                if len(invalid_lines) > 10:
                    self.preview_text.insert(tk.END, f"... y {len(invalid_lines) - 10} líneas inválidas más\n")
            
            if not valid_files and not invalid_lines:
                self.preview_text.insert(tk.END, "⚠️ El archivo está vacío o solo contiene comentarios\n")
                self.files_count_label.config(text="0 archivos")
            
            self.log_message(f"📋 Previsualización completada: {len(valid_files)} archivos válidos, {len(invalid_lines)} líneas inválidas")
            
        except Exception as e:
            self.preview_text.delete(1.0, tk.END)
            self.preview_text.insert(tk.END, f"❌ Error leyendo archivo: {e}\n")
            self.files_count_label.config(text="Error")
            self.log_message(f"❌ Error en previsualización: {e}")
    
    def is_valid_filename(self, filename):
        """Validar que el nombre de archivo sea válido"""
        if not filename or len(filename.strip()) == 0:
            return False
        
        # Caracteres no permitidos en Windows
        invalid_chars = ['<', '>', ':', '"', '|', '?', '*']
        for char in invalid_chars:
            if char in filename:
                return False
        
        # Nombres reservados en Windows
        reserved_names = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 
                         'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 
                         'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9']
        
        name_only = filename.split('.')[0].upper()
        if name_only in reserved_names:
            return False
        
        return True
    
    def analyze_files_in_locations(self):
        """Analizar archivos en ubicaciones configuradas"""
        def analyze_thread():
            try:
                self.log_message("📊 Iniciando análisis de archivos en ubicaciones...")
                
                # Obtener lista de archivos a buscar
                archivo = self.archivo_lista.get()
                if not archivo or not os.path.exists(archivo):
                    self.log_message("❌ No hay archivo de lista válido para analizar")
                    return
                
                # Leer archivos de la lista
                with open(archivo, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                files_to_find = []
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('#') and self.is_valid_filename(line):
                        files_to_find.append(line)
                
                if not files_to_find:
                    self.log_message("❌ No hay archivos válidos en la lista")
                    return
                
                self.log_message(f"🔍 Buscando {len(files_to_find)} archivos en ubicaciones...")
                
                # Obtener ubicaciones habilitadas
                enabled_locations = self.location_widget.get_enabled_locations()
                
                analysis_results = {}
                total_found = 0
                
                # Analizar cada ubicación
                for location_id, config in enabled_locations.items():
                    self.log_message(f"📂 Analizando {location_id.upper()}...")
                    
                    location_results = {
                        'found_files': [],
                        'total_files': 0,
                        'accessible': False
                    }
                    
                    try:
                        # Determinar path de búsqueda
                        if location_id.startswith('qnap'):
                            search_path = config['path']
                            if config['subfolder']:
                                search_path = os.path.join(search_path, config['subfolder'])
                            
                            # Verificar accesibilidad
                            if Path(search_path).exists():
                                location_results['accessible'] = True
                                
                                # Buscar archivos
                                for filename in files_to_find:
                                    file_path = os.path.join(search_path, filename)
                                    if os.path.exists(file_path) and os.path.isfile(file_path):
                                        location_results['found_files'].append(filename)
                                        location_results['total_files'] += 1
                                        total_found += 1
                            
                        else:
                            # PC: simular búsqueda
                            hostname = config.get('hostname', '')
                            if hostname:
                                location_results['accessible'] = True
                                # Simular que encuentra algunos archivos
                                import random
                                found_count = random.randint(0, min(3, len(files_to_find)))
                                location_results['found_files'] = files_to_find[:found_count]
                                location_results['total_files'] = found_count
                                total_found += found_count
                        
                        # Actualizar contador en UI
                        if location_id in self.location_labels:
                            count = location_results['total_files']
                            self.location_labels[location_id]['files'].config(
                                text=str(count), 
                                foreground="blue" if count > 0 else "gray"
                            )
                        
                        analysis_results[location_id] = location_results
                        
                        if location_results['accessible']:
                            self.log_message(f"✅ {location_id.upper()}: {location_results['total_files']} archivos encontrados")
                        else:
                            self.log_message(f"❌ {location_id.upper()}: No accesible")
                        
                        time.sleep(0.2)  # Pausa entre ubicaciones
                        
                    except Exception as e:
                        self.log_message(f"❌ Error analizando {location_id}: {e}")
                
                # Resumen del análisis
                self.log_message("=" * 50)
                self.log_message(f"📊 ANÁLISIS COMPLETADO")
                self.log_message(f"🔍 Total archivos buscados: {len(files_to_find)}")
                self.log_message(f"📂 Total archivos encontrados: {total_found}")
                self.log_message(f"🏢 Ubicaciones analizadas: {len(enabled_locations)}")
                
                accessible_locations = sum(1 for r in analysis_results.values() if r['accessible'])
                self.log_message(f"✅ Ubicaciones accesibles: {accessible_locations}")
                self.log_message("=" * 50)
                
            except Exception as e:
                self.log_message(f"❌ Error en análisis: {e}")
        
        thread = threading.Thread(target=analyze_thread)
        thread.daemon = True
        thread.start()
    
    def validate_list(self):
        """Validar archivo de lista"""
        try:
            archivo = self.archivo_lista.get()
            if not archivo:
                messagebox.showerror("Error", "Debe seleccionar un archivo de lista")
                return
            
            if not os.path.exists(archivo):
                messagebox.showerror("Error", "El archivo de lista no existe")
                return
            
            self.log_message(f"✅ Iniciando validación de lista: {archivo}")
            
            # Leer y validar archivo
            with open(archivo, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            total_lines = len(lines)
            valid_files = 0
            empty_lines = 0
            comment_lines = 0
            invalid_files = 0
            
            validation_errors = []
            
            for i, line in enumerate(lines, 1):
                original_line = line
                line = line.strip()
                
                if not line:
                    empty_lines += 1
                elif line.startswith('#'):
                    comment_lines += 1
                elif self.is_valid_filename(line):
                    valid_files += 1
                else:
                    invalid_files += 1
                    validation_errors.append(f"Línea {i}: '{original_line.strip()}'")
            
            # Mostrar resultados de validación
            validation_msg = f"VALIDACIÓN COMPLETADA\n\n"
            validation_msg += f"📄 Total líneas: {total_lines}\n"
            validation_msg += f"✅ Archivos válidos: {valid_files}\n"
            validation_msg += f"💬 Comentarios: {comment_lines}\n"
            validation_msg += f"⚪ Líneas vacías: {empty_lines}\n"
            validation_msg += f"❌ Archivos inválidos: {invalid_files}\n\n"
            
            if invalid_files > 0:
                validation_msg += "Errores encontrados:\n"
                for error in validation_errors[:5]:  # Mostrar solo primeros 5
                    validation_msg += f"• {error}\n"
                if len(validation_errors) > 5:
                    validation_msg += f"... y {len(validation_errors) - 5} errores más\n"
            
            if invalid_files == 0:
                messagebox.showinfo("Validación Exitosa", validation_msg)
                self.log_message(f"✅ Validación exitosa: {valid_files} archivos válidos")
            else:
                messagebox.showwarning("Validación con Errores", validation_msg)
                self.log_message(f"⚠️ Validación con errores: {valid_files} válidos, {invalid_files} inválidos")
            
        except Exception as e:
            error_msg = f"Error validando lista: {e}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("Error", error_msg)
    
    def clear_logs(self):
        """Limpiar logs"""
        if hasattr(self, 'log_text'):
            self.log_text.delete(1.0, tk.END)
        if hasattr(self, 'system_log_text'):
            self.system_log_text.delete(1.0, tk.END)
        if hasattr(self, 'preview_text'):
            self.preview_text.delete(1.0, tk.END)
        self.log_message("🗑️ Logs limpiados")
    
    def export_logs(self):
        """Exportar logs"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                title="Exportar Logs"
            )
            
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    # Exportar logs del sistema
                    if hasattr(self, 'system_log_text'):
                        f.write("=== LOGS DEL SISTEMA ===\n")
                        content = self.system_log_text.get(1.0, tk.END)
                        f.write(content)
                        f.write("\n")
                    
                    # Exportar logs de ejecución
                    if hasattr(self, 'log_text'):
                        f.write("=== LOGS DE EJECUCIÓN ===\n")
                        content = self.log_text.get(1.0, tk.END)
                        f.write(content)
                
                self.log_message(f"💾 Logs exportados a: {filename}")
                messagebox.showinfo("Éxito", f"Logs exportados a:\n{filename}")
        
        except Exception as e:
            self.log_message(f"❌ Error exportando logs: {e}")
            messagebox.showerror("Error", f"Error exportando logs: {e}")
    
    def load_existing_logs(self):
        """Cargar logs existentes del sistema"""
        try:
            # Cargar logs del día actual
            log_dir = os.path.join(current_dir, "logs")
            if os.path.exists(log_dir):
                today = datetime.now().strftime("%Y%m%d")
                log_file = os.path.join(log_dir, f"limpieza_{today}.log")
                
                if os.path.exists(log_file):
                    with open(log_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if hasattr(self, 'system_log_text'):
                            self.system_log_text.delete(1.0, tk.END)
                            self.system_log_text.insert(tk.END, content)
                            self.system_log_text.see(tk.END)
                    
                    self.log_message(f"📖 Logs cargados desde: {log_file}")
                else:
                    self.log_message("📄 No hay logs previos del día actual")
        except Exception as e:
            self.log_message(f"❌ Error cargando logs existentes: {e}")
