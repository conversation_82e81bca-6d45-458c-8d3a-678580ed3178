"""
Gestor de Archivos Corporativo - Base Connection
==============================================

Clase base para manejo de conexiones híbridas con failover automático
"""

import os
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
from enum import Enum
import logging

# Import con fallback para evitar errores de path
try:
    from .config_manager import config
    from .auth_manager import auth_manager
    from .utils import is_network_path, is_valid_path, get_free_space, format_file_size
except ImportError:
    # Fallback para cuando se ejecuta directamente
    try:
        from core.config_manager import config
        from core.auth_manager import auth_manager
        from core.utils import is_network_path, is_valid_path, get_free_space, format_file_size
    except ImportError:
        # Mock para testing básico
        class MockConfig:
            def get(self, key, default=None): return default
            def get_enabled_locations(self): return {}
            def get_credentials(self, loc): return {'username': None}
        
        class MockAuth:
            def is_authenticated(self, loc): return False
            def authenticate_location(self, loc): return False, "Mock"
            def disconnect_location(self, loc): pass
        
        config = MockConfig()
        auth_manager = MockAuth()
        
        def is_network_path(path): return '\\\\' in str(path)
        def is_valid_path(path): return True
        def get_free_space(path): return 1024*1024*1024
        def format_file_size(size): return f"{size/1024/1024:.1f} MB"

class ConnectionMethod(Enum):
    """Métodos de conexión disponibles"""
    SYMLINK = "symlink"
    NETWORK = "network"
    UNKNOWN = "unknown"

class ConnectionStatus(Enum):
    """Estados de conexión"""
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    FAILED = "failed"
    AUTHENTICATING = "authenticating"
    TESTING = "testing"

class BaseConnection:
    """Clase base para manejo de conexiones a ubicaciones remotas"""
    
    def __init__(self, location_id: str):
        self.location_id = location_id
        self.logger = logging.getLogger('gestor.conectividad')
        
        # Cargar configuración de la ubicación
        self.config = config.get(f'locations.{location_id}')
        if not self.config:
            raise ValueError(f"Ubicación {location_id} no encontrada en configuración")
        
        # Propiedades de conexión
        self.current_method: Optional[ConnectionMethod] = None
        self.current_path: Optional[Path] = None
        self.status = ConnectionStatus.DISCONNECTED
        self.last_test_time: Optional[float] = None
        self.last_test_result: Optional[Dict] = None
        
        # Configuración de timeouts
        self.connection_timeout = config.get('connection_settings.timeout_seconds', 30)
        self.retry_attempts = config.get('connection_settings.retry_attempts', 3)
        
        self.logger.info(f"Inicializada conexión base para {location_id}")
    
    def connect(self) -> Tuple[bool, str]:
        """
        Conecta a la ubicación usando el mejor método disponible
        
        Returns:
            Tuple[bool, str]: (éxito, mensaje)
        """
        if not self.config.get('enabled', False):
            return False, f"Ubicación {self.location_id} está deshabilitada"
        
        self.logger.info(f"Iniciando conexión a {self.location_id}")
        self.status = ConnectionStatus.AUTHENTICATING
        
        # Obtener prioridad de métodos de conexión
        connection_priority = self.config.get('connection_priority', ['symlink', 'network'])
        
        for method_name in connection_priority:
            method = ConnectionMethod(method_name)
            
            self.logger.debug(f"Intentando método {method.value} para {self.location_id}")
            
            success, message, path = self._try_connection_method(method)
            
            if success:
                self.current_method = method
                self.current_path = path
                self.status = ConnectionStatus.CONNECTED
                
                self.logger.info(f"Conexión exitosa: {self.location_id} via {method.value}")
                return True, f"Conectado via {method.value}: {path}"
        
        # Todos los métodos fallaron
        self.status = ConnectionStatus.FAILED
        self.logger.error(f"Falló conexión a {self.location_id}")
        return False, f"No se pudo conectar a {self.location_id} con ningún método"
    
    def _try_connection_method(self, method: ConnectionMethod) -> Tuple[bool, str, Optional[Path]]:
        """Intenta un método específico de conexión"""
        try:
            if method == ConnectionMethod.SYMLINK:
                return self._try_symlink_connection()
            elif method == ConnectionMethod.NETWORK:
                return self._try_network_connection()
            else:
                return False, f"Método {method.value} no soportado", None
                
        except Exception as e:
            self.logger.error(f"Error en método {method.value} para {self.location_id}: {e}")
            return False, f"Error {method.value}: {e}", None
    
    def _try_symlink_connection(self) -> Tuple[bool, str, Optional[Path]]:
        """Intenta conexión via carpeta simbólica"""
        symlink_path = self.config.get('symlink_path')
        if not symlink_path:
            return False, "No hay symlink_path configurado", None
        
        path_obj = Path(symlink_path)
        
        # Verificar si la carpeta simbólica existe y es accesible
        if path_obj.exists() and path_obj.is_dir():
            try:
                # Test básico de acceso
                list(path_obj.iterdir())
                return True, f"Carpeta simbólica accesible: {symlink_path}", path_obj
            except PermissionError:
                return False, f"Sin permisos para acceder a {symlink_path}", None
            except Exception as e:
                return False, f"Error accediendo carpeta simbólica: {e}", None
        else:
            return False, f"Carpeta simbólica no existe: {symlink_path}", None
    
    def _try_network_connection(self) -> Tuple[bool, str, Optional[Path]]:
        """Intenta conexión via ruta de red"""
        network_path = self.config.get('network_path')
        if not network_path:
            return False, "No hay network_path configurado", None
        
        # Intentar autenticación si es necesario
        credentials = config.get_credentials(self.location_id)
        if credentials['username']:
            if not auth_manager.is_authenticated(self.location_id):
                auth_success, auth_message = auth_manager.authenticate_location(self.location_id)
                if not auth_success:
                    return False, f"Fallo autenticación: {auth_message}", None
        
        # Verificar acceso a la ruta de red
        path_obj = Path(network_path)
        
        try:
            if path_obj.exists() and path_obj.is_dir():
                # Test básico de acceso
                list(path_obj.iterdir())
                return True, f"Ruta de red accesible: {network_path}", path_obj
            else:
                return False, f"Ruta de red no accesible: {network_path}", None
                
        except PermissionError:
            return False, f"Sin permisos para ruta de red: {network_path}", None
        except Exception as e:
            return False, f"Error accediendo ruta de red: {e}", None
    
    def disconnect(self) -> Tuple[bool, str]:
        """Desconecta de la ubicación"""
        if self.status == ConnectionStatus.DISCONNECTED:
            return True, f"Ya desconectado de {self.location_id}"
        
        self.logger.info(f"Desconectando de {self.location_id}")
        
        # Si usamos red y hay autenticación, desconectar
        if (self.current_method == ConnectionMethod.NETWORK and 
            auth_manager.is_authenticated(self.location_id)):
            auth_manager.disconnect_location(self.location_id)
        
        # Limpiar estado
        self.current_method = None
        self.current_path = None
        self.status = ConnectionStatus.DISCONNECTED
        
        self.logger.info(f"Desconectado de {self.location_id}")
        return True, f"Desconectado de {self.location_id}"
    
    def test_connection(self) -> Dict:
        """
        Prueba la conexión actual y retorna información detallada
        
        Returns:
            Dict: Información detallada del test
        """
        self.status = ConnectionStatus.TESTING
        self.last_test_time = time.time()
        
        test_result = {
            'location_id': self.location_id,
            'timestamp': self.last_test_time,
            'success': False,
            'current_method': self.current_method.value if self.current_method else None,
            'current_path': str(self.current_path) if self.current_path else None,
            'methods_tested': {},
            'total_test_time': 0,
            'error_details': None
        }
        
        start_time = time.time()
        
        try:
            if self.status == ConnectionStatus.CONNECTED and self.current_path:
                # Test conexión actual
                test_result['success'] = self._test_current_connection()
            else:
                # Test todos los métodos disponibles
                connection_priority = self.config.get('connection_priority', ['symlink', 'network'])
                
                for method_name in connection_priority:
                    method = ConnectionMethod(method_name)
                    method_start = time.time()
                    
                    success, message, path = self._try_connection_method(method)
                    method_time = time.time() - method_start
                    
                    test_result['methods_tested'][method_name] = {
                        'success': success,
                        'message': message,
                        'path': str(path) if path else None,
                        'test_time': method_time
                    }
                    
                    if success and not test_result['success']:
                        test_result['success'] = True
                        test_result['best_method'] = method_name
                        test_result['best_path'] = str(path)
            
            # Información adicional si hay conexión exitosa
            if test_result['success'] and self.current_path:
                test_result.update(self._get_location_info())
            
        except Exception as e:
            test_result['error_details'] = str(e)
            self.logger.error(f"Error en test de conexión para {self.location_id}: {e}")
        
        finally:
            test_result['total_test_time'] = time.time() - start_time
            self.last_test_result = test_result
            
            # Restaurar estado
            if test_result['success']:
                self.status = ConnectionStatus.CONNECTED
            else:
                self.status = ConnectionStatus.FAILED
        
        return test_result
    
    def _test_current_connection(self) -> bool:
        """Prueba la conexión actual"""
        if not self.current_path:
            return False
        
        try:
            # Verificar que la ruta sigue siendo accesible
            if not self.current_path.exists():
                return False
            
            # Test básico de lectura
            list(self.current_path.iterdir())
            return True
            
        except Exception as e:
            self.logger.debug(f"Test conexión actual falló para {self.location_id}: {e}")
            return False
    
    def _get_location_info(self) -> Dict:
        """Obtiene información detallada de la ubicación"""
        info = {}
        
        if self.current_path and self.current_path.exists():
            try:
                # Espacio libre
                free_space = get_free_space(self.current_path)
                if free_space:
                    info['free_space_bytes'] = free_space
                    info['free_space_formatted'] = format_file_size(free_space)
                
                # Conteo básico de archivos
                files_count = sum(1 for item in self.current_path.iterdir() if item.is_file())
                dirs_count = sum(1 for item in self.current_path.iterdir() if item.is_dir())
                
                info['files_count'] = files_count
                info['directories_count'] = dirs_count
                info['total_items'] = files_count + dirs_count
                
            except Exception as e:
                info['info_error'] = str(e)
        
        return info
    
    def get_current_path(self) -> Optional[Path]:
        """Obtiene la ruta actual de conexión"""
        return self.current_path
    
    def is_connected(self) -> bool:
        """Verifica si está conectado"""
        return self.status == ConnectionStatus.CONNECTED
    
    def get_status(self) -> Dict:
        """Obtiene estado completo de la conexión"""
        return {
            'location_id': self.location_id,
            'enabled': self.config.get('enabled', False),
            'status': self.status.value,
            'current_method': self.current_method.value if self.current_method else None,
            'current_path': str(self.current_path) if self.current_path else None,
            'last_test_time': self.last_test_time,
            'last_test_success': self.last_test_result.get('success') if self.last_test_result else None,
            'available_methods': {
                'symlink': bool(self.config.get('symlink_path')),
                'network': bool(self.config.get('network_path'))
            },
            'connection_priority': self.config.get('connection_priority', [])
        }
    
    def auto_reconnect(self) -> Tuple[bool, str]:
        """Intenta reconectar automáticamente"""
        self.logger.info(f"Reconexión automática para {self.location_id}")
        
        # Desconectar primero
        self.disconnect()
        
        # Esperar un momento
        time.sleep(1)
        
        # Intentar reconectar
        return self.connect()

if __name__ == "__main__":
    # Test básico de conexiones
    print("🔗 Base Connection Test")
    print("=" * 30)
    
    # Test con primera ubicación habilitada
    locations = config.get_enabled_locations()
    if locations:
        first_location = list(locations.keys())[0]
        print(f"Testing connection to: {first_location}")
        
        try:
            # Crear conexión
            connection = BaseConnection(first_location)
            
            # Obtener estado inicial
            status = connection.get_status()
            print(f"  Status: {status['status']}")
            print(f"  Available methods: {status['available_methods']}")
            
            # Test de conexión
            print(f"\n🔍 Testing connection...")
            test_result = connection.test_connection()
            
            print(f"  Success: {test_result['success']}")
            print(f"  Test time: {test_result['total_test_time']:.2f}s")
            
            if test_result.get('methods_tested'):
                print("  Methods tested:")
                for method, result in test_result['methods_tested'].items():
                    status = "✅" if result['success'] else "❌"
                    print(f"    {status} {method}: {result['message']}")
            
            # Si hay conexión exitosa, mostrar info
            if test_result['success']:
                if test_result.get('free_space_formatted'):
                    print(f"  Free space: {test_result['free_space_formatted']}")
                if test_result.get('total_items'):
                    print(f"  Items: {test_result['total_items']}")
            
        except Exception as e:
            print(f"  ❌ Error: {e}")
    else:
        print("No hay ubicaciones habilitadas para probar")
