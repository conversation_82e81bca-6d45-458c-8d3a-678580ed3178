"""
Test Sesión 2 - Sistema de Conexiones Base
=========================================

Verifica que el sistema de conexiones y autenticación funcionen correctamente
"""

import sys
from pathlib import Path
import time

# Agregar src al path para imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_auth_manager():
    """Test del gestor de autenticación"""
    print("🔐 Testing Authentication Manager...")
    
    try:
        from core.auth_manager import AuthenticationManager, auth_manager
        from core.config_manager import config
        
        # Verificar inicialización
        print(f"  ✓ Auth Manager inicializado")
        
        # Obtener ubicaciones configuradas
        locations = config.get_enabled_locations()
        print(f"  ✓ Ubicaciones disponibles: {len(locations)}")
        
        # Test de autenticación (simulado)
        test_location = list(locations.keys())[0] if locations else 'qnap1'
        
        print(f"\n  🔍 Testing authentication for: {test_location}")
        
        # Test básico de autenticación (sin credenciales reales)
        success, message, info = auth_manager.test_authentication(test_location)
        status = "✅" if success else "⚠️"
        print(f"    {status} Auth test: {message}")
        
        if info.get('response_time_ms'):
            print(f"    📡 Response time: {info['response_time_ms']:.1f}ms")
        
        # Test de lista de ubicaciones autenticadas
        authenticated = auth_manager.get_authenticated_locations()
        print(f"  ✓ Authenticated locations: {len(authenticated)}")
        
        # Test de información de sesión
        if authenticated:
            session_info = auth_manager.get_session_info(authenticated[0])
            if session_info:
                print(f"    Session age: {session_info['age_seconds']:.1f}s")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Auth Manager Error: {e}")
        return False

def test_base_connection():
    """Test de conexiones base"""
    print("\n🔗 Testing Base Connection...")
    
    try:
        from core.base_connection import BaseConnection, ConnectionStatus, ConnectionMethod
        from core.config_manager import config
        
        # Obtener ubicaciones para test
        locations = config.get_enabled_locations()
        if not locations:
            print("  ⚠️  No hay ubicaciones habilitadas para probar")
            return True
        
        test_location = list(locations.keys())[0]
        print(f"  🔍 Testing connection to: {test_location}")
        
        # Crear conexión
        connection = BaseConnection(test_location)
        print(f"  ✓ Connection object created")
        
        # Obtener estado inicial
        status = connection.get_status()
        print(f"  ✓ Initial status: {status['status']}")
        print(f"  ✓ Available methods: {status['available_methods']}")
        
        # Test de conexión
        print(f"\n  🔍 Testing connection methods...")
        test_result = connection.test_connection()
        
        success_status = "✅" if test_result['success'] else "❌"
        print(f"  {success_status} Connection test: {test_result['success']}")
        print(f"  ⏱️  Test time: {test_result['total_test_time']:.2f}s")
        
        # Mostrar resultados por método
        if test_result.get('methods_tested'):
            print("    Methods tested:")
            for method, result in test_result['methods_tested'].items():
                method_status = "✅" if result['success'] else "❌"
                print(f"      {method_status} {method}: {result['message']}")
                if result.get('test_time'):
                    print(f"        Time: {result['test_time']:.2f}s")
        
        # Información adicional si hay conexión exitosa
        if test_result['success']:
            if test_result.get('free_space_formatted'):
                print(f"    📁 Free space: {test_result['free_space_formatted']}")
            if test_result.get('total_items') is not None:
                print(f"    📄 Total items: {test_result['total_items']}")
        
        # Test de estado de conexión
        final_status = connection.get_status()
        print(f"  ✓ Final status: {final_status['status']}")
        print(f"  ✓ Is connected: {connection.is_connected()}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Base Connection Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_connection_integration():
    """Test de integración entre conexiones y autenticación"""
    print("\n🔗🔐 Testing Connection Integration...")
    
    try:
        from core.base_connection import BaseConnection
        from core.auth_manager import auth_manager
        from core.config_manager import config
        
        locations = config.get_enabled_locations()
        if not locations:
            print("  ⚠️  No hay ubicaciones para test de integración")
            return True
        
        # Probar con las primeras 2 ubicaciones
        test_locations = list(locations.keys())[:2]
        print(f"  🔍 Testing integration with: {test_locations}")
        
        connection_results = {}
        
        for location_id in test_locations:
            print(f"\n    Testing {location_id}...")
            
            try:
                # Crear conexión
                connection = BaseConnection(location_id)
                
                # Test de conexión
                test_result = connection.test_connection()
                
                connection_results[location_id] = {
                    'success': test_result['success'],
                    'method': test_result.get('best_method'),
                    'test_time': test_result['total_test_time']
                }
                
                status = "✅" if test_result['success'] else "❌"
                print(f"      {status} Connection: {test_result['success']}")
                
                if test_result['success']:
                    print(f"      📡 Best method: {test_result.get('best_method', 'unknown')}")
                
            except Exception as e:
                print(f"      ❌ Error with {location_id}: {e}")
                connection_results[location_id] = {'success': False, 'error': str(e)}
        
        # Resumen de integración
        successful_connections = sum(1 for result in connection_results.values() 
                                   if result.get('success', False))
        
        print(f"\n  📊 Integration Summary:")
        print(f"    Successful connections: {successful_connections}/{len(test_locations)}")
        
        # Mostrar ubicaciones autenticadas
        authenticated = auth_manager.get_authenticated_locations()
        print(f"    Authenticated sessions: {len(authenticated)}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Integration Error: {e}")
        return False

def test_connection_performance():
    """Test de rendimiento de conexiones"""
    print("\n⚡ Testing Connection Performance...")
    
    try:
        from core.base_connection import BaseConnection
        from core.config_manager import config
        
        locations = config.get_enabled_locations()
        if not locations:
            print("  ⚠️  No hay ubicaciones para test de rendimiento")
            return True
        
        test_location = list(locations.keys())[0]
        print(f"  🔍 Performance test with: {test_location}")
        
        # Múltiples tests de conexión
        test_times = []
        success_count = 0
        
        for i in range(3):
            print(f"    Test #{i+1}...")
            
            connection = BaseConnection(test_location)
            
            start_time = time.time()
            test_result = connection.test_connection()
            test_time = time.time() - start_time
            
            test_times.append(test_time)
            if test_result['success']:
                success_count += 1
            
            # Limpiar entre tests
            connection.disconnect()
            time.sleep(0.5)
        
        # Estadísticas de rendimiento
        avg_time = sum(test_times) / len(test_times)
        min_time = min(test_times)
        max_time = max(test_times)
        
        print(f"  📊 Performance Results:")
        print(f"    Success rate: {success_count}/3 ({success_count/3*100:.1f}%)")
        print(f"    Average time: {avg_time:.2f}s")
        print(f"    Min time: {min_time:.2f}s")
        print(f"    Max time: {max_time:.2f}s")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Performance Test Error: {e}")
        return False

def main():
    """Ejecuta todos los tests de la Sesión 2"""
    print("🚀 SESIÓN 2 - TEST DE SISTEMA DE CONEXIONES")
    print("=" * 55)
    
    tests = [
        ("Authentication Manager", test_auth_manager),
        ("Base Connection", test_base_connection),
        ("Connection Integration", test_connection_integration),
        ("Connection Performance", test_connection_performance)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  💥 {test_name} CRASHED: {e}")
            results.append((test_name, False))
    
    # Resumen final
    print("\n" + "=" * 60)
    print("📊 RESUMEN DE TESTS - SESIÓN 2")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResultado: {passed}/{len(results)} tests pasaron")
    
    if passed == len(results):
        print("\n🎉 ¡SESIÓN 2 COMPLETADA EXITOSAMENTE!")
        print("✅ Sistema de conexiones base funcional")
        print("✅ Autenticación independiente por ubicación")
        print("✅ Failover automático entre métodos")
        print("✅ Health checks y monitoreo de conexiones")
        print("\n🔄 Listo para Sesión 3: Handlers específicos (Qnap + PC)")
    else:
        print("\n⚠️  Algunos tests fallaron - revisar configuración de red")
        print("💡 Nota: Es normal que fallen si no hay ubicaciones reales configuradas")

if __name__ == "__main__":
    main()
