"""
dashboard.py - VERSIÓN MÍNIMA FUNCIONAL
Dashboard principal del Gestor de Archivos Corporativo
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
from pathlib import Path
import logging
from typing import Dict, Any

class GestorDashboard:
    """Dashboard principal con interfaz por pestañas"""
    
    def __init__(self):
        """Inicializar dashboard"""
        self.setup_logging()
        self.setup_ui()  # Crear ventana principal PRIMERO
        self.setup_variables()  # Variables DESPUÉS de la ventana
        self.create_tabs()  # Crear pestañas AL FINAL
        self.logger.info("Dashboard inicializado correctamente")
    
    def setup_logging(self):
        """Configurar sistema de logging"""
        try:
            from core.logger import gestor_logger
            self.logger = gestor_logger.get_logger('sistema')
        except ImportError:
            logging.basicConfig(level=logging.INFO)
            self.logger = logging.getLogger('dashboard')
    
    def setup_variables(self):
        """Configurar variables de estado"""
        self.operaciones_activas = {'validacion': False, 'limpieza': False}
        
        # Variables de configuración
        self.validacion_origen_var = tk.StringVar()
        self.validacion_rechazados_var = tk.StringVar() 
        self.validacion_dry_run_var = tk.BooleanVar(value=True)
        
        self.limpieza_archivo_var = tk.StringVar()
        self.limpieza_dry_run_var = tk.BooleanVar(value=True)
        
        # Estado
        self.connectivity_status = {}
        self.log_category_var = tk.StringVar(value='sistema')
    
    def setup_ui(self):
        """Configurar interfaz de usuario"""
        # Ventana principal - PRIMERO
        self.root = tk.Tk()
        self.root.title("Gestor de Archivos Corporativo v1.0")
        self.root.geometry("1000x700")
        
        # Solo crear la estructura básica, las pestañas se crean después
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Barra de estado
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=10, pady=5)
        
        self.status_label = ttk.Label(self.status_frame, text="Sistema iniciado - Listo")
        self.status_label.pack(side=tk.LEFT)
    
    def create_tabs(self):
        """Crear todas las pestañas (llamado después de setup_variables)"""
        self.create_dashboard_tab()
        self.create_validacion_tab()
        self.create_limpieza_tab()
        self.create_logs_tab()
    
    def create_dashboard_tab(self):
        """Crear pestaña de dashboard principal"""
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="📊 Dashboard")
        
        ttk.Label(dashboard_frame, text="Estado del Sistema", 
                 font=('Arial', 11, 'bold')).pack(pady=10)
        
        # Frame de conectividad simplificado
        connectivity_frame = ttk.LabelFrame(dashboard_frame, text="Estado de Conectividad", padding="10")
        connectivity_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Lista de ubicaciones
        self.connectivity_labels = {}
        locations = ['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4']
        
        for location in locations:
            frame = ttk.Frame(connectivity_frame)
            frame.pack(fill=tk.X, pady=2)
            
            ttk.Label(frame, text=f"{location.upper()}:").pack(side=tk.LEFT)
            status_label = ttk.Label(frame, text="🔄 Verificando...")
            status_label.pack(side=tk.LEFT, padx=10)
            
            self.connectivity_labels[location] = status_label
        
        # Botón de test
        ttk.Button(connectivity_frame, text="Test Conexiones", 
                  command=self.test_connections).pack(pady=10)
        
        # Info del sistema
        system_frame = ttk.LabelFrame(dashboard_frame, text="Información", padding="10")
        system_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.system_info_text = tk.Text(system_frame, height=8, wrap=tk.WORD)
        self.system_info_text.pack(fill=tk.BOTH, expand=True)
        
        self.update_system_info()
    
    def create_validacion_tab(self):
        """Crear pestaña de validación"""
        validacion_frame = ttk.Frame(self.notebook)
        self.notebook.add(validacion_frame, text="🔍 Validación")
        
        ttk.Label(validacion_frame, text="Validación de Duplicados", 
                 font=('Arial', 11, 'bold')).pack(pady=10)
        
        # Configuración
        config_frame = ttk.LabelFrame(validacion_frame, text="Configuración", padding="10")
        config_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Carpeta origen
        ttk.Label(config_frame, text="Carpeta Origen:").pack(anchor=tk.W)
        origen_frame = ttk.Frame(config_frame)
        origen_frame.pack(fill=tk.X, pady=2)
        
        ttk.Entry(origen_frame, textvariable=self.validacion_origen_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(origen_frame, text="...", width=3, command=self.select_origen).pack(side=tk.RIGHT)
        
        # Carpeta rechazados
        ttk.Label(config_frame, text="Carpeta Rechazados:").pack(anchor=tk.W, pady=(10,0))
        rechazados_frame = ttk.Frame(config_frame)
        rechazados_frame.pack(fill=tk.X, pady=2)
        
        ttk.Entry(rechazados_frame, textvariable=self.validacion_rechazados_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(rechazados_frame, text="...", width=3, command=self.select_rechazados).pack(side=tk.RIGHT)
        
        # Opciones
        ttk.Checkbutton(config_frame, text="Modo Simulación (Dry Run)", 
                       variable=self.validacion_dry_run_var).pack(anchor=tk.W, pady=10)
        
        # Ejecución
        execution_frame = ttk.LabelFrame(validacion_frame, text="Ejecución", padding="10")
        execution_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.validacion_execute_btn = ttk.Button(execution_frame, text="🚀 Ejecutar Validación", 
                                               command=self.execute_validacion)
        self.validacion_execute_btn.pack(side=tk.LEFT, padx=5)
        
        # Resultados
        results_frame = ttk.LabelFrame(validacion_frame, text="Resultados", padding="10")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.validacion_results_text = tk.Text(results_frame, wrap=tk.WORD)
        self.validacion_results_text.pack(fill=tk.BOTH, expand=True)
    
    def create_limpieza_tab(self):
        """Crear pestaña de limpieza"""
        limpieza_frame = ttk.Frame(self.notebook)
        self.notebook.add(limpieza_frame, text="🗑️ Limpieza")
        
        ttk.Label(limpieza_frame, text="Limpieza por Lista", 
                 font=('Arial', 11, 'bold')).pack(pady=10)
        
        # Configuración
        config_frame = ttk.LabelFrame(limpieza_frame, text="Configuración", padding="10")
        config_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Archivo de lista
        ttk.Label(config_frame, text="Archivo de Lista:").pack(anchor=tk.W)
        archivo_frame = ttk.Frame(config_frame)
        archivo_frame.pack(fill=tk.X, pady=2)
        
        ttk.Entry(archivo_frame, textvariable=self.limpieza_archivo_var).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(archivo_frame, text="...", width=3, command=self.select_archivo).pack(side=tk.RIGHT)
        
        # Opciones
        ttk.Checkbutton(config_frame, text="Modo Simulación (Dry Run)", 
                       variable=self.limpieza_dry_run_var).pack(anchor=tk.W, pady=10)
        
        # Ejecución
        execution_frame = ttk.LabelFrame(limpieza_frame, text="Ejecución", padding="10")
        execution_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.limpieza_execute_btn = ttk.Button(execution_frame, text="🚀 Ejecutar Limpieza", 
                                             command=self.execute_limpieza)
        self.limpieza_execute_btn.pack(side=tk.LEFT, padx=5)
        
        # Resultados
        results_frame = ttk.LabelFrame(limpieza_frame, text="Resultados", padding="10")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.limpieza_results_text = tk.Text(results_frame, wrap=tk.WORD)
        self.limpieza_results_text.pack(fill=tk.BOTH, expand=True)
    
    def create_logs_tab(self):
        """Crear pestaña de logs"""
        logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(logs_frame, text="📝 Logs")
        
        ttk.Label(logs_frame, text="Logs del Sistema", 
                 font=('Arial', 11, 'bold')).pack(pady=10)
        
        # Controles
        controls_frame = ttk.Frame(logs_frame)
        controls_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(controls_frame, text="Categoría:").pack(side=tk.LEFT, padx=5)
        
        category_combo = ttk.Combobox(controls_frame, textvariable=self.log_category_var,
                                     values=['sistema', 'limpieza', 'validacion', 'conectividad'],
                                     state='readonly', width=15)
        category_combo.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(controls_frame, text="Actualizar", command=self.refresh_logs).pack(side=tk.LEFT, padx=5)
        
        # Logs
        self.logs_text = tk.Text(logs_frame, wrap=tk.WORD, font=('Consolas', 9))
        self.logs_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.refresh_logs()
    
    # === MÉTODOS DE ACCIÓN ===
    
    def test_connections(self):
        """Test básico de conexiones"""
        for location_id, label in self.connectivity_labels.items():
            try:
                # Test simulado
                label.config(text="🟢 Online", foreground='green')
                self.connectivity_status[location_id] = {'connected': True}
            except:
                label.config(text="🔴 Offline", foreground='red')
                self.connectivity_status[location_id] = {'connected': False}
        
        self.update_system_info()
    
    def update_system_info(self):
        """Actualizar información del sistema"""
        info = []
        info.append("=== INFORMACIÓN DEL SISTEMA ===\n")
        info.append(f"Aplicación: Gestor de Archivos Corporativo v1.0")
        info.append(f"Estado: Funcionando correctamente")
        info.append("")
        
        info.append("=== ESTADO DE UBICACIONES ===")
        online_count = 0
        for location_id, status in self.connectivity_status.items():
            if status.get('connected', False):
                info.append(f"🟢 {location_id.upper()}: ONLINE")
                online_count += 1
            else:
                info.append(f"🔴 {location_id.upper()}: OFFLINE")
        
        info.append(f"\nResumen: {online_count}/{len(self.connectivity_labels)} ubicaciones online")
        
        self.system_info_text.delete(1.0, tk.END)
        self.system_info_text.insert(1.0, '\n'.join(info))
    
    def select_origen(self):
        """Seleccionar carpeta origen"""
        folder = filedialog.askdirectory(title="Seleccionar Carpeta Origen")
        if folder:
            self.validacion_origen_var.set(folder)
    
    def select_rechazados(self):
        """Seleccionar carpeta rechazados"""
        folder = filedialog.askdirectory(title="Seleccionar Carpeta Rechazados")
        if folder:
            self.validacion_rechazados_var.set(folder)
    
    def select_archivo(self):
        """Seleccionar archivo de lista"""
        file_path = filedialog.askopenfilename(
            title="Seleccionar Archivo de Lista",
            filetypes=[("Archivos de texto", "*.txt"), ("Todos los archivos", "*.*")]
        )
        if file_path:
            self.limpieza_archivo_var.set(file_path)
    
    def execute_validacion(self):
        """Ejecutar validación"""
        origen = self.validacion_origen_var.get().strip()
        rechazados = self.validacion_rechazados_var.get().strip()
        
        if not origen or not rechazados:
            messagebox.showwarning("Configuración Incompleta", 
                                 "Configure las carpetas origen y rechazados")
            return
        
        dry_run = self.validacion_dry_run_var.get()
        mode_text = "SIMULACIÓN" if dry_run else "REAL"
        
        self.validacion_results_text.delete(1.0, tk.END)
        self.validacion_results_text.insert(1.0, f"🚀 Iniciando validación en modo {mode_text}...\n")
        
        # Simulación básica
        self.validacion_results_text.insert(tk.END, f"📁 Carpeta origen: {origen}\n")
        self.validacion_results_text.insert(tk.END, f"🗂️ Carpeta rechazados: {rechazados}\n")
        self.validacion_results_text.insert(tk.END, "✅ Validación completada (simulada)\n")
        
        messagebox.showinfo("Validación", "Operación completada exitosamente")
    
    def execute_limpieza(self):
        """Ejecutar limpieza"""
        archivo = self.limpieza_archivo_var.get().strip()
        
        if not archivo:
            messagebox.showwarning("Configuración Incompleta", "Seleccione un archivo de lista")
            return
        
        dry_run = self.limpieza_dry_run_var.get()
        mode_text = "SIMULACIÓN" if dry_run else "REAL"
        
        self.limpieza_results_text.delete(1.0, tk.END)
        self.limpieza_results_text.insert(1.0, f"🚀 Iniciando limpieza en modo {mode_text}...\n")
        
        # Simulación básica
        self.limpieza_results_text.insert(tk.END, f"📄 Archivo de lista: {archivo}\n")
        self.limpieza_results_text.insert(tk.END, "✅ Limpieza completada (simulada)\n")
        
        messagebox.showinfo("Limpieza", "Operación completada exitosamente")
    
    def refresh_logs(self):
        """Actualizar logs"""
        try:
            from core.logger import gestor_logger
            
            category = self.log_category_var.get()
            log_files = gestor_logger.get_log_files()
            
            self.logs_text.delete(1.0, tk.END)
            
            if category in log_files:
                log_file = log_files[category]
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        recent_lines = lines[-50:] if len(lines) > 50 else lines
                        for line in recent_lines:
                            self.logs_text.insert(tk.END, line)
                except Exception as e:
                    self.logs_text.insert(tk.END, f"Error leyendo log: {e}\n")
            else:
                self.logs_text.insert(tk.END, f"No hay logs para la categoría '{category}'.\n")
        
        except Exception as e:
            self.logs_text.insert(tk.END, f"Error cargando logs: {e}\n")
    
    def run(self):
        """Ejecutar la aplicación"""
        try:
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"Error en ejecución del dashboard: {e}")
            messagebox.showerror("Error Fatal", f"Error ejecutando la aplicación:\n{e}")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    try:
        dashboard = GestorDashboard()
        dashboard.run()
    except Exception as e:
        print(f"Error iniciando dashboard: {e}")
        import traceback
        traceback.print_exc()
