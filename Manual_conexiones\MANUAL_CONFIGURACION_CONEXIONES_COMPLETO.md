# Manual de Configuración de Conexiones - PARTE 1
## Gestor de Archivos Corporativo

**Versión:** 2.0  
**Fecha:** Junio 2025  
**Aplicaciones:** Validación de Duplicados & Limpieza por Lista  

---

## 🎯 Introducción

Este manual explica cómo configurar conexiones a dispositivos NAS (QNAP) y PCs de workstation. El sistema utiliza un **método híbrido** con failover automático entre conexiones simbólicas y rutas de red.

### Aplicaciones Incluidas
- **IGSON_Validacion_Duplicados.exe** - Valida archivos duplicados entre origen y QNAPs
- **LimpiezaPorLista.exe** - Elimina archivos específicos de múltiples ubicaciones

---

## 📁 Estructura de Configuración

### Archivos Principales
```
├── config.json              # Configuración principal
├── encryption.key           # Clave de encriptación (auto-generada)
├── gestor_validacion_license.lic  # Licencia Validación
├── gestor_limpieza_license.lic    # Licencia Limpieza
└── logs/                    # Logs por operación y fecha
    ├── validacion_YYYYMMDD.log
    └── limpieza_YYYYMMDD.log
```

### Tipos de Conexión Soportados
- **Symlinks (Enlaces Simbólicos)**: Rutas locales mapeadas (ej: `C:\Links\qnap1`)
- **Network Paths (Rutas de Red)**: Rutas UNC directas (ej: `\\qnap1\share`)
- **Híbrido**: Failover automático entre ambos métodos

---

## 🐧 Configuración de Servidores Samba (Linux/Ubuntu)

### Archivo smb.conf de Ejemplo

```ini
# /etc/samba/smb.conf

[global]
workgroup = WORKGROUP
server string = Ubuntu File Server
netbios name = ubuntu-srv01
security = user
map to guest = bad user
encrypt passwords = yes
smb ports = 445

[shared]
comment = Shared Documents
path = /srv/samba/shared
browseable = yes
read only = no
guest ok = no
valid users = operador, admin
create mask = 0664
directory mask = 0775

[production]
comment = Production Files
path = /var/data/production
browseable = yes
read only = no
valid users = @production-group
create mask = 0660
directory mask = 0770

[backup]
comment = Backup Storage
path = /backup/files
browseable = no
read only = no
valid users = backup-user
admin users = admin
create mask = 0600
directory mask = 0700

[public]
comment = Public Share
path = /srv/samba/public
browseable = yes
read only = yes
guest ok = yes
```

### Configuración en el GUI según smb.conf
![alt text](imagenes/gui_PC_connection_example.png)
#### ✅ Campo Share: Nombre entre corchetes [nombre]

**Regla fundamental:** En el campo **Share** del GUI pones exactamente el nombre que está entre corchetes `[nombre]` en smb.conf.

#### Ejemplo 1: Share [shared] con autenticación
**Archivo samba:**
```ini
[shared]               # ← Este nombre va en el campo Share
path = /srv/samba/shared
valid users = operador, admin
```

**Configuración GUI:**
```
Hostname/IP: ************
Share: shared          # ← Exactamente el nombre del [shared]
Usuario: operador
Contraseña: [password]
Dominio: WORKGROUP
Subcarpeta: [vacío]
```

#### Ejemplo 2: Share [production] con subcarpeta
**Archivo samba:**
```ini
[production]           # ← Este nombre va en el campo Share
path = /var/data/production
valid users = @production-group
```

**Configuración GUI:**
```
Hostname/IP: ubuntu-srv01
Share: production      # ← Exactamente el nombre del [production]
Usuario: prod-user
Contraseña: [password]
Dominio: WORKGROUP
Subcarpeta: reports\monthly
```

#### Ejemplo 3: Share [backup] sin browseable
**Archivo samba:**
```ini
[backup]               # ← Este nombre va en el campo Share
path = /backup/files
browseable = no
valid users = backup-user
```

**Configuración GUI:**
```
Hostname/IP: ************
Share: backup          # ← Exactamente el nombre del [backup]
Usuario: backup-user
Contraseña: [password]
Dominio: [vacío]
Subcarpeta: [vacío]
```

### Reglas de Campos en el GUI

#### Campo Hostname/IP
- **SIN barras**: `************` ✅
- **SIN barras**: `ubuntu-srv01` ✅  
- **CON barras**: `\\************\\` ❌

#### Campo Share
- **Nombre del share**: `shared` ✅
- **Solo el nombre**: `production` ✅
- **SIN barras**: `backup` ✅
- **CON barras**: `\\shared\\` ❌

#### Campo Subcarpeta
- **Ruta relativa**: `reports\monthly` ✅
- **Con barra inicial**: `\temp\files` ✅
- **Sin barras**: `subfolder` ✅
- **Vacío**: `[vacío]` ✅ (raíz del share)

### Ejemplos Combinados Samba ↔ GUI

#### Servidor Ubuntu con múltiples shares
```ini
# smb.conf
[datos]                # ← Share name
path = /srv/data
valid users = operador

[archivos]             # ← Share name
path = /srv/files
valid users = admin, user1
```

**Configuración GUI para PC1:**
```
Hostname/IP: ubuntu-srv01
Share: datos           # ← Nombre del [datos]
Subcarpeta: produccion
```

**Configuración GUI para PC2:**
```
Hostname/IP: ubuntu-srv01
Share: archivos        # ← Nombre del [archivos]
Subcarpeta: trabajo\activos
```

### Matriz de Configuraciones Samba ↔ GUI

| smb.conf | GUI Hostname/IP | GUI Share | GUI Subcarpeta | Resultado Final |
|----------|----------------|-----------|----------------|-----------------|
| `[datos]` en `*************` | `*************` | `datos` | `[vacío]` | `\\*************\datos` |
| `[shared]` en `file-server` | `file-server` | `shared` | `temp` | `\\file-server\shared\temp` |
| `[backup]` en `nas.local` | `nas.local` | `backup` | `daily\logs` | `\\nas.local\backup\daily\logs` |
| `[public]` en `*********` | `*********` | `public` | `downloads` | `\\*********\public\downloads` |

### Casos Especiales

#### Share Oculto (termina en $)
**Samba config:**
```ini
[hidden$]              # ← Share name con $
path = /srv/hidden
browseable = no
```

**GUI config:**
```
Share: hidden$         # ← Incluye el $ en el nombre
```

#### Shares con espacios
**Samba config:**
```ini
[Company Files]        # ← Share name con espacios
path = /srv/company_files
```

**GUI config:**
```
Share: Company Files   # ← Respeta los espacios
```

#### Subcarpetas con espacios
**GUI config:**
```
Subcarpeta: Monthly Reports\2025\January
```

---

## ⚙️ Configuración Básica config.json

### Estructura del config.json

```json
{
  "app_info": {
    "name": "Gestor de Archivos Corporativo",
    "version": "1.0",
    "dev_mode": false
  },
  "connection_settings": {
    "connection_type": "hybrid",
    "timeout_seconds": 30,
    "retry_attempts": 3
  },
  "locations": {
    "qnap1": {
      "name": "QNAP Storage 1",
      "network_path": "\\\\qnap1\\share",
      "symlink_path": "C:\\Links\\qnap1",
      "enabled": true,
      "connection_priority": ["symlink", "network"],
      "credentials": {
        "username": "admin",
        "password_encrypted": "gAAAAABh...",
        "domain": "WORKGROUP"
      }
    }
  }
}
```

### Parámetros de Conexión

| Parámetro | Descripción | Valores |
|-----------|-------------|---------|
| `connection_type` | Método de conexión | `"symlink"`, `"network"`, `"hybrid"` |
| `timeout_seconds` | Timeout por conexión | `15-60` segundos |
| `retry_attempts` | Intentos de reconexión | `1-5` intentos |
| `connection_priority` | Orden de fallback | `["symlink", "network"]` o `["network", "symlink"]` |

---

## 🖥️ Configuración de Dispositivos QNAP
![alt text](imagenes/gui_qnap_connection_example.png)
### Configuración Estándar

```json
"qnap1": {
  "name": "QNAP Storage Principal",
  "network_path": "\\\\*************\\share",
  "symlink_path": "Q:\\",
  "enabled": true,
  "connection_priority": ["symlink", "network"],
  "credentials": {
    "username": "admin",
    "password_encrypted": "",
    "domain": "WORKGROUP"
  }
}
```

### Tipos de Configuración QNAP

#### 1. **Por IP Fija**
```json
"network_path": "\\\\*************\\PublicShare"
```

#### 2. **Por Nombre de Host**
```json
"network_path": "\\\\qnap-storage01\\datos"
```

#### 3. **Con Dominio Corporativo**
```json
"credentials": {
  "username": "administrador",
  "password_encrypted": "encrypted_password",
  "domain": "EMPRESA.LOCAL"
}
```

#### 4. **Acceso Público/Guest**
```json
"credentials": {
  "username": "",
  "password_encrypted": "",
  "domain": "WORKGROUP"
}
```

### Configuración de Enlaces Simbólicos

#### Crear Symlinks para QNAP

**Método 1: Script PowerShell (Recomendado)**
```powershell
# Ejecutar como Administrador
New-Item -ItemType SymbolicLink -Path "C:\Links\qnap1" -Target "\\*************\share"
New-Item -ItemType SymbolicLink -Path "C:\Links\qnap2" -Target "\\*************\backup"
```

**Método 2: CMD (Alternativo)**
```cmd
mklink /D "C:\Links\qnap1" "\\*************\share"
mklink /D "C:\Links\qnap2" "\\*************\backup"
```

**Método 3: Mapeo de Unidades de Red**
```json
"symlink_path": "Q:\\"  // Unidad mapeada manualmente
```

---

## 💻 Configuración de PCs de Workstation

### Configuración Windows Server/PC con SMB

#### Compartir Carpeta Windows
```cmd
# Crear share básico
net share datos=C:\SharedData /grant:Everyone,FULL

# Share con usuarios específicos  
net share produccion=D:\Production /grant:"DOMAIN\operador",FULL

# Share oculto (termina en $)
net share backup$=E:\Backup /grant:"Administradores",FULL
```

#### Configuración GUI desde Windows Share
**Share Windows:**
```cmd
net share archivos=C:\CompanyFiles /grant:"operador",CHANGE
```

**Configuración GUI:**
```
Hostname/IP: PC-SERVIDOR01
Share: archivos        # ← Nombre del share creado
Usuario: operador
Contraseña: [password]
Dominio: WORKGROUP
Subcarpeta: documentos\2025
```

### Configuraciones por Escenario

#### 1. **PC con Dominio Corporativo**
```json
"pc1": {
  "network_path": "\\\\pc-workstation-01\\shared",
  "credentials": {
    "username": "admin.sistemas",
    "password_encrypted": "gAAAAABh_encrypted_password",
    "domain": "EMPRESA.LOCAL"
  }
}
```

#### 2. **PC con Workgroup Local**
```json
"pc2": {
  "network_path": "\\\\************\\shared",
  "credentials": {
    "username": "administrador",
    "password_encrypted": "gAAAAABh_encrypted_password",
    "domain": "WORKGROUP"
  }
}
```

#### 3. **PC con Usuario Local**
```json
"pc3": {
  "network_path": "\\\\PC-PRODUCCION\\work",
  "credentials": {
    "username": "PC-PRODUCCION\\operador",
    "password_encrypted": "gAAAAABh_encrypted_password",
    "domain": ""
  }
}
```

---

## 🔄 Sistema de Failover y Detección

### Método Híbrido de Conexión

```python
# Flujo de conexión
1. Intenta método primario (symlink o network)
   ↓ Si falla ↓
2. Intenta método secundario (network o symlink)  
   ↓ Si falla ↓
3. Marca ubicación como "Sin conexión"
```

### Prioridades Recomendadas

#### Para QNAP (NAS)
```json
"connection_priority": ["symlink", "network"]
```

#### Para PCs
```json
"connection_priority": ["network", "symlink"]
```

---

## 🚀 Casos de Uso Específicos

### Caso 1: NAS Synology como QNAP

**Configuración Synology DSM:**
```
Panel de Control > Compartir archivos > Carpeta compartida
- Nombre: datos
- Ubicación: /volume1/datos
```

**GUI Configuration:**
```
Hostname/IP: *************
Share: datos           # ← Nombre de la carpeta compartida
Usuario: admin
Contraseña: [synology_password]
Dominio: WORKGROUP
Subcarpeta: produccion\2025
```

### Caso 2: Servidor CentOS/RHEL

**Configuración smb.conf:**
```ini
[enterprise]           # ← Share name
path = /opt/enterprise_data
valid users = @enterprise_group
```

**GUI Configuration:**
```
Hostname/IP: centos-server.empresa.com
Share: enterprise      # ← Nombre del [enterprise]
Usuario: enterprise_user
Dominio: EMPRESA.LOCAL
Subcarpeta: reports\quarterly
```

### Caso 3: FreeNAS/TrueNAS

**Configuración TrueNAS:**
```
Sharing > Windows (SMB) Shares
- Name: backup         # ← Este nombre va en Share
- Path: /mnt/pool1/backup
```

**GUI Configuration:**
```
Hostname/IP: truenas.local
Share: backup          # ← Nombre del share TrueNAS
Usuario: backup_user
Dominio: WORKGROUP  
Subcarpeta: daily\logs
```

---

## 🔗 Integración con Active Directory

### Configuración Samba con AD

```ini
# /etc/samba/smb.conf
[global]
workgroup = EMPRESA
realm = EMPRESA.LOCAL
security = ads
password server = dc1.empresa.local

[profiles]             # ← Share name
path = /srv/profiles
read only = no
profile acls = yes
```

**Comandos de Configuración AD:**
```bash
sudo net ads join -U Administrator
sudo systemctl enable winbind
sudo systemctl start winbind
```

**GUI Configuration para AD:**
```
Hostname/IP: file-server.empresa.local
Share: profiles        # ← Nombre del [profiles]
Usuario: EMPRESA\administrador.sistemas
Dominio: EMPRESA.LOCAL
Subcarpeta: users\%username%
```

---

## 📝 Ejemplos de Configuración Completa

### Escenario 1: Empresa Pequeña (2 QNAP + 2 PCs)

```json
{
  "locations": {
    "qnap1": {
      "name": "QNAP Principal",
      "network_path": "\\\\*************\\datos",
      "symlink_path": "Q:\\",
      "enabled": true,
      "connection_priority": ["symlink", "network"],
      "credentials": {
        "username": "admin",
        "password_encrypted": "gAAAAABh_qnap_password",
        "domain": "WORKGROUP"
      }
    },
    "pc1": {
      "name": "PC Producción",
      "network_path": "\\\\PC-PROD\\shared",
      "symlink_path": "C:\\Links\\pc1",
      "enabled": true,
      "connection_priority": ["network", "symlink"],
      "credentials": {
        "username": "operador",
        "password_encrypted": "gAAAAABh_pc_password",
        "domain": ""
      }
    }
  }
}
```

### Escenario 2: Configuración Samba + Windows

```json
{
  "locations": {
    "ubuntu_server": {
      "name": "Ubuntu File Server",
      "network_path": "\\\\************\\shared",
      "symlink_path": "U:\\",
      "enabled": true,
      "connection_priority": ["symlink", "network"],
      "credentials": {
        "username": "operador",
        "password_encrypted": "gAAAAABh_ubuntu_password",
        "domain": "WORKGROUP"
      }
    }
  }
}
```

---

## 🧪 Testing y Verificación

### Test de Conectividad Individual

**Dashboard de Conectividad:**
- ✅ Verde: Conectado
- ❌ Rojo: Error
- ⚪ Gris: Deshabilitado
- ❓ Amarillo: Sin configurar

### Tests desde Línea de Comandos

```cmd
# Test SMB Manual
net use \\*************\share /user:admin password
net view \\*************

# Test de Symlinks
dir C:\Links\qnap1
echo test > C:\Links\qnap1\test.txt
```

---

## 🚨 Troubleshooting

### Problemas Comunes

#### 1. **Error "Acceso Denegado"**
```
Causa: Credenciales incorrectas
Solución:
- Verificar usuario/contraseña
- Comprobar permisos del share
- Probar acceso manual: \\servidor\share
```

#### 2. **"No se puede encontrar la ruta de red"**
```
Causa: Problema de conectividad
Solución:
- ping *************
- telnet ************* 445
- Usar IP en lugar de nombre
```

#### 3. **Symlinks No Funcionan**
```
Causa: Permisos insuficientes
Solución:
- Ejecutar como Administrador
- Verificar que el target existe
- Recrear el symlink
```

### Comandos de Diagnóstico

```cmd
# Verificar conectividad
ping qnap1
telnet qnap1 445

# Listar shares
net view \\qnap1

# Test autenticación
net use \\qnap1\share /user:admin
```

---

## 🛠️ Configuración de Operaciones

### Validación de Duplicados

```json
"operations": {
  "validacion_duplicados": {
    "carpeta_origen": "C:\\Temp\\archivos_origen",
    "carpeta_rechazados": "C:\\Temp\\rechazados",
    "qnaps_a_revisar": ["qnap1", "qnap2"],
    "enabled": true
  }
}
```

### Limpieza por Lista

```json
"operations": {
  "limpieza_lista": {
    "ubicaciones_limpieza": ["qnap1", "qnap2", "pc1", "pc2", "pc3", "pc4"],
    "carpetas_objetivo": {
      "qnap1": "\\datos\\produccion",
      "qnap2": "\\backup\\files",
      "pc1": "\\shared\\work"
    },
    "enabled": true
  }
}
```

---

## 📋 Plantillas de Configuración SMB

### Plantilla Básica Ubuntu Server

```ini
# /etc/samba/smb.conf - Configuración básica
[global]
workgroup = WORKGROUP
server string = File Server
security = user

[shared]               # ← Share name
comment = Shared Files
path = /srv/shared
valid users = operador, admin
read only = no
```

**Correspondencia GUI:**
```
Hostname/IP: [IP_DEL_SERVIDOR]
Share: shared          # ← Nombre del [shared]
Usuario: operador
Dominio: WORKGROUP
```

### Comandos de Configuración Samba

```bash
# Crear usuario Samba
sudo smbpasswd -a operador

# Reiniciar servicio
sudo systemctl restart smbd

# Verificar configuración
testparm

# Test desde cliente
smbclient //servidor/shared -U operador
```

---

## 🔐 Gestión de Credenciales

### Encriptación de Contraseñas

```python
# La aplicación maneja automáticamente:
"password_encrypted": "gAAAAABh_abc123_encrypted_password"
```

### Configuración por Interfaz

1. Abrir aplicación
2. Ir a "Configuración" 
3. Seleccionar ubicación
4. Introducir credenciales

### Credenciales por Tipo

#### QNAP NAS
```json
"credentials": {
  "username": "admin",
  "password_encrypted": "...",
  "domain": "WORKGROUP"
}
```

#### PC con Dominio
```json
"credentials": {
  "username": "DOMINIO\\usuario",
  "password_encrypted": "...",
  "domain": "EMPRESA.LOCAL"
}
```

---

## 🔍 Detección Automática de QNAPs

### Auto-Discovery

```json
"auto_discovery": {
  "enabled": true,
  "scan_range": "***********-254",
  "qnap_ports": [80, 443, 8080],
  "timeout_per_host": 2
}
```

### Protocolos de Detección

1. **Puerto HTTP** - Busca servicios QNAP
2. **Banner SMB** - Identifica comparticiones QNAP  
3. **MDNS/Bonjour** - Busca servicios _qnap._tcp

---

## 📞 Soporte Técnico

### Información de Contacto
- **Desarrollador**: IGSON
- **Versión**: 2.0
- **Fecha**: Junio 2025

### Logs para Soporte

```
logs/sistema_YYYYMMDD.log
logs/conectividad_YYYYMMDD.log
config.json (sin contraseñas)
```

### Comandos de Diagnóstico

```cmd
# Estado del sistema
systeminfo | findstr /B /C:"OS"
ipconfig /all

# Test de red
nslookup qnap1
ping -t *************

# Servicios SMB
sc query lanmanserver
sc query lanmanworkstation
```

---

## 📚 Referencias

### Documentación
- [Samba Official Documentation](https://www.samba.org/samba/docs/)
- [Active Directory Integration](https://wiki.samba.org/index.php/Setting_up_Samba_as_a_Domain_Member)

### Herramientas
- **smbclient**: Cliente SMB para testing
- **testparm**: Validador configuración Samba
- **net**: Herramientas de red Windows
- **wbinfo**: Información Winbind (AD)

### Puertos SMB
- **445**: SMB directo sobre TCP
- **139**: NetBIOS Session Service
- **138**: NetBIOS Datagram Service  
- **137**: NetBIOS Name Service

---

*Manual de Configuración v2.0 - Gestor de Archivos Corporativo*  
*© 2025 IGSON - Todos los derechos reservados*
}
```

---

## 🔄 Sistema de Failover y Detección

### Método Híbrido de Conexión

```python
# Flujo de conexión
1. Intenta método primario (symlink o network)
   ↓ Si falla ↓
2. Intenta método secundario (network o symlink)  
   ↓ Si falla ↓
3. Marca ubicación como "Sin conexión"
```

### Prioridades Recomendadas

#### Para QNAP (NAS)
```json
"connection_priority": ["symlink", "network"]
```

#### Para PCs
```json
"connection_priority": ["network", "symlink"]
```

---

## 🚀 Casos de Uso Específicos

### Caso 1: NAS Synology como QNAP

**Configuración Synology DSM:**
```
Panel de Control > Compartir archivos > Carpeta compartida
- Nombre: datos
- Ubicación: /volume1/datos
```

**GUI Configuration:**
```
Hostname/IP: *************
Share: datos           # ← Nombre de la carpeta compartida
Usuario: admin
Contraseña: [synology_password]
Dominio: WORKGROUP
Subcarpeta: produccion\2025
```

### Caso 2: Servidor CentOS/RHEL

**Configuración smb.conf:**
```ini
[enterprise]           # ← Share name
path = /opt/enterprise_data
valid users = @enterprise_group
```

**GUI Configuration:**
```
Hostname/IP: centos-server.empresa.com
Share: enterprise      # ← Nombre del [enterprise]
Usuario: enterprise_user
Dominio: EMPRESA.LOCAL
Subcarpeta: reports\quarterly
```

### Caso 3: FreeNAS/TrueNAS

**Configuración TrueNAS:**
```
Sharing > Windows (SMB) Shares
- Name: backup         # ← Este nombre va en Share
- Path: /mnt/pool1/backup
```

**GUI Configuration:**
```
Hostname/IP: truenas.local
Share: backup          # ← Nombre del share TrueNAS
Usuario: backup_user
Dominio: WORKGROUP  
Subcarpeta: daily\logs
```

---

## 🔗 Integración con Active Directory

### Configuración Samba con AD

```ini
# /etc/samba/smb.conf
[global]
workgroup = EMPRESA
realm = EMPRESA.LOCAL
security = ads
password server = dc1.empresa.local

[profiles]             # ← Share name
path = /srv/profiles
read only = no
profile acls = yes
```

**Comandos de Configuración AD:**
```bash
sudo net ads join -U Administrator
sudo systemctl enable winbind
sudo systemctl start winbind
```

**GUI Configuration para AD:**
```
Hostname/IP: file-server.empresa.local
Share: profiles        # ← Nombre del [profiles]
Usuario: EMPRESA\administrador.sistemas
Dominio: EMPRESA.LOCAL
Subcarpeta: users\%username%
```

---

## 📝 Ejemplos de Configuración Completa

### Escenario 1: Empresa Pequeña (2 QNAP + 2 PCs)

```json
{
  "locations": {
    "qnap1": {
      "name": "QNAP Principal",
      "network_path": "\\\\*************\\datos",
      "symlink_path": "Q:\\",
      "enabled": true,
      "connection_priority": ["symlink", "network"],
      "credentials": {
        "username": "admin",
        "password_encrypted": "gAAAAABh_qnap_password",
        "domain": "WORKGROUP"
      }
    },
    "pc1": {
      "name": "PC Producción",
      "network_path": "\\\\PC-PROD\\shared",
      "symlink_path": "C:\\Links\\pc1",
      "enabled": true,
      "connection_priority": ["network", "symlink"],
      "credentials": {
        "username": "operador",
        "password_encrypted": "gAAAAABh_pc_password",
        "domain": ""
      }
    }
  }
}
```

### Escenario 2: Configuración Samba + Windows

```json
{
  "locations": {
    "ubuntu_server": {
      "name": "Ubuntu File Server",
      "network_path": "\\\\************\\shared",
      "symlink_path": "U:\\",
      "enabled": true,
      "connection_priority": ["symlink", "network"],
      "credentials": {
        "username": "operador",
        "password_encrypted": "gAAAAABh_ubuntu_password",
        "domain": "WORKGROUP"
      }
    }
  }
}
```

---

## 🧪 Testing y Verificación

### Test de Conectividad Individual

**Dashboard de Conectividad:**
- ✅ Verde: Conectado
- ❌ Rojo: Error
- ⚪ Gris: Deshabilitado
- ❓ Amarillo: Sin configurar

### Tests desde Línea de Comandos

```cmd
# Test SMB Manual
net use \\*************\share /user:admin password
net view \\*************

# Test de Symlinks
dir C:\Links\qnap1
echo test > C:\Links\qnap1\test.txt
```

---

## 🚨 Troubleshooting

### Problemas Comunes

#### 1. **Error "Acceso Denegado"**
```
Causa: Credenciales incorrectas
Solución:
- Verificar usuario/contraseña
- Comprobar permisos del share
- Probar acceso manual: \\servidor\share
```

#### 2. **"No se puede encontrar la ruta de red"**
```
Causa: Problema de conectividad
Solución:
- ping *************
- telnet ************* 445
- Usar IP en lugar de nombre
```

#### 3. **Symlinks No Funcionan**
```
Causa: Permisos insuficientes
Solución:
- Ejecutar como Administrador
- Verificar que el target existe
- Recrear el symlink
```

### Comandos de Diagnóstico

```cmd
# Verificar conectividad
ping qnap1
telnet qnap1 445

# Listar shares
net view \\qnap1

# Test autenticación
net use \\qnap1\share /user:admin
```

---

## 🛠️ Configuración de Operaciones

### Validación de Duplicados

```json
"operations": {
  "validacion_duplicados": {
    "carpeta_origen": "C:\\Temp\\archivos_origen",
    "carpeta_rechazados": "C:\\Temp\\rechazados",
    "qnaps_a_revisar": ["qnap1", "qnap2"],
    "enabled": true
  }
}
```

### Limpieza por Lista

```json
"operations": {
  "limpieza_lista": {
    "ubicaciones_limpieza": ["qnap1", "qnap2", "pc1", "pc2", "pc3", "pc4"],
    "carpetas_objetivo": {
      "qnap1": "\\datos\\produccion",
      "qnap2": "\\backup\\files",
      "pc1": "\\shared\\work"
    },
    "enabled": true
  }
}
```

---

## 📋 Plantillas de Configuración SMB

### Plantilla Básica Ubuntu Server

```ini
# /etc/samba/smb.conf - Configuración básica
[global]
workgroup = WORKGROUP
server string = File Server
security = user

[shared]               # ← Share name
comment = Shared Files
path = /srv/shared
valid users = operador, admin
read only = no
```

**Correspondencia GUI:**
```
Hostname/IP: [IP_DEL_SERVIDOR]
Share: shared          # ← Nombre del [shared]
Usuario: operador
Dominio: WORKGROUP
```

### Comandos de Configuración Samba

```bash
# Crear usuario Samba
sudo smbpasswd -a operador

# Reiniciar servicio
sudo systemctl restart smbd

# Verificar configuración
testparm

# Test desde cliente
smbclient //servidor/shared -U operador
```

---

## 🔐 Gestión de Credenciales

### Encriptación de Contraseñas

```python
# La aplicación maneja automáticamente:
"password_encrypted": "gAAAAABh_abc123_encrypted_password"
```

### Configuración por Interfaz

1. Abrir aplicación
2. Ir a "Configuración" 
3. Seleccionar ubicación
4. Introducir credenciales

### Credenciales por Tipo

#### QNAP NAS
```json
"credentials": {
  "username": "admin",
  "password_encrypted": "...",
  "domain": "WORKGROUP"
}
```

#### PC con Dominio
```json
"credentials": {
  "username": "DOMINIO\\usuario",
  "password_encrypted": "...",
  "domain": "EMPRESA.LOCAL"
}
```

---

## 🔍 Detección Automática de QNAPs

### Auto-Discovery

```json
"auto_discovery": {
  "enabled": true,
  "scan_range": "***********-254",
  "qnap_ports": [80, 443, 8080],
  "timeout_per_host": 2
}
```

### Protocolos de Detección

1. **Puerto HTTP** - Busca servicios QNAP
2. **Banner SMB** - Identifica comparticiones QNAP  
3. **MDNS/Bonjour** - Busca servicios _qnap._tcp

---

## 📞 Soporte Técnico

### Información de Contacto
- **Desarrollador**: IGSON
- **Versión**: 2.0
- **Fecha**: Junio 2025

### Logs para Soporte

```
logs/sistema_YYYYMMDD.log
logs/conectividad_YYYYMMDD.log
config.json (sin contraseñas)
```

### Comandos de Diagnóstico

```cmd
# Estado del sistema
systeminfo | findstr /B /C:"OS"
ipconfig /all

# Test de red
nslookup qnap1
ping -t *************

# Servicios SMB
sc query lanmanserver
sc query lanmanworkstation
```

---

## 📚 Referencias

### Documentación
- [Samba Official Documentation](https://www.samba.org/samba/docs/)
- [Active Directory Integration](https://wiki.samba.org/index.php/Setting_up_Samba_as_a_Domain_Member)

### Herramientas
- **smbclient**: Cliente SMB para testing
- **testparm**: Validador configuración Samba
- **net**: Herramientas de red Windows
- **wbinfo**: Información Winbind (AD)

### Puertos SMB
- **445**: SMB directo sobre TCP
- **139**: NetBIOS Session Service
- **138**: NetBIOS Datagram Service  
- **137**: NetBIOS Name Service

---

*Manual de Configuración v2.0 - Gestor de Archivos Corporativo*  
*© 2025 IGSON - Todos los derechos reservados*