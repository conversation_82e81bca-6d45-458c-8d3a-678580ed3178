"""
Gestor de Archivos Corporativo - Validación de Duplicados (FIXED + LOGO)
========================================================================

Primera operación completa: Revisa archivos en carpeta origen contra Qnaps
y mueve duplicados a carpeta "rechazados" para mantener origen limpio

INCLUYE: Logo IGSON en interfaz de consola
"""

import os
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Set
import logging
from datetime import datetime

# Imports del sistema
import sys
sys.path.append(str(Path(__file__).parent / "src"))
try:
    from core.config_manager import config
    from core.logger import get_logger, log_operation_start, log_operation_end, log_file_operation
    from core.utils import safe_move_file, get_file_hash, format_file_size, ProgressTracker
    from handlers.qnap_handler import QnapHandler
except ImportError as e:
    print(f"⚠️  Import error: {e}")
    print("ℹ️  Ejecutando en modo standalone...")

# Imports para manejo de imagen
try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

class ValidacionDuplicados:
    """
    Operación de validación y limpieza de archivos duplicados
    
    Funcionalidad:
    1. Escanea carpeta origen
    2. Verifica si archivos existen en Qnaps configurados
    3. Mueve duplicados a carpeta "rechazados"
    4. Mantiene carpeta origen siempre limpia
    5. Muestra logo IGSON en la interfaz
    """
    
    def __init__(self):
        self.operation_name = "Validación de Duplicados"
        
        # Configurar logging básico
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger('validacion')
        
        # Configuración por defecto para testing
        self.carpeta_origen = Path("C:/Temp/archivos_origen_test")
        self.carpeta_rechazados = Path("C:/Temp/archivos_rechazados")
        self.qnaps_a_revisar = ['qnap1', 'qnap2']
        
        # Crear carpetas de testing si no existen
        self._setup_testing_folders()
        
        # Inicializar estadísticas
        self.stats = {
            'archivos_escaneados': 0,
            'archivos_duplicados': 0,
            'archivos_movidos': 0,
            'archivos_fallidos': 0,
            'errores': [],
            'duplicados_por_qnap': {},
            'archivos_no_duplicados': 0,
            'tiempo_inicio': None,
            'tiempo_fin': None
        }
        
        # Inicializar contadores por qnap
        for qnap_id in self.qnaps_a_revisar:
            self.stats['duplicados_por_qnap'][qnap_id] = 0
        
        self.logger.info(f"Inicializada operación: {self.operation_name}")
        self.logger.info(f"Carpeta origen: {self.carpeta_origen}")
        self.logger.info(f"Carpeta rechazados: {self.carpeta_rechazados}")
        self.logger.info(f"Qnaps a revisar: {self.qnaps_a_revisar}")
    
    def _setup_testing_folders(self):
        """Configurar carpetas para testing"""
        try:
            # Crear carpeta origen de testing
            self.carpeta_origen.mkdir(parents=True, exist_ok=True)
            
            # Crear algunos archivos de prueba si la carpeta está vacía
            archivos_test = [
                "documento_test.pdf",
                "imagen_muestra.jpg", 
                "archivo_datos.xlsx",
                "video_ejemplo.mp4",
                "texto_simple.txt"
            ]
            
            archivos_existentes = list(self.carpeta_origen.glob("*"))
            if len(archivos_existentes) == 0:
                print(f"📁 Creando archivos de prueba en {self.carpeta_origen}")
                for archivo in archivos_test:
                    archivo_path = self.carpeta_origen / archivo
                    archivo_path.write_text(f"Archivo de prueba: {archivo}\nCreado: {datetime.now()}")
                print(f"✅ {len(archivos_test)} archivos de prueba creados")
            
            # Crear carpeta rechazados
            self.carpeta_rechazados.mkdir(parents=True, exist_ok=True)
            
        except Exception as e:
            print(f"⚠️  Error configurando carpetas de testing: {e}")
    
    def mostrar_logo_igson(self):
        """Mostrar logo IGSON en consola"""
        try:
            logo_path = Path(__file__).parent / "logo_igson.png"
            
            print("\n" + "="*60)
            print("    🏢 IGSON - Gestor de Archivos Corporativo")
            print("="*60)
            
            if logo_path.exists():
                print(f"📷 Logo encontrado: {logo_path.name}")
                
                if PIL_AVAILABLE:
                    try:
                        # Mostrar información de la imagen
                        with Image.open(logo_path) as img:
                            print(f"📐 Dimensiones: {img.size[0]}x{img.size[1]} píxeles")
                            print(f"🎨 Formato: {img.format}")
                    except Exception as e:
                        print(f"⚠️  No se pudo analizar la imagen: {e}")
                else:
                    print("ℹ️  PIL no disponible - instale Pillow para análisis de imagen")
            else:
                print("⚠️  Logo no encontrado en ruta esperada")
                print(f"📍 Buscado en: {logo_path}")
            
            print("🔍 OPERACIÓN: Validación de Archivos Duplicados")
            print("="*60 + "\n")
            
        except Exception as e:
            print(f"⚠️  Error mostrando logo: {e}")
    
    def escanear_carpeta_origen(self) -> List[Path]:
        """Escanea la carpeta origen y obtiene lista de archivos"""
        archivos = []
        
        try:
            self.logger.info(f"Escaneando carpeta origen: {self.carpeta_origen}")
            
            # Obtener todos los archivos (no directorios)
            for item in self.carpeta_origen.iterdir():
                if item.is_file():
                    archivos.append(item)
            
            self.logger.info(f"Encontrados {len(archivos)} archivos en carpeta origen")
            
            # Log algunos ejemplos
            for i, archivo in enumerate(archivos[:5]):  # Primeros 5
                size = archivo.stat().st_size / 1024  # KB
                self.logger.info(f"  {i+1}. {archivo.name} ({size:.1f} KB)")
            
            if len(archivos) > 5:
                self.logger.info(f"  ... y {len(archivos) - 5} archivos más")
                
        except Exception as e:
            error_msg = f"Error escaneando carpeta origen: {e}"
            self.logger.error(error_msg)
            self.stats['errores'].append(error_msg)
        
        return archivos
    
    def verificar_duplicados_simulado(self, archivo: Path) -> Dict[str, bool]:
        """Simula verificación de duplicados para testing"""
        resultados = {}
        
        # Simular que algunos archivos son duplicados
        duplicado_patterns = ['documento', 'imagen', 'datos']
        es_duplicado = any(pattern in archivo.name.lower() for pattern in duplicado_patterns)
        
        for qnap_id in self.qnaps_a_revisar:
            # Simular que se encuentra en el primer qnap si es duplicado
            if es_duplicado and qnap_id == self.qnaps_a_revisar[0]:
                resultados[qnap_id] = True
                self.stats['duplicados_por_qnap'][qnap_id] += 1
            else:
                resultados[qnap_id] = False
        
        return resultados
    
    def mover_a_rechazados(self, archivo: Path, simular: bool = True) -> Tuple[bool, str]:
        """Mueve un archivo duplicado a la carpeta de rechazados"""
        try:
            # Crear carpeta rechazados si no existe
            self.carpeta_rechazados.mkdir(parents=True, exist_ok=True)
            
            # Ruta destino
            destino = self.carpeta_rechazados / archivo.name
            
            # Si ya existe, agregar timestamp
            if destino.exists():
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                nombre_base = archivo.stem
                extension = archivo.suffix
                destino = self.carpeta_rechazados / f"{nombre_base}_{timestamp}{extension}"
            
            if simular:
                message = f"SIMULADO: Movería {archivo.name} → {destino.name}"
                return True, message
            else:
                # Mover archivo real
                archivo.rename(destino)
                message = f"Movido a rechazados: {archivo.name} → {destino.name}"
                return True, message
                
        except Exception as e:
            error_msg = f"Error moviendo archivo {archivo.name}: {e}"
            return False, error_msg
    
    def procesar_archivo(self, archivo: Path, dry_run: bool = True) -> Dict:
        """Procesa un archivo individual"""
        resultado = {
            'archivo': archivo.name,
            'es_duplicado': False,
            'qnaps_encontrado': [],
            'movido': False,
            'error': None
        }
        
        try:
            self.stats['archivos_escaneados'] += 1
            
            # Verificar duplicados (simulado)
            duplicados = self.verificar_duplicados_simulado(archivo)
            
            # Determinar si es duplicado
            qnaps_con_archivo = [qnap_id for qnap_id, existe in duplicados.items() if existe]
            
            if qnaps_con_archivo:
                # Es un duplicado
                resultado['es_duplicado'] = True
                resultado['qnaps_encontrado'] = qnaps_con_archivo
                self.stats['archivos_duplicados'] += 1
                
                print(f"  🔍 DUPLICADO: {archivo.name} encontrado en {qnaps_con_archivo}")
                
                # Mover a rechazados
                success, message = self.mover_a_rechazados(archivo, simular=dry_run)
                resultado['movido'] = success
                
                if success:
                    self.stats['archivos_movidos'] += 1
                    print(f"    ✅ {message}")
                else:
                    self.stats['archivos_fallidos'] += 1
                    resultado['error'] = message
                    print(f"    ❌ {message}")
            else:
                # No es duplicado
                self.stats['archivos_no_duplicados'] += 1
                print(f"  ✅ OK: {archivo.name} - No duplicado")
        
        except Exception as e:
            error_msg = f"Error procesando archivo {archivo.name}: {e}"
            self.logger.error(error_msg)
            resultado['error'] = error_msg
            self.stats['archivos_fallidos'] += 1
            self.stats['errores'].append(error_msg)
        
        return resultado
    
    def ejecutar(self, dry_run: bool = True) -> Dict:
        """Ejecuta la operación completa de validación de duplicados"""
        self.stats['tiempo_inicio'] = time.time()
        
        # Mostrar logo IGSON
        self.mostrar_logo_igson()
        
        print(f"🚀 Iniciando {self.operation_name}")
        if dry_run:
            print("🧪 MODO SIMULACIÓN ACTIVADO - No se moverán archivos")
        print(f"📁 Carpeta origen: {self.carpeta_origen}")
        print(f"🗂️  Carpeta rechazados: {self.carpeta_rechazados}")
        print(f"📀 QNAPs a verificar: {self.qnaps_a_revisar}")
        print()
        
        try:
            # 1. Escanear carpeta origen
            print("=== FASE 1: ESCANEO DE ARCHIVOS ===")
            archivos = self.escanear_carpeta_origen()
            
            if not archivos:
                print("⚠️  No hay archivos en carpeta origen")
                return self.stats
            
            print(f"✅ {len(archivos)} archivos encontrados para procesar")
            
            # 2. Simular conexión a QNAPs
            print("\n=== FASE 2: VERIFICACIÓN DE QNAPS ===")
            for qnap_id in self.qnaps_a_revisar:
                print(f"🔌 {qnap_id}: Conexión simulada - OK")
            
            # 3. Procesar archivos
            print("\n=== FASE 3: PROCESAMIENTO DE ARCHIVOS ===")
            
            resultados_procesamiento = []
            
            for i, archivo in enumerate(archivos, 1):
                print(f"\n📄 Procesando {i}/{len(archivos)}: {archivo.name}")
                resultado = self.procesar_archivo(archivo, dry_run)
                resultados_procesamiento.append(resultado)
            
            # 4. Resumen de resultados
            print("\n=== FASE 4: RESUMEN DE RESULTADOS ===")
            self._mostrar_resumen()
            
        except Exception as e:
            error_msg = f"Error en ejecución de operación: {e}"
            print(f"❌ {error_msg}")
            self.stats['errores'].append(error_msg)
        
        finally:
            # Finalizar estadísticas
            self.stats['tiempo_fin'] = time.time()
            duration = self.stats['tiempo_fin'] - self.stats['tiempo_inicio']
            
            print(f"\n⏱️  Duración: {duration:.2f} segundos")
            
            if dry_run:
                print("\n🧪 SIMULACIÓN COMPLETADA - No se realizaron cambios reales")
            
            print("="*60)
        
        return self.stats
    
    def _mostrar_resumen(self):
        """Mostrar resumen detallado de la operación"""
        print("📊 ESTADÍSTICAS DE VALIDACIÓN:")
        print(f"  📄 Archivos escaneados: {self.stats['archivos_escaneados']}")
        print(f"  🔍 Archivos duplicados: {self.stats['archivos_duplicados']}")
        print(f"  ✅ Archivos no duplicados: {self.stats['archivos_no_duplicados']}")
        print(f"  📦 Archivos movidos: {self.stats['archivos_movidos']}")
        print(f"  ❌ Archivos fallidos: {self.stats['archivos_fallidos']}")
        
        # Duplicados por Qnap
        print("\n📀 Duplicados por QNAP:")
        for qnap_id, count in self.stats['duplicados_por_qnap'].items():
            print(f"    {qnap_id}: {count} duplicados")
        
        # Errores
        if self.stats['errores']:
            print(f"\n🚨 Errores ({len(self.stats['errores'])}):")
            for error in self.stats['errores'][:3]:
                print(f"    - {error}")
    
    def get_stats(self) -> Dict:
        """Obtiene estadísticas actuales de la operación"""
        return self.stats.copy()

def main():
    """Función principal con logo IGSON"""
    try:
        print("🔍 Validación de Duplicados - Test con Logo IGSON")
        print("=" * 50)
        
        # Crear operación
        validador = ValidacionDuplicados()
        
        print(f"\n📋 CONFIGURACIÓN:")
        print(f"  Carpeta origen: {validador.carpeta_origen}")
        print(f"  Carpeta rechazados: {validador.carpeta_rechazados}")
        print(f"  QNAPs a revisar: {validador.qnaps_a_revisar}")
        
        # Ejecutar en modo simulación
        stats = validador.ejecutar(dry_run=True)
        
        print(f"\n📈 ESTADÍSTICAS FINALES:")
        print(f"  Archivos procesados: {stats['archivos_escaneados']}")
        print(f"  Duplicados encontrados: {stats['archivos_duplicados']}")
        print(f"  Errores: {len(stats['errores'])}")
        
        if stats['tiempo_inicio'] and stats['tiempo_fin']:
            duracion = stats['tiempo_fin'] - stats['tiempo_inicio']
            print(f"  Duración: {duracion:.2f} segundos")
        
        print("\n✅ OPERACIÓN COMPLETADA EXITOSAMENTE")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
