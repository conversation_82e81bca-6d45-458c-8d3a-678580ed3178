"""
dashboard_pieza3b.py
PIEZA 3B: Pestaña de Limpieza - UI y estructura
Gestor de Archivos Corporativo
"""

# CONTINUACIÓN DE LA CLASE GestorDashboard - PESTAÑA LIMPIEZA

    def create_limpieza_tab(self):
        """Crear pestaña de limpieza por lista avanzada"""
        limpieza_frame = ttk.Frame(self.notebook)
        self.notebook.add(limpieza_frame, text="🗑️ Limpieza")
        
        # Header de la pestaña
        header_frame = ttk.Frame(limpieza_frame)
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(header_frame, text="🗑️ Limpieza por Lista", 
                 style='Title.TLabel').pack(side=tk.LEFT)
        
        # Botón de ayuda
        ttk.Button(header_frame, text="❓ Ayuda", 
                  command=self.show_limpieza_help).pack(side=tk.RIGHT, padx=5)
        
        # Frame principal con scroll
        main_canvas_lim = tk.<PERSON>(limpieza_frame)
        scrollbar_lim = ttk.Scrollbar(limpieza_frame, orient="vertical", command=main_canvas_lim.yview)
        scrollable_lim_frame = ttk.Frame(main_canvas_lim)
        
        scrollable_lim_frame.bind(
            "<Configure>",
            lambda e: main_canvas_lim.configure(scrollregion=main_canvas_lim.bbox("all"))
        )
        
        main_canvas_lim.create_window((0, 0), window=scrollable_lim_frame, anchor="nw")
        main_canvas_lim.configure(yscrollcommand=scrollbar_lim.set)
        
        main_canvas_lim.pack(side="left", fill="both", expand=True)
        scrollbar_lim.pack(side="right", fill="y")
        
        # === SECCIÓN: CONFIGURACIÓN ===
        config_frame_lim = ttk.LabelFrame(scrollable_lim_frame, text="⚙️ Configuración de Limpieza", padding="15")
        config_frame_lim.pack(fill=tk.X, padx=10, pady=10)
        
        # Archivo de lista
        ttk.Label(config_frame_lim, text="📄 Archivo de Lista:", style='Status.TLabel').pack(anchor=tk.W, pady=(0, 5))
        
        archivo_frame = ttk.Frame(config_frame_lim)
        archivo_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.limpieza_archivo_entry = ttk.Entry(archivo_frame, textvariable=self.limpieza_archivo_var, font=('Consolas', 9))
        self.limpieza_archivo_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Button(archivo_frame, text="📂", width=3, 
                  command=self.select_limpieza_file).pack(side=tk.RIGHT, padx=(5,0))
        
        ttk.Button(archivo_frame, text="👁️", width=3, 
                  command=self.preview_list_file).pack(side=tk.RIGHT, padx=(2,0))
        
        ttk.Button(archivo_frame, text="✏️", width=3, 
                  command=self.edit_list_file).pack(side=tk.RIGHT, padx=(2,0))
        
        # Información del archivo
        self.file_info_frame = ttk.Frame(config_frame_lim)
        self.file_info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.file_info_label = ttk.Label(self.file_info_frame, text="📄 Seleccione un archivo de lista", 
                                       style='Small.TLabel')
        self.file_info_label.pack(anchor=tk.W)
        
        # Ubicaciones objetivo
        locations_frame = ttk.LabelFrame(config_frame_lim, text="🌐 Ubicaciones Objetivo", padding="10")
        locations_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Checkboxes para ubicaciones
        self.limpieza_locations = {}
        locations_grid = ttk.Frame(locations_frame)
        locations_grid.pack(fill=tk.X)
        
        locations_list = ['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4']
        for i, location in enumerate(locations_list):
            var = tk.BooleanVar(value=True)
            self.limpieza_locations[location] = var
            
            row = i // 3
            col = i % 3
            
            cb = ttk.Checkbutton(locations_grid, text=location.upper(), variable=var)
            cb.grid(row=row, column=col, sticky='w', padx=10, pady=2)
        
        # Configurar grid weights
        for col in range(3):
            locations_grid.columnconfigure(col, weight=1)
        
        # Botones de selección rápida
        quick_select_frame = ttk.Frame(locations_frame)
        quick_select_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(quick_select_frame, text="✅ Todas", width=8,
                  command=self.select_all_locations).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(quick_select_frame, text="❌ Ninguna", width=8,
                  command=self.deselect_all_locations).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(quick_select_frame, text="🏢 Solo QNAPs", width=10,
                  command=self.select_only_qnaps).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(quick_select_frame, text="💻 Solo PCs", width=10,
                  command=self.select_only_pcs).pack(side=tk.LEFT, padx=5)
        
        # Opciones avanzadas
        options_frame_lim = ttk.LabelFrame(config_frame_lim, text="🔧 Opciones Avanzadas", padding="10")
        options_frame_lim.pack(fill=tk.X, pady=(10, 0))
        
        # Fila 1 de opciones
        options_row1_lim = ttk.Frame(options_frame_lim)
        options_row1_lim.pack(fill=tk.X, pady=2)
        
        ttk.Checkbutton(options_row1_lim, text="🧪 Modo Simulación (Dry Run)", 
                       variable=self.limpieza_dry_run_var).pack(side=tk.LEFT, padx=10)
        
        self.limpieza_backup_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(options_row1_lim, text="💾 Crear backup antes de eliminar", 
                       variable=self.limpieza_backup_var).pack(side=tk.LEFT, padx=10)
        
        # Fila 2 de opciones
        options_row2_lim = ttk.Frame(options_frame_lim)
        options_row2_lim.pack(fill=tk.X, pady=2)
        
        self.limpieza_parallel_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_row2_lim, text="⚡ Procesamiento paralelo", 
                       variable=self.limpieza_parallel_var).pack(side=tk.LEFT, padx=10)
        
        self.limpieza_confirm_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_row2_lim, text="❓ Confirmar antes de eliminar", 
                       variable=self.limpieza_confirm_var).pack(side=tk.LEFT, padx=10)
        
        # Fila 3 de opciones
        options_row3_lim = ttk.Frame(options_frame_lim)
        options_row3_lim.pack(fill=tk.X, pady=2)
        
        self.limpieza_verify_before_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_row3_lim, text="🔍 Verificar existencia antes", 
                       variable=self.limpieza_verify_before_var).pack(side=tk.LEFT, padx=10)
        
        self.limpieza_skip_errors_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(options_row3_lim, text="⏭️ Continuar si hay errores", 
                       variable=self.limpieza_skip_errors_var).pack(side=tk.LEFT, padx=10)
        
        # === SECCIÓN: EJECUCIÓN ===
        execution_frame_lim = ttk.LabelFrame(scrollable_lim_frame, text="🚀 Ejecución de Limpieza", padding="15")
        execution_frame_lim.pack(fill=tk.X, padx=10, pady=10)
        
        # Botones principales
        main_buttons_lim = ttk.Frame(execution_frame_lim)
        main_buttons_lim.pack(fill=tk.X, pady=(0, 10))
        
        self.limpieza_execute_btn = ttk.Button(main_buttons_lim, text="🚀 Ejecutar Limpieza", 
                                             style='Action.TButton', command=self.execute_limpieza)
        self.limpieza_execute_btn.pack(side=tk.LEFT, padx=5)
        
        self.limpieza_stop_btn = ttk.Button(main_buttons_lim, text="⏹️ Detener", 
                                          state='disabled', command=self.stop_limpieza)
        self.limpieza_stop_btn.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(main_buttons_lim, text="🔍 Análisis Previo", 
                  command=self.analyze_limpieza).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(main_buttons_lim, text="💾 Guardar Config", 
                  command=self.save_limpieza_config).pack(side=tk.RIGHT, padx=5)
        
        # Progreso con detalles por ubicación
        progress_frame_lim = ttk.Frame(execution_frame_lim)
        progress_frame_lim.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(progress_frame_lim, text="Progreso General:").pack(anchor=tk.W)
        
        progress_row = ttk.Frame(progress_frame_lim)
        progress_row.pack(fill=tk.X, pady=(5, 0))
        
        self.limpieza_progress = ttk.Progressbar(progress_row, mode='determinate')
        self.limpieza_progress.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.limpieza_progress_label = ttk.Label(progress_row, text="0%")
        self.limpieza_progress_label.pack(side=tk.RIGHT, padx=(10, 0))
        
        # Progreso individual por ubicación
        self.location_progress_frame = ttk.LabelFrame(execution_frame_lim, text="📊 Progreso por Ubicación", padding="5")
        self.location_progress_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.location_progress_labels = {}
        for i, location in enumerate(locations_list):
            loc_frame = ttk.Frame(self.location_progress_frame)
            loc_frame.pack(fill=tk.X, pady=1)
            
            # Checkbox para habilitar/deshabilitar durante ejecución
            loc_enabled_var = tk.BooleanVar(value=True)
            ttk.Checkbutton(loc_frame, text=f"{location.upper()}:", 
                           variable=loc_enabled_var, width=8).pack(side=tk.LEFT)
            
            progress_bar = ttk.Progressbar(loc_frame, mode='determinate', length=120)
            progress_bar.pack(side=tk.LEFT, padx=(5, 10))
            
            status_label = ttk.Label(loc_frame, text="Esperando...", width=12)
            status_label.pack(side=tk.LEFT)
            
            files_label = ttk.Label(loc_frame, text="0/0", width=8, style='Small.TLabel')
            files_label.pack(side=tk.LEFT)
            
            self.location_progress_labels[location] = {
                'enabled': loc_enabled_var,
                'progress': progress_bar,
                'status': status_label,
                'files': files_label
            }
        
        # === SECCIÓN: RESULTADOS ===
        results_frame_lim = ttk.LabelFrame(scrollable_lim_frame, text="📊 Resultados y Estadísticas", padding="15")
        results_frame_lim.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Notebook para diferentes vistas de resultados
        results_notebook_lim = ttk.Notebook(results_frame_lim)
        results_notebook_lim.pack(fill=tk.BOTH, expand=True)
        
        # Tab: Resumen
        summary_tab_lim = ttk.Frame(results_notebook_lim)
        results_notebook_lim.add(summary_tab_lim, text="📋 Resumen")
        
        self.limpieza_summary_text = tk.Text(summary_tab_lim, height=6, wrap=tk.WORD, 
                                           font=('Consolas', 9), state='disabled')
        summary_scroll_lim = ttk.Scrollbar(summary_tab_lim, orient=tk.VERTICAL, 
                                         command=self.limpieza_summary_text.yview)
        self.limpieza_summary_text.configure(yscrollcommand=summary_scroll_lim.set)
        
        self.limpieza_summary_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        summary_scroll_lim.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Tab: Por ubicación
        location_tab = ttk.Frame(results_notebook_lim)
        results_notebook_lim.add(location_tab, text="🌐 Por Ubicación")
        
        self.limpieza_location_text = tk.Text(location_tab, height=6, wrap=tk.WORD, 
                                            font=('Consolas', 8), state='disabled')
        location_scroll = ttk.Scrollbar(location_tab, orient=tk.VERTICAL, 
                                      command=self.limpieza_location_text.yview)
        self.limpieza_location_text.configure(yscrollcommand=location_scroll.set)
        
        self.limpieza_location_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        location_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Tab: Log detallado
        log_tab_lim = ttk.Frame(results_notebook_lim)
        results_notebook_lim.add(log_tab_lim, text="📝 Log Detallado")
        
        self.limpieza_log_text = tk.Text(log_tab_lim, height=6, wrap=tk.WORD, 
                                       font=('Consolas', 8), state='disabled')
        log_scroll_lim = ttk.Scrollbar(log_tab_lim, orient=tk.VERTICAL, 
                                     command=self.limpieza_log_text.yview)
        self.limpieza_log_text.configure(yscrollcommand=log_scroll_lim.set)
        
        self.limpieza_log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scroll_lim.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Tab: Estadísticas avanzadas
        advanced_stats_tab = ttk.Frame(results_notebook_lim)
        results_notebook_lim.add(advanced_stats_tab, text="📈 Estadísticas")
        
        self.limpieza_advanced_stats_text = tk.Text(advanced_stats_tab, height=6, wrap=tk.WORD, 
                                                  font=('Consolas', 9), state='disabled')
        advanced_stats_scroll = ttk.Scrollbar(advanced_stats_tab, orient=tk.VERTICAL, 
                                            command=self.limpieza_advanced_stats_text.yview)
        self.limpieza_advanced_stats_text.configure(yscrollcommand=advanced_stats_scroll.set)
        
        self.limpieza_advanced_stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        advanced_stats_scroll.pack(side=tk.RIGHT, fill=tk.Y)
    
    def show_limpieza_help(self):
        """Mostrar ayuda para limpieza"""
        help_window = tk.Toplevel(self.root)
        help_window.title("Ayuda - Limpieza por Lista")
        help_window.geometry("600x500")
        help_window.transient(self.root)
        
        help_text = tk.Text(help_window, wrap=tk.WORD, font=('Arial', 10))
        help_scroll = ttk.Scrollbar(help_window, orient=tk.VERTICAL, command=help_text.yview)
        help_text.configure(yscrollcommand=help_scroll.set)
        
        help_content = """
🗑️ LIMPIEZA POR LISTA - AYUDA

DESCRIPCIÓN:
La limpieza por lista elimina archivos específicos de múltiples ubicaciones usando una lista en archivo de texto.

CONFIGURACIÓN:
• Archivo de Lista: Archivo .txt con nombres de archivos (uno por línea)
• Ubicaciones: Seleccione dónde buscar y eliminar archivos

FORMATO DEL ARCHIVO:
archivo1.pdf
imagen.jpg
documento.docx
video.mp4

UBICACIONES OBJETIVO:
• Seleccione las ubicaciones donde buscar archivos
• Use botones de selección rápida para mayor comodidad
• Puede cambiar selección durante la ejecución

OPCIONES AVANZADAS:
• 🧪 Modo Simulación: Solo muestra qué eliminaría sin hacerlo
• 💾 Crear backup: Copia de seguridad antes de eliminar
• ⚡ Procesamiento paralelo: Proceso simultáneo en ubicaciones
• ❓ Confirmar: Pedir confirmación antes de eliminar
• 🔍 Verificar existencia: Comprobar que el archivo existe antes
• ⏭️ Continuar si hay errores: No detener por errores individuales

PROCESO:
1. Lee y valida archivo de lista
2. Busca archivos en ubicaciones seleccionadas
3. Elimina archivos encontrados (con opciones de backup)
4. Genera estadísticas detalladas por ubicación

RECOMENDACIONES:
• Use modo simulación para verificar primero
• Revise la lista de archivos cuidadosamente
• Active backup para archivos importantes
• Verifique permisos en ubicaciones remotas
• Use análisis previo para validar configuración
        """
        
        help_text.insert(1.0, help_content.strip())
        help_text.config(state='disabled')
        
        help_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        help_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
        ttk.Button(help_window, text="Cerrar", command=help_window.destroy).pack(pady=10)
    
    # === MÉTODOS DE SELECCIÓN RÁPIDA ===
    
    def select_all_locations(self):
        """Seleccionar todas las ubicaciones"""
        for var in self.limpieza_locations.values():
            var.set(True)
        self.update_status("Todas las ubicaciones seleccionadas", "info")
    
    def deselect_all_locations(self):
        """Deseleccionar todas las ubicaciones"""
        for var in self.limpieza_locations.values():
            var.set(False)
        self.update_status("Todas las ubicaciones deseleccionadas", "warning")
    
    def select_only_qnaps(self):
        """Seleccionar solo ubicaciones QNAP"""
        for location, var in self.limpieza_locations.items():
            var.set(location.startswith('qnap'))
        self.update_status("Solo QNAPs seleccionados", "info")
    
    def select_only_pcs(self):
        """Seleccionar solo ubicaciones PC"""
        for location, var in self.limpieza_locations.items():
            var.set(location.startswith('pc'))
        self.update_status("Solo PCs seleccionados", "info")

# FIN PIEZA 3B
