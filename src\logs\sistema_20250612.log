2025-06-12 21:21:57 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-12 21:21:57 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-12 21:21:57 - gestor.sistema - INFO - Sistema detectado: Windows 11
2025-06-12 21:21:57 - gestor.sistema - INFO - Modo desarrollo: True
2025-06-12 21:21:57 - gestor.sistema - INFO - Ubicaciones habilitadas: 6
2025-06-12 21:36:02 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-12 21:36:02 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-12 22:22:01 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-12 22:22:01 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-12 22:22:01 - gestor.sistema - INFO - === GESTOR DE ARCHIVOS CORPORATIVO INICIADO ===
2025-06-12 22:22:01 - gestor.sistema - INFO - Directorio de trabajo: D:\sports_manager\gestor_archivos_corporativo
2025-06-12 22:22:01 - gestor.sistema - INFO - Python: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-06-12 22:22:02 - gestor.sistema - INFO - Aplicación: Gestor de Archivos Corporativo v1.0
2025-06-12 22:22:02 - gestor.sistema - INFO - Modo: Desarrollo
2025-06-12 22:22:02 - gestor.sistema - INFO - Ubicaciones configuradas: 6
2025-06-12 22:22:02 - gestor.sistema - WARNING - Advertencias de configuración:
2025-06-12 22:22:02 - gestor.sistema - WARNING -   - Location qnap1: No username configured
2025-06-12 22:22:02 - gestor.sistema - WARNING -   - Location qnap2: No username configured
2025-06-12 22:22:02 - gestor.sistema - WARNING -   - Location pc1: No username configured
2025-06-12 22:22:02 - gestor.sistema - WARNING -   - Location pc2: No username configured
2025-06-12 22:22:02 - gestor.sistema - WARNING -   - Location pc3: No username configured
2025-06-12 22:22:02 - gestor.sistema - WARNING -   - Location pc4: No username configured
2025-06-12 22:22:02 - gestor.sistema - INFO - Iniciando interfaz gráfica...
2025-06-12 22:22:02 - gestor.sistema - ERROR - Error fatal en la aplicación: unexpected indent (dashboard.py, line 1)
2025-06-12 22:22:02 - gestor.sistema - ERROR - Detalles del error:
Traceback (most recent call last):
  File "d:\sports_manager\gestor_archivos_corporativo\main.py", line 99, in main
    from ui.dashboard import GestorDashboard
  File "d:\sports_manager\gestor_archivos_corporativo\src\ui\dashboard.py", line 1
    result += f"✅ Archivos válidos: {stats.get('archivos_validos', 0)}\n"
IndentationError: unexpected indent
2025-06-12 22:22:06 - gestor.sistema - INFO - === GESTOR DE ARCHIVOS CORPORATIVO FINALIZADO ===
2025-06-12 22:43:30 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-12 22:43:30 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-12 22:43:30 - gestor.sistema - INFO - === GESTOR DE ARCHIVOS CORPORATIVO INICIADO ===
2025-06-12 22:43:30 - gestor.sistema - INFO - Directorio de trabajo: D:\sports_manager\gestor_archivos_corporativo
2025-06-12 22:43:30 - gestor.sistema - INFO - Python: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-06-12 22:43:30 - gestor.sistema - INFO - Aplicación: Gestor de Archivos Corporativo v1.0
2025-06-12 22:43:30 - gestor.sistema - INFO - Modo: Desarrollo
2025-06-12 22:43:30 - gestor.sistema - INFO - Ubicaciones configuradas: 6
2025-06-12 22:43:30 - gestor.sistema - WARNING - Advertencias de configuración:
2025-06-12 22:43:30 - gestor.sistema - WARNING -   - Location qnap1: No username configured
2025-06-12 22:43:30 - gestor.sistema - WARNING -   - Location qnap2: No username configured
2025-06-12 22:43:30 - gestor.sistema - WARNING -   - Location pc1: No username configured
2025-06-12 22:43:30 - gestor.sistema - WARNING -   - Location pc2: No username configured
2025-06-12 22:43:30 - gestor.sistema - WARNING -   - Location pc3: No username configured
2025-06-12 22:43:30 - gestor.sistema - WARNING -   - Location pc4: No username configured
2025-06-12 22:43:30 - gestor.sistema - INFO - Iniciando interfaz gráfica...
2025-06-12 22:43:30 - gestor.sistema - ERROR - Error fatal en la aplicación: Too early to create variable: no default root window
2025-06-12 22:43:30 - gestor.sistema - ERROR - Detalles del error:
Traceback (most recent call last):
  File "d:\sports_manager\gestor_archivos_corporativo\main.py", line 101, in main
    dashboard = GestorDashboard()
  File "d:\sports_manager\gestor_archivos_corporativo\src\ui\dashboard.py", line 20, in __init__
    self.setup_variables()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "d:\sports_manager\gestor_archivos_corporativo\src\ui\dashboard.py", line 38, in setup_variables
    self.validacion_origen_var = tk.StringVar()
                                 ~~~~~~~~~~~~^^
  File "C:\Python313\Lib\tkinter\__init__.py", line 565, in __init__
    Variable.__init__(self, master, value, name)
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python313\Lib\tkinter\__init__.py", line 397, in __init__
    master = _get_default_root('create variable')
  File "C:\Python313\Lib\tkinter\__init__.py", line 323, in _get_default_root
    raise RuntimeError(f"Too early to {what}: no default root window")
RuntimeError: Too early to create variable: no default root window
2025-06-12 22:43:32 - gestor.sistema - INFO - === GESTOR DE ARCHIVOS CORPORATIVO FINALIZADO ===
