#!/usr/bin/env python3
"""
Script de Build para Ejecutables del Gestor
==========================================

Compila las aplicaciones a .exe usando PyInstaller
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
from datetime import datetime

class ExecutableBuilder:
    """Builder para ejecutables del gestor"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.build_dir = self.base_dir / "build"
        self.dist_dir = self.base_dir / "dist"
        self.output_dir = self.base_dir / "executables"
        
        # Configuración de builds
        self.apps_to_build = {
            "license_generator": {
                "file": "dev_gestor_license_generator.py",
                "name": "IGSON_License_Generator",
                "icon": None,
                "windowed": True,
                "onefile": True
            },
            "validacion_duplicados": {
                "file": "validacion_duplicados_gui_real.py", 
                "name": "IGSON_Validacion_Duplicados",
                "icon": None,
                "windowed": True,
                "onefile": True
            }
        }
    
    def clean_previous_builds(self):
        """Limpiar builds anteriores"""
        print("🗑️ Limpiando builds anteriores...")
        
        for dir_to_clean in [self.build_dir, self.dist_dir]:
            if dir_to_clean.exists():
                shutil.rmtree(dir_to_clean)
                print(f"   Eliminado: {dir_to_clean}")
        
        print("✅ Limpieza completada")
    
    def check_pyinstaller(self):
        """Verificar que PyInstaller esté instalado"""
        try:
            result = subprocess.run(['pyinstaller', '--version'], 
                                  capture_output=True, text=True, check=True)
            print(f"✅ PyInstaller disponible: {result.stdout.strip()}")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ PyInstaller no encontrado")
            print("   Instalar con: pip install pyinstaller")
            return False
    
    def build_app(self, app_id, config):
        """Compilar una aplicación específica"""
        print(f"\n🔨 Compilando: {config['name']}")
        print(f"   Archivo: {config['file']}")
        
        # Verificar que el archivo existe
        source_file = self.base_dir / config['file']
        if not source_file.exists():
            print(f"❌ Archivo no encontrado: {source_file}")
            return False
        
        # Construir comando PyInstaller
        cmd = [
            'pyinstaller',
            '--clean',  # Limpiar cache
            '--noconfirm',  # No confirmar overwrite
        ]
        
        # Opciones específicas
        if config.get('onefile', True):
            cmd.append('--onefile')
        
        if config.get('windowed', True):
            cmd.append('--windowed')
        
        # Nombre del ejecutable
        cmd.extend(['--name', config['name']])
        
        # Icon si está disponible
        if config.get('icon') and Path(config['icon']).exists():
            cmd.extend(['--icon', config['icon']])
        
        # Incluir archivos adicionales si es necesario
        if app_id == "validacion_duplicados":
            # Incluir src folder para handlers
            cmd.extend(['--add-data', 'src;src'])
            # Incluir location config widget
            if (self.base_dir / "location_config_widget.py").exists():
                cmd.extend(['--add-data', 'location_config_widget.py;.'])
            # Incluir logo IGSON
            if (self.base_dir / "logo_igson.png").exists():
                cmd.extend(['--add-data', 'logo_igson.png;.'])
        
        # Excluir módulos no necesarios para reducir tamaño
        exclude_modules = [
            'matplotlib', 'numpy', 'scipy', 'pandas', 'jupyter',
            'IPython', 'tornado', 'zmq', 'PIL.ImageQt'
        ]
        
        for module in exclude_modules:
            cmd.extend(['--exclude-module', module])
        
        # Archivo source
        cmd.append(str(source_file))
        
        print(f"   Comando: {' '.join(cmd)}")
        
        try:
            # Ejecutar PyInstaller
            result = subprocess.run(cmd, cwd=self.base_dir, 
                                  capture_output=True, text=True, check=True)
            
            print(f"✅ Compilación exitosa: {config['name']}")
            
            # Mostrar warnings si los hay
            if result.stderr:
                warnings = [line for line in result.stderr.split('\n') 
                           if 'WARNING' in line]
                if warnings:
                    print("⚠️ Warnings:")
                    for warning in warnings[:5]:  # Solo primeros 5
                        print(f"   {warning}")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Error compilando {config['name']}")
            print(f"   Error: {e}")
            if e.stderr:
                print(f"   Stderr: {e.stderr}")
            return False
    
    def organize_outputs(self):
        """Organizar archivos de salida"""
        print(f"\n📁 Organizando archivos en: {self.output_dir}")
        
        # Crear directorio de salida
        self.output_dir.mkdir(exist_ok=True)
        
        # Timestamp para versioning
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        version_dir = self.output_dir / f"release_{timestamp}"
        version_dir.mkdir(exist_ok=True)
        
        success_count = 0
        
        # Mover ejecutables
        for app_id, config in self.apps_to_build.items():
            exe_name = f"{config['name']}.exe"
            source_exe = self.dist_dir / exe_name
            
            if source_exe.exists():
                dest_exe = version_dir / exe_name
                shutil.move(str(source_exe), str(dest_exe))
                
                # Info del archivo
                size_mb = dest_exe.stat().st_size / (1024 * 1024)
                print(f"   ✅ {exe_name} ({size_mb:.1f} MB)")
                success_count += 1
            else:
                print(f"   ❌ {exe_name} no encontrado")
        
        # Crear archivo README
        readme_content = f"""IGSON - Gestor de Archivos Corporativo
======================================

Release: {timestamp}
Fecha: {datetime.now().strftime("%d/%m/%Y %H:%M:%S")}

Ejecutables incluidos:
"""
        
        for exe_file in version_dir.glob("*.exe"):
            size_mb = exe_file.stat().st_size / (1024 * 1024)
            readme_content += f"- {exe_file.name} ({size_mb:.1f} MB)\n"
        
        readme_content += f"""
Instrucciones:
1. Ejecutar IGSON_License_Generator_v2.exe para generar licencias
2. Ejecutar IGSON_Validacion_Duplicados.exe para validar duplicados

Requisitos:
- Windows 10/11
- Acceso a red SMB para ubicaciones remotas
- Licencia válida para operación

Contacto: IGSON
Versión: 2.0
"""
        
        readme_file = version_dir / "README.txt"
        readme_file.write_text(readme_content, encoding='utf-8')
        
        print(f"\n🎉 Build completado:")
        print(f"   📂 Directorio: {version_dir}")
        print(f"   📊 Ejecutables: {success_count}/{len(self.apps_to_build)}")
        print(f"   📄 README: {readme_file.name}")
        
        return success_count > 0
    
    def run_build(self):
        """Ejecutar proceso completo de build"""
        print("🔨 IGSON - Build de Ejecutables")
        print("=" * 40)
        
        # Verificaciones previas
        if not self.check_pyinstaller():
            return False
        
        # Limpiar builds anteriores
        self.clean_previous_builds()
        
        # Build cada aplicación
        success_count = 0
        for app_id, config in self.apps_to_build.items():
            if self.build_app(app_id, config):
                success_count += 1
        
        # Organizar outputs
        if success_count > 0:
            self.organize_outputs()
        
        print(f"\n{'='*40}")
        print(f"🏁 Build finalizado: {success_count}/{len(self.apps_to_build)} exitosos")
        
        return success_count == len(self.apps_to_build)

def main():
    """Función principal"""
    builder = ExecutableBuilder()
    
    try:
        success = builder.run_build()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Build cancelado por usuario")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
