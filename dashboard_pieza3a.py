"""
dashboard_pieza3a.py
PIEZA 3A: Pestaña de Validación - UI y estructura
Gestor de Archivos Corporativo
"""

# CONTINUACIÓN DE LA CLASE GestorDashboard - PESTAÑA VALIDACIÓN

    def create_validacion_tab(self):
        """Crear pestaña de validación de duplicados avanzada"""
        validacion_frame = ttk.Frame(self.notebook)
        self.notebook.add(validacion_frame, text="🔍 Validación")
        
        # Header de la pestaña
        header_frame = ttk.Frame(validacion_frame)
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(header_frame, text="🔍 Validación de Duplicados", 
                 style='Title.TLabel').pack(side=tk.LEFT)
        
        # Botón de ayuda
        ttk.Button(header_frame, text="❓ Ayuda", 
                  command=self.show_validacion_help).pack(side=tk.RIGHT, padx=5)
        
        # Frame principal con scroll
        main_canvas = tk.<PERSON>(validacion_frame)
        scrollbar_val = ttk.Scrollbar(validacion_frame, orient="vertical", command=main_canvas.yview)
        scrollable_val_frame = ttk.Frame(main_canvas)
        
        scrollable_val_frame.bind(
            "<Configure>",
            lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all"))
        )
        
        main_canvas.create_window((0, 0), window=scrollable_val_frame, anchor="nw")
        main_canvas.configure(yscrollcommand=scrollbar_val.set)
        
        main_canvas.pack(side="left", fill="both", expand=True)
        scrollbar_val.pack(side="right", fill="y")
        
        # === SECCIÓN: CONFIGURACIÓN ===
        config_frame = ttk.LabelFrame(scrollable_val_frame, text="⚙️ Configuración de Validación", padding="15")
        config_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Grid para configuración
        config_grid = ttk.Frame(config_frame)
        config_grid.pack(fill=tk.X)
        
        # Carpeta origen
        ttk.Label(config_grid, text="📁 Carpeta Origen:", style='Status.TLabel').grid(row=0, column=0, sticky='w', pady=5)
        origen_frame = ttk.Frame(config_grid)
        origen_frame.grid(row=0, column=1, sticky='ew', padx=5)
        
        self.validacion_origen_entry = ttk.Entry(origen_frame, textvariable=self.validacion_origen_var, font=('Consolas', 9))
        self.validacion_origen_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Button(origen_frame, text="📂", width=3, 
                  command=self.select_validacion_origen).pack(side=tk.RIGHT, padx=(5,0))
        
        ttk.Button(origen_frame, text="👁️", width=3, 
                  command=self.preview_origen_folder).pack(side=tk.RIGHT, padx=(2,0))
        
        # Carpeta rechazados
        ttk.Label(config_grid, text="🗂️ Carpeta Rechazados:", style='Status.TLabel').grid(row=1, column=0, sticky='w', pady=5)
        rechazados_frame = ttk.Frame(config_grid)
        rechazados_frame.grid(row=1, column=1, sticky='ew', padx=5)
        
        self.validacion_rechazados_entry = ttk.Entry(rechazados_frame, textvariable=self.validacion_rechazados_var, font=('Consolas', 9))
        self.validacion_rechazados_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Button(rechazados_frame, text="📂", width=3, 
                  command=self.select_validacion_rechazados).pack(side=tk.RIGHT, padx=(5,0))
        
        ttk.Button(rechazados_frame, text="📁+", width=3, 
                  command=self.create_rechazados_folder).pack(side=tk.RIGHT, padx=(2,0))
        
        # Configurar grid weights
        config_grid.columnconfigure(1, weight=1)
        
        # Opciones avanzadas
        options_frame = ttk.LabelFrame(config_frame, text="🔧 Opciones Avanzadas", padding="10")
        options_frame.pack(fill=tk.X, pady=(15, 0))
        
        # Fila 1 de opciones
        options_row1 = ttk.Frame(options_frame)
        options_row1.pack(fill=tk.X, pady=2)
        
        ttk.Checkbutton(options_row1, text="🧪 Modo Simulación (Dry Run)", 
                       variable=self.validacion_dry_run_var).pack(side=tk.LEFT, padx=10)
        
        self.validacion_backup_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_row1, text="💾 Crear backup antes de mover", 
                       variable=self.validacion_backup_var).pack(side=tk.LEFT, padx=10)
        
        # Fila 2 de opciones
        options_row2 = ttk.Frame(options_frame)
        options_row2.pack(fill=tk.X, pady=2)
        
        self.validacion_log_detailed_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_row2, text="📝 Log detallado", 
                       variable=self.validacion_log_detailed_var).pack(side=tk.LEFT, padx=10)
        
        self.validacion_verify_integrity_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(options_row2, text="🔍 Verificar integridad de archivos", 
                       variable=self.validacion_verify_integrity_var).pack(side=tk.LEFT, padx=10)
        
        # Botones de configuración
        config_buttons = ttk.Frame(config_frame)
        config_buttons.pack(fill=tk.X, pady=(15, 0))
        
        ttk.Button(config_buttons, text="💾 Guardar Config", 
                  command=self.save_validacion_config).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(config_buttons, text="📂 Cargar Config", 
                  command=self.load_validacion_config).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(config_buttons, text="🔄 Restaurar Defecto", 
                  command=self.reset_validacion_config).pack(side=tk.LEFT, padx=5)
        
        # === SECCIÓN: EJECUCIÓN ===
        execution_frame = ttk.LabelFrame(scrollable_val_frame, text="🚀 Ejecución de Validación", padding="15")
        execution_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Botones principales
        main_buttons = ttk.Frame(execution_frame)
        main_buttons.pack(fill=tk.X, pady=(0, 10))
        
        self.validacion_execute_btn = ttk.Button(main_buttons, text="🚀 Ejecutar Validación", 
                                               style='Action.TButton', command=self.execute_validacion)
        self.validacion_execute_btn.pack(side=tk.LEFT, padx=5)
        
        self.validacion_stop_btn = ttk.Button(main_buttons, text="⏹️ Detener", 
                                            state='disabled', command=self.stop_validacion)
        self.validacion_stop_btn.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(main_buttons, text="🔍 Previsualizar", 
                  command=self.preview_validacion).pack(side=tk.LEFT, padx=5)
        
        # Progreso
        progress_frame = ttk.Frame(execution_frame)
        progress_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(progress_frame, text="Progreso:").pack(side=tk.LEFT)
        
        self.validacion_progress = ttk.Progressbar(progress_frame, mode='determinate')
        self.validacion_progress.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))
        
        self.validacion_progress_label = ttk.Label(progress_frame, text="0%")
        self.validacion_progress_label.pack(side=tk.RIGHT, padx=(10, 0))
        
        # === SECCIÓN: RESULTADOS ===
        results_frame = ttk.LabelFrame(scrollable_val_frame, text="📊 Resultados y Estadísticas", padding="15")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Notebook para diferentes vistas de resultados
        results_notebook = ttk.Notebook(results_frame)
        results_notebook.pack(fill=tk.BOTH, expand=True)
        
        # Tab: Resumen
        summary_tab = ttk.Frame(results_notebook)
        results_notebook.add(summary_tab, text="📋 Resumen")
        
        self.validacion_summary_text = tk.Text(summary_tab, height=6, wrap=tk.WORD, 
                                             font=('Consolas', 9), state='disabled')
        summary_scroll = ttk.Scrollbar(summary_tab, orient=tk.VERTICAL, 
                                     command=self.validacion_summary_text.yview)
        self.validacion_summary_text.configure(yscrollcommand=summary_scroll.set)
        
        self.validacion_summary_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        summary_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Tab: Log detallado
        log_tab = ttk.Frame(results_notebook)
        results_notebook.add(log_tab, text="📝 Log Detallado")
        
        self.validacion_log_text = tk.Text(log_tab, height=6, wrap=tk.WORD, 
                                         font=('Consolas', 8), state='disabled')
        log_scroll = ttk.Scrollbar(log_tab, orient=tk.VERTICAL, 
                                 command=self.validacion_log_text.yview)
        self.validacion_log_text.configure(yscrollcommand=log_scroll.set)
        
        self.validacion_log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Tab: Estadísticas
        stats_tab = ttk.Frame(results_notebook)
        results_notebook.add(stats_tab, text="📈 Estadísticas")
        
        self.validacion_stats_text = tk.Text(stats_tab, height=6, wrap=tk.WORD, 
                                           font=('Consolas', 9), state='disabled')
        stats_scroll = ttk.Scrollbar(stats_tab, orient=tk.VERTICAL, 
                                   command=self.validacion_stats_text.yview)
        self.validacion_stats_text.configure(yscrollcommand=stats_scroll.set)
        
        self.validacion_stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        stats_scroll.pack(side=tk.RIGHT, fill=tk.Y)
    
    def show_validacion_help(self):
        """Mostrar ayuda para validación"""
        help_window = tk.Toplevel(self.root)
        help_window.title("Ayuda - Validación de Duplicados")
        help_window.geometry("600x500")
        help_window.transient(self.root)
        
        help_text = tk.Text(help_window, wrap=tk.WORD, font=('Arial', 10))
        help_scroll = ttk.Scrollbar(help_window, orient=tk.VERTICAL, command=help_text.yview)
        help_text.configure(yscrollcommand=help_scroll.set)
        
        help_content = """
🔍 VALIDACIÓN DE DUPLICADOS - AYUDA

DESCRIPCIÓN:
La validación de duplicados busca archivos en la carpeta origen que ya existen en las ubicaciones QNAP y los mueve a una carpeta de rechazados.

CONFIGURACIÓN:
• Carpeta Origen: Donde están los archivos a verificar
• Carpeta Rechazados: Donde se moverán los duplicados encontrados

OPCIONES AVANZADAS:
• 🧪 Modo Simulación: Solo muestra qué haría sin realizar cambios
• 💾 Crear backup: Copia de seguridad antes de mover archivos
• 📝 Log detallado: Registro completo de todas las operaciones
• 🔍 Verificar integridad: Compara checksums de archivos

PROCESO:
1. Escanea archivos en carpeta origen
2. Busca duplicados en ubicaciones QNAP
3. Mueve duplicados a carpeta rechazados
4. Genera reporte detallado

RECOMENDACIONES:
• Use siempre modo simulación primero
• Verifique que las rutas sean correctas
• Revise los logs para confirmar resultados
        """
        
        help_text.insert(1.0, help_content.strip())
        help_text.config(state='disabled')
        
        help_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        help_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
        ttk.Button(help_window, text="Cerrar", command=help_window.destroy).pack(pady=10)

# FIN PIEZA 3A
