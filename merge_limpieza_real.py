"""
Script de Merge para GUI Limpieza Real
====================================

Combina las piezas de la GUI de limpieza con configuración real de ubicaciones
"""

import os
import shutil
from datetime import datetime

def merge_limpieza_real():
    """Merge las piezas de la GUI de limpieza real"""
    
    # Configuración de archivos
    pieces = [
        "limpieza_pieza1_real.py",
        "limpieza_pieza2_real.py", 
        "limpieza_pieza3_real.py",
        "limpieza_pieza4_real.py",
        "limpieza_pieza5_real.py"
    ]
    
    output_file = "limpieza_lista_gui_real.py"
    
    print("🔧 MERGE GUI LIMPIEZA REAL - GESTOR DE ARCHIVOS")
    print("=" * 55)
    print(f"📁 Directorio actual: {os.getcwd()}")
    print(f"📋 Piezas a procesar: {len(pieces)}")
    
    # Verificar que todas las piezas existen
    missing_pieces = []
    for piece in pieces:
        if not os.path.exists(piece):
            missing_pieces.append(piece)
    
    if missing_pieces:
        print(f"❌ ERROR: Piezas faltantes:")
        for piece in missing_pieces:
            print(f"  - {piece}")
        return False
    
    # Mostrar archivos encontrados
    for i, piece in enumerate(pieces, 1):
        size = os.path.getsize(piece) / 1024  # KB
        print(f"  {i}. {piece} ({size:.1f} KB)")
    
    # Crear backup si el archivo ya existe
    if os.path.exists(output_file):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"limpieza_gui_real_backup_{timestamp}.py"
        shutil.copy2(output_file, backup_file)
        print(f"💾 Backup creado: {backup_file}")
    
    print("🔄 Procesando archivos...")
    
    # Procesar archivos
    merged_content = []
    
    # Pieza 1: Archivo completo (base)
    print(f"  📄 Procesando {pieces[0]} (base completa)...")
    with open(pieces[0], 'r', encoding='utf-8') as f:
        content = f.read()
        merged_content.append(content)
    
    # Pieza 2: Solo métodos (quitar header y comentarios)
    if len(pieces) > 1 and os.path.exists(pieces[1]):
        print(f"  📄 Procesando {pieces[1]} (métodos)...")
        with open(pieces[1], 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Buscar líneas que empiecen con espacios (métodos de clase)
        method_lines = []
        in_method = False
        
        for line in lines:
            if line.strip().startswith('def ') and not line.startswith('def'):
                in_method = True
            
            if in_method:
                method_lines.append(line)
                
                # Si llegamos a una línea que no está indentada y no es comentario, salimos
                if line.strip() and not line.startswith('    ') and not line.startswith('#') and not line.startswith('"""'):
                    if not line.strip().startswith('def '):
                        in_method = False
        
        if method_lines:
            merged_content.append('\n'.join(method_lines))
    
    # Piezas 3, 4, 5: Solo métodos (quitar header y comentarios)
    for piece_index in range(2, len(pieces)):
        if os.path.exists(pieces[piece_index]):
            print(f"  📄 Procesando {pieces[piece_index]} (métodos)...")
            with open(pieces[piece_index], 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Para piezas 4 y 5 que son solo funciones, agregar directamente
            if piece_index >= 3:  # Piezas 4 y 5
                # Estas piezas contienen solo funciones, agregar con indentación de clase
                lines = content.split('\n')
                method_lines = []
                for line in lines:
                    if line.strip():  # Si no está vacía
                        # Agregar indentación de clase si no la tiene
                        if not line.startswith('    ') and not line.startswith('#') and not line.startswith('"""'):
                            method_lines.append('    ' + line)
                        else:
                            method_lines.append(line)
                    else:
                        method_lines.append(line)
                
                if method_lines:
                    merged_content.append('\n'.join(method_lines))
            
            else:  # Pieza 3 - lógica original
                lines = content.split('\n')
                method_lines = []
                in_method = False
                found_method = False
                
                for line in lines:
                    # Buscar métodos de clase
                    if line.strip().startswith('def ') and line.startswith('    '):
                        in_method = True
                        found_method = True
                    
                    # Buscar if __name__ == "__main__":
                    if line.strip().startswith('if __name__'):
                        in_method = True
                        found_method = True
                    
                    if in_method or found_method:
                        method_lines.append(line)
                
                if method_lines:
                    merged_content.append('\n'.join(method_lines))
    
    # Escribir archivo final
    print("💾 Escribiendo archivo final...")
    
    final_content = '\n'.join(merged_content)
    
    # Limpiar contenido duplicado
    final_content = clean_merged_content(final_content)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(final_content)
    
    # Estadísticas finales
    final_size = os.path.getsize(output_file)
    line_count = len(final_content.split('\n'))
    
    print("🎉 ¡MERGE COMPLETADO EXITOSAMENTE!")
    print(f"📄 Archivo generado: {output_file}")
    print(f"📏 Tamaño: {final_size / 1024:.1f} KB")
    print(f"📊 Líneas totales: {line_count}")
    
    return True

def clean_merged_content(content):
    """Limpiar contenido merged de duplicados y problemas"""
    
    lines = content.split('\n')
    cleaned_lines = []
    seen_methods = set()
    
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # Detectar métodos duplicados
        if line.strip().startswith('def ') and line.startswith('    '):
            method_name = line.strip().split('(')[0].replace('def ', '')
            
            if method_name in seen_methods:
                # Saltar método duplicado
                indent_level = len(line) - len(line.lstrip())
                i += 1
                while i < len(lines) and (lines[i].startswith(' ' * (indent_level + 1)) or lines[i].strip() == ''):
                    i += 1
                continue
            else:
                seen_methods.add(method_name)
        
        # Limpiar headers de continuación
        if '# CONTINUACIÓN DE LA CLASE' in line:
            i += 1
            continue
        
        # Limpiar imports duplicados innecesarios en piezas 2+
        if line.strip().startswith('import ') or line.strip().startswith('from '):
            if len(cleaned_lines) > 50:  # Si ya tenemos muchas líneas, probablemente ya tenemos los imports
                i += 1
                continue
        
        cleaned_lines.append(line)
        i += 1
    
    return '\n'.join(cleaned_lines)

def verify_syntax(filename):
    """Verificar sintaxis del archivo Python"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        compile(content, filename, 'exec')
        print(f"✅ Sintaxis verificada: {filename}")
        return True
    
    except SyntaxError as e:
        print(f"❌ Error de sintaxis en {filename}:")
        print(f"  Línea {e.lineno}: {e.text.strip() if e.text else 'N/A'}")
        print(f"  Error: {e.msg}")
        return False
    
    except Exception as e:
        print(f"❌ Error verificando {filename}: {e}")
        return False

if __name__ == "__main__":
    success = merge_limpieza_real()
    
    if success:
        print("\n🔍 Verificando sintaxis...")
        if verify_syntax("limpieza_lista_gui_real.py"):
            print("\n✅ MERGE EXITOSO - Archivo listo para usar")
            print("\n📋 Para probar:")
            print("  python limpieza_lista_gui_real.py")
        else:
            print("\n⚠️ MERGE COMPLETADO pero con errores de sintaxis")
            print("  Revisar archivo generado manualmente")
    else:
        print("\n❌ MERGE FALLÓ")
