def preview_list_content(self):
        """Previsualizar contenido del archivo de lista FASE 2 - Nombres base → archivos objetivo
        
        NUEVA FUNCIONALIDAD:
        - Muestra nombres base de la lista
        - Calcula archivos objetivo reales (.mxf/.mov)
        - Previsualiza el resultado final
        """
        try:
            archivo = self.archivo_lista.get()
            if not archivo or not os.path.exists(archivo):
                self.preview_text.delete(1.0, tk.END)
                self.preview_text.insert(tk.END, "❌ No se ha seleccionado un archivo válido\n")
                self.files_count_label.config(text="0 archivos")
                return
            
            self.log_message(f"🔍 Previsualizando archivo de nombres base: {archivo}")
            
            # Leer y procesar archivo
            with open(archivo, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # Procesar líneas
            valid_basenames = []
            invalid_lines = []
            total_target_files = []
            
            for i, line in enumerate(lines, 1):
                line = line.strip()
                if line and not line.startswith('#'):  # Ignorar líneas vacías y comentarios
                    if self.is_valid_video_basename(line):
                        valid_basenames.append(line)
                        # Obtener archivos objetivo para este basename
                        target_files = self.get_target_files_for_basename(line)
                        total_target_files.extend(target_files)
                    else:
                        invalid_lines.append(f"Línea {i}: {line}")
            
            # Mostrar previsualización mejorada
            self.preview_text.delete(1.0, tk.END)
            
            # Cabecera informativa FASE 2
            self.preview_text.insert(tk.END, "🎬 MODO VIDEOS (.mxf/.mov) - FASE 2\n")
            self.preview_text.insert(tk.END, "=" * 60 + "\n")
            self.preview_text.insert(tk.END, "Cada nombre base genera 2 archivos objetivo:\n")
            self.preview_text.insert(tk.END, "Ejemplo: '20826' → buscar '20826.mxf' y '20826.mov'\n\n")
            
            if valid_basenames:
                self.preview_text.insert(tk.END, f"✅ NOMBRES BASE VÁLIDOS ({len(valid_basenames)}):\n")
                self.preview_text.insert(tk.END, "=" * 50 + "\n")
                
                # Mostrar solo primeros 10 para evitar overflow
                display_count = min(10, len(valid_basenames))
                for i, basename in enumerate(valid_basenames[:display_count], 1):
                    target_files = self.get_target_files_for_basename(basename)
                    self.preview_text.insert(tk.END, f"{i:2d}. {basename} → {', '.join(target_files)}\n")
                
                if len(valid_basenames) > display_count:
                    remaining = len(valid_basenames) - display_count
                    self.preview_text.insert(tk.END, f"... y {remaining} nombres base más\n")
                
                # Estadísticas FASE 2
                self.preview_text.insert(tk.END, f"\n📊 ESTADÍSTICAS:\n")
                self.preview_text.insert(tk.END, f"📝 Nombres base: {len(valid_basenames)}\n")
                self.preview_text.insert(tk.END, f"🎬 Archivos objetivo: {len(total_target_files)}\n")
                self.preview_text.insert(tk.END, f"📁 Extensiones: .mxf, .mov\n")
                
                # Actualizar contador
                self.files_count_label.config(text=f"{len(valid_basenames)} nombres base → {len(total_target_files)} archivos objetivo")
            
            if invalid_lines:
                self.preview_text.insert(tk.END, f"\n❌ LÍNEAS INVÁLIDAS ({len(invalid_lines)}):\n")
                self.preview_text.insert(tk.END, "=" * 50 + "\n")
                self.preview_text.insert(tk.END, "Recuerda: Solo nombres base sin extensión\n")
                
                for invalid in invalid_lines[:5]:  # Mostrar solo primeras 5
                    self.preview_text.insert(tk.END, f"⚠️ {invalid}\n")
                
                if len(invalid_lines) > 5:
                    self.preview_text.insert(tk.END, f"... y {len(invalid_lines) - 5} líneas inválidas más\n")
            
            if not valid_basenames and not invalid_lines:
                self.preview_text.insert(tk.END, "⚠️ El archivo está vacío o solo contiene comentarios\n")
                self.files_count_label.config(text="0 archivos")
            
            self.log_message(f"📋 Previsualización completada: {len(valid_basenames)} nombres base → {len(total_target_files)} archivos objetivo")
            
        except Exception as e:
            self.preview_text.delete(1.0, tk.END)
            self.preview_text.insert(tk.END, f"❌ Error leyendo archivo: {e}\n")
            self.files_count_label.config(text="Error")
            self.log_message(f"❌ Error en previsualización: {e}")