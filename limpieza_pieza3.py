"""
GUI Limpieza por Lista - Pieza 3: Control UI + Config
==================================================
"""

def setup_ui_continued(self):
    """Continuar configuración UI - Controles"""
    # Frame de control
    control_frame = ttk.LabelFrame(main_frame, text="Control", padding="10")
    control_frame.pack(fill='x', pady=(10,0))
    
    # Botones de control
    button_frame = ttk.Frame(control_frame)
    button_frame.pack(fill='x')
    
    self.execute_btn = ttk.Button(button_frame, text="🗑️ Ejecutar Limpieza", 
                                 command=self.execute_cleanup, state='normal')
    self.execute_btn.pack(side='left', padx=(0,10))
    
    self.stop_btn = ttk.Button(button_frame, text="⏹ Detener", 
                              command=self.stop_cleanup, state='disabled')
    self.stop_btn.pack(side='left', padx=(0,10))
    
    ttk.Button(button_frame, text="📄 Previsualizar Lista", 
              command=self.preview_list).pack(side='left', padx=(0,10))
    
    # Barra de progreso general
    self.progress = ttk.Progressbar(control_frame, mode='determinate')
    self.progress.pack(fill='x', pady=(10,5))
    
    # Frame de progreso por ubicación
    self.progress_frame = ttk.Frame(control_frame)
    self.progress_frame.pack(fill='x', pady=5)
    
    self.location_progress = {}
    col = 0
    for location in self.ubicaciones.keys():
        frame = ttk.Frame(self.progress_frame)
        frame.grid(row=0, column=col, padx=5, sticky='ew')
        
        ttk.Label(frame, text=location, font=("Arial", 8)).pack()
        progress_bar = ttk.Progressbar(frame, mode='determinate', length=100)
        progress_bar.pack(fill='x')
        
        self.location_progress[location] = progress_bar
        col += 1
    
    # Frame de resultados
    results_frame = ttk.LabelFrame(main_frame, text="Resultados", padding="10")
    results_frame.pack(fill='both', expand=True, pady=(10,0))
    
    # Área de texto para logs
    self.log_text = scrolledtext.ScrolledText(results_frame, height=12, width=90)
    self.log_text.pack(fill='both', expand=True)
    
    # Frame de estadísticas
    stats_frame = ttk.Frame(results_frame)
    stats_frame.pack(fill='x', pady=(10,0))
    
    self.stats_label = ttk.Label(stats_frame, text="Listo para ejecutar", font=("Arial", 10))
    self.stats_label.pack(side='left')
    
    # Estado de licencia
    self.license_status = ttk.Label(stats_frame, text="Verificando licencia...", font=("Arial", 9))
    self.license_status.pack(side='right')

def load_default_config(self):
    """Cargar configuración por defecto para testing"""
    base_dir = os.path.join(current_dir, "test_limpieza")
    
    if not os.path.exists(base_dir):
        try:
            os.makedirs(base_dir, exist_ok=True)
            
            # Crear carpetas para ubicaciones
            location_dirs = {}
            for location in self.ubicaciones.keys():
                location_dir = os.path.join(base_dir, location.lower())
                os.makedirs(location_dir, exist_ok=True)
                location_dirs[location] = location_dir
                
                # Crear archivos de prueba en cada ubicación
                test_files = ["archivo1.txt", "documento2.pdf", "imagen3.jpg", "video4.mp4", "texto5.txt"]
                for file in test_files:
                    with open(os.path.join(location_dir, file), 'w') as f:
                        f.write(f"Contenido de {file} en {location}")
            
            # Crear archivo de lista de prueba
            lista_path = os.path.join(base_dir, "lista_archivos.txt")
            with open(lista_path, 'w') as f:
                files_to_delete = ["archivo1.txt", "documento2.pdf", "imagen3.jpg"]
                for file in files_to_delete:
                    f.write(f"{file}\n")
            
            self.archivo_lista.set(lista_path)
            
            # Configurar rutas de ubicaciones
            for location, location_dir in location_dirs.items():
                self.ubicaciones[location]["path"].set(location_dir)
            
            self.log_message("✅ Entorno de prueba creado")
            
        except Exception as e:
            self.log_message(f"❌ Error creando entorno: {e}")
    else:
        # Cargar configuración existente
        lista_path = os.path.join(base_dir, "lista_archivos.txt")
        if os.path.exists(lista_path):
            self.archivo_lista.set(lista_path)
        
        for location in self.ubicaciones.keys():
            location_dir = os.path.join(base_dir, location.lower())
            if os.path.exists(location_dir):
                self.ubicaciones[location]["path"].set(location_dir)

def log_message(self, message):
    """Agregar mensaje al log"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    formatted_message = f"[{timestamp}] {message}\n"
    
    self.log_text.insert(tk.END, formatted_message)
    self.log_text.see(tk.END)
    self.root.update_idletasks()
