"""
Location Configuration Widget
============================

Widget reutilizable para configuración de ubicaciones QNAPs/PCs
con detección automática y configuración manual
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
from pathlib import Path
import threading
import time
import subprocess
import socket
import platform

# Imports del sistema
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from src.core.config_manager import ConfigManager
    from src.handlers.qnap_handler import QnapHandler
    from src.handlers.pc_handler import PCHandler
    from src.core.auth_manager import AuthManager
except ImportError as e:
    print(f"Warning: Some modules not found: {e}")

class LocationConfigWidget:
    """Widget de configuración de ubicaciones"""
    
    def __init__(self, parent, config_manager=None, logger=None):
        self.parent = parent
        self.config = config_manager or ConfigManager()
        self.logger = logger
        
        # Variables de configuración
        self.locations_config = {}
        self.init_location_vars()
        
        # Handlers para testing
        self.handlers = {}
        
        self.create_widget()
        self.load_config()
    
    def init_location_vars(self):
        """Inicializar variables de ubicaciones"""
        for location_id in ['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4']:
            self.locations_config[location_id] = {
                'enabled': tk.BooleanVar(value=True),
                'path': tk.StringVar(),
                'hostname': tk.StringVar(),
                'username': tk.StringVar(),
                'password': tk.StringVar(),
                'domain': tk.StringVar(),
                'share': tk.StringVar(),
                'subfolder': tk.StringVar(),
                'status': tk.StringVar(value="Sin configurar"),
                'status_color': 'gray'
            }
    
    def create_widget(self):
        """Crear interfaz del widget"""
        # Frame principal
        self.main_frame = ttk.LabelFrame(self.parent, text="Configuración de Ubicaciones", padding="10")
        self.main_frame.pack(fill='both', expand=True, pady=(10,0))
        
        # Notebook para separar QNAPs y PCs
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill='both', expand=True)
        
        # Tab QNAPs
        self.qnap_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.qnap_frame, text="QNAPs (Auto-detección)")
        self.create_qnap_config()
        
        # Tab PCs
        self.pc_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.pc_frame, text="PCs (Configuración SMB)")
        self.create_pc_config()
        
        # Frame de control
        control_frame = ttk.Frame(self.main_frame)
        control_frame.pack(fill='x', pady=(10,0))
        
        ttk.Button(control_frame, text="🔍 Auto-detectar QNAPs", 
                  command=self.auto_detect_qnaps).pack(side='left', padx=(0,10))
        ttk.Button(control_frame, text="💾 Guardar Configuración", 
                  command=self.save_config).pack(side='left', padx=(0,10))
        ttk.Button(control_frame, text="🧪 Test Todas las Conexiones", 
                  command=self.test_all_connections).pack(side='left')
    
    def create_qnap_config(self):
        """Crear configuración para QNAPs"""
        ttk.Label(self.qnap_frame, text="Los QNAPs se detectan automáticamente en M:\\ y N:\\", 
                 font=('Arial', 9), foreground='blue').pack(anchor='w', pady=(0,10))
        
        for qnap_id in ['qnap1', 'qnap2']:
            drive_letter = 'M:' if qnap_id == 'qnap1' else 'N:'
            
            # Frame para cada QNAP
            qnap_group = ttk.LabelFrame(self.qnap_frame, text=f"{qnap_id.upper()} - {drive_letter}", padding="10")
            qnap_group.pack(fill='x', pady=(0,10))
            
            # Enabled checkbox
            ttk.Checkbutton(qnap_group, text="Habilitado", 
                           variable=self.locations_config[qnap_id]['enabled']).grid(row=0, column=0, sticky='w')
            
            # Status
            status_label = ttk.Label(qnap_group, textvariable=self.locations_config[qnap_id]['status'])
            status_label.grid(row=0, column=1, padx=(20,0), sticky='w')
            
            # Path automático
            ttk.Label(qnap_group, text="Ruta:").grid(row=1, column=0, sticky='w', pady=(5,0))
            path_entry = ttk.Entry(qnap_group, textvariable=self.locations_config[qnap_id]['path'], width=40)
            path_entry.grid(row=1, column=1, padx=(10,0), pady=(5,0))
            
            # Subcarpeta
            ttk.Label(qnap_group, text="Subcarpeta:").grid(row=2, column=0, sticky='w', pady=(5,0))
            ttk.Entry(qnap_group, textvariable=self.locations_config[qnap_id]['subfolder'], width=40).grid(row=2, column=1, padx=(10,0), pady=(5,0))
            
            # Botón test individual
            test_btn = ttk.Button(qnap_group, text="🧪 Test", 
                                 command=lambda qid=qnap_id: self.test_connection(qid))
            test_btn.grid(row=1, column=2, padx=(10,0), pady=(5,0))
            
            # Configurar path por defecto
            self.locations_config[qnap_id]['path'].set(f"{drive_letter}\\")
    
    def create_pc_config(self):
        """Crear configuración para PCs"""
        ttk.Label(self.pc_frame, text="Configure los PCs con credenciales SMB específicas", 
                 font=('Arial', 9), foreground='blue').pack(anchor='w', pady=(0,10))
        
        # ScrolledFrame para PCs con soporte de mouse wheel
        canvas = tk.Canvas(self.pc_frame)
        scrollbar = ttk.Scrollbar(self.pc_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        # Configurar scroll con mouse wheel
        def on_mouse_wheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Bind mouse wheel events
        canvas.bind("<MouseWheel>", on_mouse_wheel)
        scrollable_frame.bind("<MouseWheel>", on_mouse_wheel)
        
        # Enable mouse wheel when mouse enters canvas
        def bind_to_mousewheel(event):
            canvas.bind_all("<MouseWheel>", on_mouse_wheel)
        
        def unbind_from_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")
        
        canvas.bind('<Enter>', bind_to_mousewheel)
        canvas.bind('<Leave>', unbind_from_mousewheel)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        for i, pc_id in enumerate(['pc1', 'pc2', 'pc3', 'pc4']):
            # Frame para cada PC
            pc_group = ttk.LabelFrame(scrollable_frame, text=f"{pc_id.upper()}", padding="10")
            pc_group.pack(fill='x', pady=(0,10))
            
            # Row 0: Enabled + Status
            ttk.Checkbutton(pc_group, text="Habilitado", 
                           variable=self.locations_config[pc_id]['enabled']).grid(row=0, column=0, sticky='w')
            
            status_label = ttk.Label(pc_group, textvariable=self.locations_config[pc_id]['status'])
            status_label.grid(row=0, column=1, columnspan=2, padx=(20,0), sticky='w')
            
            # Row 1: Hostname/IP
            ttk.Label(pc_group, text="Hostname/IP:").grid(row=1, column=0, sticky='w', pady=(5,0))
            ttk.Entry(pc_group, textvariable=self.locations_config[pc_id]['hostname'], width=20).grid(row=1, column=1, padx=(10,0), pady=(5,0))
            
            # Row 2: Share
            ttk.Label(pc_group, text="Share:").grid(row=2, column=0, sticky='w', pady=(5,0))
            ttk.Entry(pc_group, textvariable=self.locations_config[pc_id]['share'], width=20).grid(row=2, column=1, padx=(10,0), pady=(5,0))
            
            # Row 3: Username
            ttk.Label(pc_group, text="Usuario:").grid(row=3, column=0, sticky='w', pady=(5,0))
            ttk.Entry(pc_group, textvariable=self.locations_config[pc_id]['username'], width=20).grid(row=3, column=1, padx=(10,0), pady=(5,0))
            
            # Row 4: Password
            ttk.Label(pc_group, text="Contraseña:").grid(row=4, column=0, sticky='w', pady=(5,0))
            ttk.Entry(pc_group, textvariable=self.locations_config[pc_id]['password'], width=20, show='*').grid(row=4, column=1, padx=(10,0), pady=(5,0))
            
            # Row 5: Domain
            ttk.Label(pc_group, text="Dominio:").grid(row=5, column=0, sticky='w', pady=(5,0))
            ttk.Entry(pc_group, textvariable=self.locations_config[pc_id]['domain'], width=20).grid(row=5, column=1, padx=(10,0), pady=(5,0))
            
            # Row 6: Subfolder
            ttk.Label(pc_group, text="Subcarpeta:").grid(row=6, column=0, sticky='w', pady=(5,0))
            ttk.Entry(pc_group, textvariable=self.locations_config[pc_id]['subfolder'], width=30).grid(row=6, column=1, padx=(10,0), pady=(5,0))
            
            # Botón test individual
            test_btn = ttk.Button(pc_group, text="🧪 Test", 
                                 command=lambda pid=pc_id: self.test_connection(pid))
            test_btn.grid(row=1, column=2, rowspan=2, padx=(10,0), pady=(5,0))
            
            # Valores por defecto
            self.locations_config[pc_id]['hostname'].set(f"192.168.1.{10+i}")
            self.locations_config[pc_id]['share'].set("shared")
            self.locations_config[pc_id]['domain'].set("WORKGROUP")
            self.locations_config[pc_id]['subfolder'].set("work")
    
    def auto_detect_qnaps(self):
        """Auto-detección de QNAPs"""
        def detect_thread():
            try:
                self.log_message("🔍 Iniciando auto-detección de QNAPs...")
                
                # Detectar QNAP1 (M:)
                qnap1_path = Path("M:\\")
                if qnap1_path.exists():
                    self.locations_config['qnap1']['status'].set("✅ Detectado automáticamente")
                    self.locations_config['qnap1']['path'].set("M:\\")
                    self.log_message("✅ QNAP1 detectado en M:\\")
                else:
                    self.locations_config['qnap1']['status'].set("❌ No detectado")
                    self.log_message("❌ QNAP1 no detectado en M:\\")
                
                # Detectar QNAP2 (N:)
                qnap2_path = Path("N:\\")
                if qnap2_path.exists():
                    self.locations_config['qnap2']['status'].set("✅ Detectado automáticamente")
                    self.locations_config['qnap2']['path'].set("N:\\")
                    self.log_message("✅ QNAP2 detectado en N:\\")
                else:
                    self.locations_config['qnap2']['status'].set("❌ No detectado")
                    self.log_message("❌ QNAP2 no detectado en N:\\")
                
                self.log_message("🔍 Auto-detección completada")
                
            except Exception as e:
                self.log_message(f"❌ Error en auto-detección: {e}")
        
        thread = threading.Thread(target=detect_thread)
        thread.daemon = True
        thread.start()
    
    def ping_host(self, hostname, timeout=3):
        """Test de ping con timeout corto"""
        try:
            if platform.system().lower() == "windows":
                # Windows
                cmd = f"ping -n 1 -w {timeout*1000} {hostname}"
            else:
                # Linux/Mac
                cmd = f"ping -c 1 -W {timeout} {hostname}"
            
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout+1)
            return result.returncode == 0
            
        except (subprocess.TimeoutExpired, Exception) as e:
            self.log_message(f"❌ Ping timeout/error para {hostname}: {e}")
            return False
    
    def test_smb_connection(self, hostname, share, username="", timeout=3):
        """Test de conexión SMB básica"""
        try:
            # Test 1: Puerto 445 (SMB)
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((hostname, 445))
            sock.close()
            
            if result == 0:
                self.log_message(f"✅ Puerto SMB 445 abierto en {hostname}")
                return True
            else:
                self.log_message(f"❌ Puerto SMB 445 cerrado en {hostname}")
                return False
                
        except Exception as e:
            self.log_message(f"❌ Error test SMB {hostname}: {e}")
            return False
    
    def test_network_path(self, network_path, timeout=3):
        """Test de acceso a ruta de red"""
        try:
            if not network_path or not network_path.startswith('\\\\'):
                return False
            
            # Extraer hostname de la ruta de red
            parts = network_path.replace('\\\\', '').split('\\')
            if len(parts) < 2:
                return False
            
            hostname = parts[0]
            share = parts[1]
            
            # Test ping primero
            if not self.ping_host(hostname, timeout):
                return False
            
            # Test SMB
            return self.test_smb_connection(hostname, share, timeout=timeout)
            
        except Exception as e:
            self.log_message(f"❌ Error test ruta de red {network_path}: {e}")
            return False
    
    def test_location_connectivity(self, location_id):
        """Test de conectividad REAL para una ubicación específica"""
        try:
            config = self.locations_config[location_id]
            
            if not config['enabled'].get():
                return "⚪ Deshabilitado"
            
            if location_id.startswith('qnap'):
                # QNAP: Test ruta local o de red
                path = config['path'].get()
                
                if not path:
                    return "❓ Sin configurar"
                
                self.log_message(f"🧪 Testing QNAP {location_id}: {path}")
                
                # Si es ruta local (M:\, N:\)
                if path.endswith(':\\') and len(path) == 3:
                    if Path(path).exists():
                        return "✅ Conectado"
                    else:
                        return "❌ Error red"
                
                # Si es ruta de red
                elif path.startswith('\\\\'):
                    if self.test_network_path(path, timeout=3):
                        return "✅ Conectado"
                    else:
                        return "❌ Error red"
                
                # Ruta local normal
                else:
                    if Path(path).exists():
                        return "✅ Conectado"
                    else:
                        return "❌ Error red"
            
            else:
                # PC: Test SMB REAL
                hostname = config['hostname'].get()
                share = config['share'].get()
                username = config['username'].get()
                
                if not hostname or not share:
                    return "❓ Sin configurar"
                
                self.log_message(f"🧪 Testing PC {location_id}: {hostname}/{share}")
                
                # Test 1: Ping
                if not self.ping_host(hostname, timeout=3):
                    return "❌ Error red"
                
                # Test 2: SMB
                if self.test_smb_connection(hostname, share, username, timeout=3):
                    return "✅ Conectado"
                else:
                    return "🔐 Error credenciales"
        
        except Exception as e:
            self.log_message(f"❌ Error testing {location_id}: {e}")
            return "❌ Error"
    
    
    def test_connection(self, location_id):
        """Test de conexión individual con validación REAL"""
        def test_thread():
            try:
                self.log_message(f"🧪 Testing conexión a {location_id.upper()}...")
                
                # Obtener configuración
                config = self.locations_config[location_id]
                
                if not config['enabled'].get():
                    config['status'].set("⚪ Deshabilitado")
                    return
                
                config['status'].set("🔄 Testing...")
                
                # Test REAL de conectividad
                status = self.test_location_connectivity(location_id)
                
                # Actualizar estado final
                config['status'].set(status)
                
                # Log detallado
                if status == "✅ Conectado":
                    self.log_message(f"✅ {location_id.upper()}: CONECTADO exitosamente")
                elif status == "❌ Error red":
                    self.log_message(f"❌ {location_id.upper()}: No se puede acceder (ping/puerto)")
                elif status == "🔐 Error credenciales":
                    self.log_message(f"🔐 {location_id.upper()}: Credenciales incorrectas o permisos")
                elif status == "❓ Sin configurar":
                    self.log_message(f"❓ {location_id.upper()}: Faltan datos de configuración")
                
            except Exception as e:
                config['status'].set("❌ Error")
                self.log_message(f"❌ Error testing {location_id}: {e}")
        
        thread = threading.Thread(target=test_thread)
        thread.daemon = True
        thread.start()
    
    def test_all_connections(self):
        """Test REAL de todas las conexiones - SIN FALSOS POSITIVOS"""
        def test_all_thread():
            try:
                self.log_message("🧪 INICIANDO TEST REAL DE TODAS LAS CONEXIONES...")
                self.log_message("⚠️ Se realizarán pings y tests SMB reales")
                
                # Test QNAPs con validación real
                for qnap_id in ['qnap1', 'qnap2']:
                    if self.locations_config[qnap_id]['enabled'].get():
                        # Mostrar estado "Testing..."
                        self.locations_config[qnap_id]['status'].set("🔄 Testing...")
                        
                        # Test real de conectividad
                        status = self.test_location_connectivity(qnap_id)
                        
                        # Actualizar estado final
                        self.locations_config[qnap_id]['status'].set(status)
                        
                        # Log detallado
                        if status == "✅ Conectado":
                            self.log_message(f"✅ {qnap_id.upper()}: CONECTADO exitosamente")
                        elif status == "❌ Error red":
                            self.log_message(f"❌ {qnap_id.upper()}: No se puede acceder")
                        elif status == "❓ Sin configurar":
                            self.log_message(f"❓ {qnap_id.upper()}: Faltan datos de configuración")
                        
                        time.sleep(0.3)
                
                # Test PCs con validación SMB real
                for pc_id in ['pc1', 'pc2', 'pc3', 'pc4']:
                    if self.locations_config[pc_id]['enabled'].get():
                        # Mostrar estado "Testing..."
                        self.locations_config[pc_id]['status'].set("🔄 Testing...")
                        
                        # Test real de conectividad
                        status = self.test_location_connectivity(pc_id)
                        
                        # Actualizar estado final
                        self.locations_config[pc_id]['status'].set(status)
                        
                        # Log detallado
                        if status == "✅ Conectado":
                            self.log_message(f"✅ {pc_id.upper()}: CONECTADO exitosamente")
                        elif status == "❌ Error red":
                            self.log_message(f"❌ {pc_id.upper()}: No se puede acceder (ping/puerto)")
                        elif status == "🔐 Error credenciales":
                            self.log_message(f"🔐 {pc_id.upper()}: Credenciales incorrectas o permisos")
                        elif status == "❓ Sin configurar":
                            self.log_message(f"❓ {pc_id.upper()}: Faltan datos de configuración")
                        
                        time.sleep(0.3)
                
                # Resumen final
                conectados = sum(1 for loc_id in ['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4'] 
                               if self.locations_config[loc_id]['status'].get() == "✅ Conectado")
                
                self.log_message("=" * 50)
                self.log_message(f"🏁 TEST COMPLETADO: {conectados}/6 ubicaciones conectadas")
                self.log_message("=" * 50)
                
            except Exception as e:
                self.log_message(f"❌ Error en test de conectividad: {e}")
        
        thread = threading.Thread(target=test_all_thread)
        thread.daemon = True
        thread.start()
    
    def save_config(self):
        """Guardar configuración"""
        try:
            self.log_message("💾 Guardando configuración...")
            
            # Guardar en config manager
            for location_id, config_vars in self.locations_config.items():
                # Configuración básica
                self.config.set(f"locations.{location_id}.enabled", config_vars['enabled'].get())
                
                if location_id.startswith('qnap'):
                    # QNAPs: solo path y subcarpeta
                    self.config.set(f"locations.{location_id}.network_path", config_vars['path'].get())
                    self.config.set(f"locations.{location_id}.subfolder", config_vars['subfolder'].get())
                else:
                    # PCs: configuración SMB completa
                    hostname = config_vars['hostname'].get()
                    share = config_vars['share'].get()
                    username = config_vars['username'].get()
                    password = config_vars['password'].get()
                    domain = config_vars['domain'].get()
                    subfolder = config_vars['subfolder'].get()
                    
                    # Guardar todos los campos
                    self.config.set(f"locations.{location_id}.hostname", hostname)
                    self.config.set(f"locations.{location_id}.share", share)
                    self.config.set(f"locations.{location_id}.username", username)
                    self.config.set(f"locations.{location_id}.password", password)
                    self.config.set(f"locations.{location_id}.domain", domain)
                    self.config.set(f"locations.{location_id}.subfolder", subfolder)
                    
                    # Network path para compatibilidad
                    network_path = f"\\\\{hostname}\\{share}" if hostname and share else ""
                    self.config.set(f"locations.{location_id}.network_path", network_path)
            
            # Guardar archivo
            if self.config.save_config():
                self.log_message("✅ Configuración guardada exitosamente")
                messagebox.showinfo("Éxito", "Configuración guardada correctamente")
            else:
                self.log_message("❌ Error guardando configuración")
                messagebox.showerror("Error", "Error guardando configuración")
                
        except Exception as e:
            self.log_message(f"❌ Error guardando: {e}")
            messagebox.showerror("Error", f"Error guardando configuración: {e}")
    
    def load_config(self):
        """Cargar configuración existente"""
        try:
            self.log_message("📖 Cargando configuración existente...")
            
            locations = self.config.get_locations()
            
            for location_id, config_vars in self.locations_config.items():
                if location_id in locations:
                    location_config = locations[location_id]
                    
                    # Configuración básica
                    config_vars['enabled'].set(location_config.get('enabled', True))
                    config_vars['path'].set(location_config.get('network_path', ''))
                    config_vars['subfolder'].set(location_config.get('subfolder', ''))
                    
                    if not location_id.startswith('qnap'):
                        # Credenciales directas para PCs
                        config_vars['hostname'].set(location_config.get('hostname', ''))
                        config_vars['share'].set(location_config.get('share', ''))
                        config_vars['username'].set(location_config.get('username', ''))
                        config_vars['password'].set(location_config.get('password', ''))
                        config_vars['domain'].set(location_config.get('domain', ''))
            
            self.log_message("✅ Configuración cargada")
            
            # Auto-test conexiones si hay configuración cargada
            if any(locations):
                self.log_message("🧪 Auto-testing conexiones...")
                # Usar after para ejecutar después de que la UI esté lista
                self.parent.after(1500, self.test_all_connections)
            
        except Exception as e:
            self.log_message(f"❌ Error cargando configuración: {e}")
    
    def get_enabled_locations(self):
        """Obtener ubicaciones habilitadas"""
        enabled = {}
        
        for location_id, config_vars in self.locations_config.items():
            if config_vars['enabled'].get():
                enabled[location_id] = {
                    'path': config_vars['path'].get(),
                    'subfolder': config_vars['subfolder'].get(),
                    'hostname': config_vars.get('hostname', tk.StringVar()).get() if not location_id.startswith('qnap') else '',
                    'share': config_vars.get('share', tk.StringVar()).get() if not location_id.startswith('qnap') else '',
                    'username': config_vars.get('username', tk.StringVar()).get() if not location_id.startswith('qnap') else '',
                    'domain': config_vars.get('domain', tk.StringVar()).get() if not location_id.startswith('qnap') else ''
                }
        
        return enabled
    
    def log_message(self, message):
        """Log message (override in parent)"""
        if self.logger:
            self.logger.info(message)
        else:
            print(f"[LocationConfig] {message}")

if __name__ == "__main__":
    # Test standalone
    root = tk.Tk()
    root.title("Test - Location Config Widget")
    root.geometry("800x600")
    
    widget = LocationConfigWidget(root)
    
    # Test logging
    def test_log(msg):
        print(f"TEST LOG: {msg}")
    
    widget.log_message = test_log
    
    root.mainloop()
