```python
import re
from pathlib import Path
import shutil
import logging

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def fix_dashboard_file(input_path: str, output_path: str = None, backup: bool = True):
    """
    Corrige las duplicidades del método main() en dashboard.py y escribe el resultado.
    
    Args:
        input_path: Ruta al archivo dashboard.py original
        output_path: Ruta para el archivo corregido (si None, sobrescribe input_path)
        backup: Crear copia de seguridad del archivo original
    """
    input_file = Path(input_path)
    
    if not input_file.exists():
        logger.error(f"Archivo {input_path} no encontrado")
        return False
    
    # Crear copia de seguridad
    if backup:
        backup_path = input_file.with_suffix('.bak')
        try:
            shutil.copy2(input_file, backup_path)
            logger.info(f"Copia de seguridad creada en {backup_path}")
        except Exception as e:
            logger.error(f"Error creando copia de seguridad: {e}")
            return False
    
    # Leer contenido del archivo
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        logger.error(f"Error leyendo archivo {input_path}: {e}")
        return False
    
    # Definir el contenido corregido del método main()
    corrected_main = """
# === MÉTODOS ESTÁTICOS Y UTILITARIOS ===

def main():
    """Función principal para ejecutar el dashboard"""
    import sys
    
    # Configurar logging básico
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger('dashboard.main')
    
    try:
        logger.info("=== INICIANDO GESTOR DE ARCHIVOS CORPORATIVO ===")
        
        # Verificar dependencias críticas
        try:
            import tkinter
            import threading
            from pathlib import Path
        except ImportError as e:
            logger.error(f"Dependencia faltante: {e}")
            print(f"❌ Error: Dependencia faltante - {e}")
            input("Presione Enter para salir...")
            sys.exit(1)
        
        # Crear y ejecutar dashboard
        dashboard = GestorDashboard()
        dashboard.run()
        
    except KeyboardInterrupt:
        logger.info("Aplicación interrumpida por el usuario (Ctrl+C)")
        print("\n🛑 Aplicación detenida por el usuario")
        
    except Exception as e:
        logger.error(f"Error fatal en main: {e}")
        print(f"❌ Error fatal: {e}")
        
        # Mostrar información de debug si es necesario
        if "--debug" in sys.argv:
            import traceback
            print("\n🔍 Información de debug:")
            traceback.print_exc()
        
        input("Presione Enter para salir...")
        sys.exit(1)

if __name__ == "__main__":
    main()
"""
    
    # Buscar el marcador de inicio de la sección a corregir
    marker = r'# === MÉTODOS ESTÁTICOS Y UTILITARIOS ===\s*'
    
    # Dividir el contenido en la parte antes del marcador y la parte a reemplazar
    match = re.search(marker, content, re.DOTALL)
    if not match:
        logger.error("No se encontró el marcador '# === MÉTODOS ESTÁTICOS Y UTILITARIOS ==='")
        return False
    
    # Conservar todo el contenido antes del marcador
    content_before = content[:match.start()]
    
    # Combinar el contenido antes del marcador con la versión corregida
    corrected_content = content_before.strip() + "\n\n" + corrected_main.strip() + "\n"
    
    # Determinar ruta de salida
    output_file = Path(output_path) if output_path else input_file
    
    # Escribir el archivo corregido
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(corrected_content)
        logger.info(f"Archivo corregido guardado en {output_file}")
        return True
    except Exception as e:
        logger.error(f"Error escribiendo archivo {output_file}: {e}")
        return False

def main():
    """Ejecutar la corrección del archivo dashboard.py"""
    input_path = "dashboard.py"
    # output_path = "dashboard_corrected.py"  # Descomentar para guardar en un nuevo archivo
    output_path = None  # Sobrescribe el archivo original
    
    logger.info("Iniciando corrección automática de dashboard.py")
    success = fix_dashboard_file(input_path, output_path, backup=True)
    
    if success:
        logger.info("Corrección completada exitosamente")
    else:
        logger.error("La corrección falló. Revise los logs para más detalles.")

if __name__ == "__main__":
    main()
```