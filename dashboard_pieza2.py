"""
dashboard_pieza2.py
PIEZA 2: Conectividad real avanzada + dashboard tab + threads
Gestor de Archivos Corporativo
"""

# CONTINUACIÓN DE LA CLASE GestorDashboard

    def setup_threads(self):
        """Configurar threads para auto-actualización"""
        # Thread para auto-refresh de logs
        self.log_refresh_thread = threading.Thread(target=self.auto_refresh_logs_worker, daemon=True)
        self.log_refresh_thread.start()
        
        # Thread para estado de conectividad periódico
        self.connectivity_thread = threading.Thread(target=self.connectivity_worker, daemon=True)
        self.connectivity_thread.start()
        
        # Thread para actualización de estadísticas
        self.stats_thread = threading.Thread(target=self.stats_worker, daemon=True)
        self.stats_thread.start()
        
        self.logger.info("Threads de background iniciados")
    
    def auto_refresh_logs_worker(self):
        """Worker para auto-actualización de logs"""
        while not self.shutdown_threads:
            try:
                time.sleep(5)  # Actualizar cada 5 segundos
                if self.auto_refresh_logs.get():
                    self.root.after(0, self.refresh_logs)
            except Exception as e:
                self.logger.error(f"Error en auto-refresh de logs: {e}")
                time.sleep(10)  # Esperar más tiempo si hay error
    
    def connectivity_worker(self):
        """Worker para monitoreo periódico de conectividad"""
        while not self.shutdown_threads:
            try:
                time.sleep(60)  # Verificar cada 60 segundos
                self.root.after(0, self.periodic_connectivity_check)
            except Exception as e:
                self.logger.error(f"Error en monitoreo de conectividad: {e}")
                time.sleep(30)
    
    def stats_worker(self):
        """Worker para actualización de estadísticas"""
        while not self.shutdown_threads:
            try:
                time.sleep(30)  # Actualizar cada 30 segundos
                self.root.after(0, self.update_system_stats)
            except Exception as e:
                self.logger.error(f"Error en stats worker: {e}")
                time.sleep(60)
    
    def initial_connectivity_check(self):
        """Check inicial de conectividad al arrancar"""
        self.update_status("Realizando check inicial de conectividad...", "info")
        
        def initial_check_worker():
            try:
                time.sleep(2)  # Esperar que la UI esté lista
                self.root.after(0, self.test_all_connections_silent)
            except Exception as e:
                self.logger.error(f"Error en check inicial: {e}")
        
        threading.Thread(target=initial_check_worker, daemon=True).start()
    
    def periodic_connectivity_check(self):
        """Check periódico de conectividad (menos intrusivo)"""
        try:
            from core.config_manager import config_manager
            
            locations = config_manager.get_enabled_locations()
            
            for location_id in locations:
                if location_id in getattr(self, 'connectivity_labels', {}):
                    # Solo actualizar si no está siendo testeado manualmente
                    current_text = self.connectivity_labels[location_id].cget('text')
                    if "🔄" not in current_text:  # No está en test manual
                        self.test_single_location_background(location_id)
            
            self.update_connection_counter()
            
        except Exception as e:
            self.logger.error(f"Error en check periódico: {e}")
    
    def test_single_location_background(self, location_id: str):
        """Test de una ubicación en background (no bloquea UI)"""
        def bg_test_worker():
            try:
                result = self.test_location_connectivity_real(location_id)
                self.root.after(0, lambda: self.update_location_status_quiet(location_id, result))
            except Exception as e:
                self.logger.debug(f"Error en test background {location_id}: {e}")
        
        threading.Thread(target=bg_test_worker, daemon=True).start()
    
    def update_location_status_quiet(self, location_id: str, result: Dict[str, Any]):
        """Actualizar estado de ubicación sin interrumpir UI"""
        try:
            if location_id not in getattr(self, 'connectivity_labels', {}):
                return
            
            status_label = self.connectivity_labels[location_id]
            details_label = getattr(self, 'connectivity_details', {}).get(location_id)
            
            # Actualizar estado interno
            self.connectivity_status[location_id] = result
            
            # Actualizar UI solo si cambió el estado
            current_text = status_label.cget('text')
            new_online = result['connected']
            current_online = "🟢" in current_text
            
            if new_online != current_online:  # Solo si cambió el estado
                if new_online:
                    status_label.config(text="🟢 ONLINE", foreground='#2E7D32')
                    if details_label:
                        details = result.get('details', 'Conectado')
                        details_label.config(text=details, foreground='#2E7D32')
                else:
                    status_label.config(text="🔴 OFFLINE", foreground='#C62828')
                    if details_label:
                        error_text = result.get('details', result.get('error', 'Error'))
                        details_label.config(text=error_text, foreground='#C62828')
                
                self.update_connection_counter()
        
        except Exception as e:
            self.logger.error(f"Error actualizando estado quiet {location_id}: {e}")
    
    def create_tabs(self):
        """Crear todas las pestañas"""
        self.create_dashboard_tab()
        self.create_validacion_tab()
        self.create_limpieza_tab()
        self.create_logs_tab()
        
        # Seleccionar la primera pestaña
        self.notebook.select(0)
    
    def create_dashboard_tab(self):
        """Crear pestaña de dashboard principal con conectividad avanzada"""
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="📊 Dashboard")
        
        # Frame principal con scroll
        main_canvas = tk.Canvas(dashboard_frame)
        scrollbar = ttk.Scrollbar(dashboard_frame, orient="vertical", command=main_canvas.yview)
        scrollable_frame = ttk.Frame(main_canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all"))
        )
        
        main_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        main_canvas.configure(yscrollcommand=scrollbar.set)
        
        # Pack scroll components
        main_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # === SECCIÓN: CONECTIVIDAD AVANZADA ===
        connectivity_frame = ttk.LabelFrame(scrollable_frame, text="🌐 Estado de Conectividad en Tiempo Real", padding="15")
        connectivity_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Controles superiores
        controls_frame = ttk.Frame(connectivity_frame)
        controls_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Button(controls_frame, text="🔍 Test Todas", style='Action.TButton',
                  command=self.test_all_connections).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(controls_frame, text="🔄 Actualizar", 
                  command=self.update_connectivity_status).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(controls_frame, text="📊 Reporte Detallado", 
                  command=self.show_detailed_report).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(controls_frame, text="⚙️ Configurar Conexiones", 
                  command=self.show_connection_config).pack(side=tk.RIGHT, padx=5)
        
        # Grid de ubicaciones mejorado
        self.connectivity_labels = {}
        self.connectivity_details = {}
        self.connectivity_buttons = {}
        
        locations_info = {
            'qnap1': {'name': 'QNAP 1', 'type': 'NAS', 'icon': '🏢'},
            'qnap2': {'name': 'QNAP 2', 'type': 'NAS', 'icon': '🏢'},
            'pc1': {'name': 'PC Oficina 1', 'type': 'PC', 'icon': '💻'},
            'pc2': {'name': 'PC Oficina 2', 'type': 'PC', 'icon': '💻'},
            'pc3': {'name': 'PC Remoto 1', 'type': 'PC', 'icon': '💻'},
            'pc4': {'name': 'PC Remoto 2', 'type': 'PC', 'icon': '💻'}
        }
        
        for i, (location_id, info) in enumerate(locations_info.items()):
            row = i // 2
            col = i % 2
            
            # Frame individual por ubicación
            loc_frame = ttk.LabelFrame(connectivity_frame, text=f"{info['icon']} {info['name']}", padding="10")
            loc_frame.grid(row=row, column=col, padx=10, pady=10, sticky='ew')
            
            # Fila 1: Estado y botón test
            status_row = ttk.Frame(loc_frame)
            status_row.pack(fill=tk.X, pady=(0, 5))
            
            status_label = ttk.Label(status_row, text="🔄 Verificando...", style='Testing.TLabel')
            status_label.pack(side=tk.LEFT)
            
            test_btn = ttk.Button(status_row, text="Test", width=8,
                                 command=lambda loc=location_id: self.test_individual_connection(loc))
            test_btn.pack(side=tk.RIGHT)
            
            # Fila 2: Detalles técnicos
            details_label = ttk.Label(loc_frame, text="Esperando conexión...", 
                                    font=('Arial', 8), foreground='gray')
            details_label.pack(anchor=tk.W)
            
            # Fila 3: Información adicional
            extra_info = ttk.Label(loc_frame, text=f"Tipo: {info['type']} | ID: {location_id}", 
                                 font=('Arial', 7), foreground='lightgray')
            extra_info.pack(anchor=tk.W, pady=(2, 0))
            
            # Guardar referencias
            self.connectivity_labels[location_id] = status_label
            self.connectivity_details[location_id] = details_label
            self.connectivity_buttons[location_id] = test_btn
        
        # Configurar grid weights para responsive design
        for col in range(2):
            connectivity_frame.columnconfigure(col, weight=1)
        
        # === SECCIÓN: ESTADÍSTICAS DEL SISTEMA ===
        stats_frame = ttk.LabelFrame(scrollable_frame, text="📈 Estadísticas del Sistema", padding="15")
        stats_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Notebook para diferentes tipos de información
        stats_notebook = ttk.Notebook(stats_frame)
        stats_notebook.pack(fill=tk.BOTH, expand=True)
        
        # Tab 1: Resumen del sistema
        summary_frame = ttk.Frame(stats_notebook)
        stats_notebook.add(summary_frame, text="💻 Sistema")
        
        self.system_info_text = tk.Text(summary_frame, height=8, wrap=tk.WORD, 
                                       font=('Consolas', 9), state='disabled')
        system_scroll = ttk.Scrollbar(summary_frame, orient=tk.VERTICAL, 
                                     command=self.system_info_text.yview)
        self.system_info_text.configure(yscrollcommand=system_scroll.set)
        
        self.system_info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        system_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Tab 2: Estadísticas de conectividad
        connectivity_stats_frame = ttk.Frame(stats_notebook)
        stats_notebook.add(connectivity_stats_frame, text="🌐 Conectividad")
        
        self.connectivity_stats_text = tk.Text(connectivity_stats_frame, height=8, wrap=tk.WORD,
                                             font=('Consolas', 9), state='disabled')
        conn_stats_scroll = ttk.Scrollbar(connectivity_stats_frame, orient=tk.VERTICAL,
                                        command=self.connectivity_stats_text.yview)
        self.connectivity_stats_text.configure(yscrollcommand=conn_stats_scroll.set)
        
        self.connectivity_stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        conn_stats_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Tab 3: Historial de operaciones
        operations_frame = ttk.Frame(stats_notebook)
        stats_notebook.add(operations_frame, text="⚙️ Operaciones")
        
        self.operations_stats_text = tk.Text(operations_frame, height=8, wrap=tk.WORD,
                                           font=('Consolas', 9), state='disabled')
        ops_scroll = ttk.Scrollbar(operations_frame, orient=tk.VERTICAL,
                                 command=self.operations_stats_text.yview)
        self.operations_stats_text.configure(yscrollcommand=ops_scroll.set)
        
        self.operations_stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        ops_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Actualizar información inicial
        self.update_system_info()
        self.update_connectivity_stats()
        self.update_operations_stats()
    
    def test_individual_connection(self, location_id: str):
        """Test de conexión individual con feedback visual mejorado"""
        def test_worker():
            try:
                # Actualizar UI - iniciando test
                self.root.after(0, lambda: self.set_location_testing_state(location_id, True))
                
                # Test real con timeout
                result = self.test_location_connectivity_real(location_id)
                
                # Actualizar UI con resultado
                self.root.after(0, lambda: self.update_individual_connection_display(location_id, result))
                
            except Exception as e:
                error_result = {
                    'connected': False,
                    'error': f"Error en test: {str(e)}",
                    'details': f"Excepción durante test: {str(e)[:50]}..."
                }
                self.root.after(0, lambda: self.update_individual_connection_display(location_id, error_result))
            finally:
                self.root.after(0, lambda: self.set_location_testing_state(location_id, False))
        
        threading.Thread(target=test_worker, daemon=True).start()
    
    def set_location_testing_state(self, location_id: str, testing: bool):
        """Establecer estado visual de testing"""
        try:
            if location_id in self.connectivity_labels:
                if testing:
                    self.connectivity_labels[location_id].config(text="🔄 Probando...", style='Testing.TLabel')
                    self.connectivity_details[location_id].config(text="Ejecutando test de conectividad...", foreground='#1976D2')
                    self.connectivity_buttons[location_id].config(state='disabled')
                else:
                    self.connectivity_buttons[location_id].config(state='normal')
        except Exception as e:
            self.logger.error(f"Error estableciendo estado testing {location_id}: {e}")
    
    def test_location_connectivity_real(self, location_id: str) -> Dict[str, Any]:
        """Test REAL y completo de conectividad para una ubicación"""
        try:
            from core.config_manager import config_manager
            from core.auth_manager import auth_manager
            from core.base_connection import BaseConnection
            
            location_config = config_manager.get_location_config(location_id)
            if not location_config:
                return {
                    'connected': False, 
                    'error': 'No configurado', 
                    'details': 'Ubicación no encontrada en configuración'
                }
            
            # Crear conexión real
            connection = BaseConnection(location_id, location_config, auth_manager)
            
            # Test de conectividad básica
            start_time = time.time()
            basic_result = connection.test_connection()
            basic_time = time.time() - start_time
            
            if not basic_result['success']:
                return {
                    'connected': False,
                    'error': basic_result.get('error', 'Test falló'),
                    'response_time': basic_time,
                    'details': f"Conexión falló en {basic_time:.2f}s: {basic_result.get('error', 'Unknown')}"
                }
            
            # Test de acceso de escritura
            write_start = time.time()
            write_result = connection.test_write_access()
            write_time = time.time() - write_start
            
            # Test de velocidad (opcional)
            speed_info = ""
            try:
                speed_test = connection.test_speed()
                if speed_test.get('success', False):
                    speed_info = f" | Velocidad: {speed_test.get('speed_mbps', 0):.1f} MB/s"
            except:
                pass  # Speed test es opcional
            
            total_time = basic_time + write_time
            
            return {
                'connected': True,
                'response_time': total_time,
                'method': basic_result.get('method', 'unknown'),
                'write_access': write_result.get('success', False),
                'details': f"OK vía {basic_result.get('method', 'unknown')} ({total_time:.2f}s)" + 
                          (f" | ✅ Escritura" if write_result.get('success', False) else f" | ⚠️ Solo lectura") + 
                          speed_info
            }
        
        except Exception as e:
            return {
                'connected': False, 
                'error': str(e),
                'details': f"Error: {str(e)[:80]}..."
            }
    
    def update_individual_connection_display(self, location_id: str, result: Dict[str, Any]):
        """Actualizar display individual con información detallada"""
        try:
            if location_id not in self.connectivity_labels:
                return
            
            status_label = self.connectivity_labels[location_id]
            details_label = self.connectivity_details[location_id]
            
            # Actualizar estado interno
            self.connectivity_status[location_id] = result
            
            if result['connected']:
                status_label.config(text="🟢 ONLINE", style='Online.TLabel')
                
                # Detalles técnicos mejorados
                details = result.get('details', 'Conectado correctamente')
                details_label.config(text=details, foreground='#2E7D32')
                
            else:
                status_label.config(text="🔴 OFFLINE", style='Offline.TLabel')
                
                error_details = result.get('details', result.get('error', 'Error desconocido'))
                details_label.config(text=error_details, foreground='#C62828')
            
            # Actualizar contadores
            self.update_connection_counter()
            self.update_connectivity_stats()
            
            # Log del resultado
            status = "CONECTADO" if result['connected'] else "DESCONECTADO"
            self.logger.info(f"Test {location_id}: {status} - {result.get('details', '')}")
            
        except Exception as e:
            self.logger.error(f"Error actualizando display {location_id}: {e}")
    
    def test_all_connections(self):
        """Test todas las conexiones con progreso visual"""
        self.update_status("Iniciando test de todas las conexiones...", "info")
        
        def test_all_worker():
            try:
                from core.config_manager import config_manager
                
                locations = config_manager.get_enabled_locations()
                results = []
                
                for i, location_id in enumerate(locations):
                    # Actualizar progreso
                    progress_msg = f"Probando conexiones... ({i+1}/{len(locations)})"
                    self.root.after(0, lambda msg=progress_msg: self.update_status(msg, "info"))
                    
                    # Test individual
                    self.root.after(0, lambda loc=location_id: self.set_location_testing_state(loc, True))
                    
                    result = self.test_location_connectivity_real(location_id)
                    results.append((location_id, result))
                    
                    # Actualizar UI
                    self.root.after(0, lambda loc=location_id, res=result: self.update_individual_connection_display(loc, res))
                    self.root.after(0, lambda loc=location_id: self.set_location_testing_state(loc, False))
                    
                    # Pequeña pausa entre tests
                    time.sleep(0.5)
                
                # Mostrar resultados finales
                self.root.after(0, lambda: self.show_connection_test_results(results))
                self.root.after(0, lambda: self.update_status("Test de conexiones completado", "success"))
                
            except Exception as e:
                error_msg = f"Error en test masivo: {e}"
                self.root.after(0, lambda: self.update_status(error_msg, "error"))
        
        threading.Thread(target=test_all_worker, daemon=True).start()
    
    def test_all_connections_silent(self):
        """Test todas las conexiones en modo silencioso (para arranque)"""
        def silent_test_worker():
            try:
                from core.config_manager import config_manager
                
                locations = config_manager.get_enabled_locations()
                
                for location_id in locations:
                    if location_id in self.connectivity_labels:
                        result = self.test_location_connectivity_real(location_id)
                        self.root.after(0, lambda loc=location_id, res=result: 
                                      self.update_location_status_quiet(loc, res))
                        time.sleep(0.2)  # Pausa corta
                
                self.root.after(0, lambda: self.update_status("Check inicial completado", "success"))
                
            except Exception as e:
                self.logger.error(f"Error en test silencioso: {e}")
        
        threading.Thread(target=silent_test_worker, daemon=True).start()

# FIN PIEZA 2
