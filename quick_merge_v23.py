#!/usr/bin/env python3
"""
Quick Merge v2.3 - Combinar piezas actualizadas
"""

import os
from datetime import datetime

def quick_merge():
    # Backup archivo actual
    output_file = "limpieza_lista_gui_real.py"
    if os.path.exists(output_file):
        backup = f"limpieza_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
        with open(output_file, 'r', encoding='utf-8') as f:
            content = f.read()
        with open(backup, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"Backup: {backup}")

    # Actualizar header para v2.3
    header_v23 = '''"""
GUI Limpieza por Lista - Versión 2.3 - Julio 2025
========================================================
Cambios v2.3:
- ✅ Soporte para extensiones configurables
- ✅ Whitelist y blacklist de extensiones  
- ✅ Validación automática de archivos
- ✅ Handlers actualizados con ConfigManager v2.3
- ✅ UI de configuración de extensiones

Cambios v2.2:
- ✅ Búsqueda RECURSIVA en QNAPs y PCs
- ✅ Eliminación en todas las subcarpetas automáticamente
- ✅ Logging mejorado con rutas completas
================================================================
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
from datetime import datetime
import threading
import time
import random
from pathlib import Path
from PIL import Image, ImageTk

# Agregar el directorio actual al path para imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Imports del sistema
try:
    from gestor_license_manager import GestorLicenseManager, show_license_dialog
    from src.core.config_manager import ConfigManager
    from src.core.logger import LoggerManager
except ImportError as e:
    print(f"Warning: Core modules not found: {e}")
    print("Running in standalone mode...")

# Import handlers con fallback
try:
    from src.handlers.qnap_handler import QnapHandler
    from src.handlers.pc_handler import PCHandler
    print("✅ Handlers v2.3 importados exitosamente")
except ImportError as e:
    print(f"❌ Handlers falló: {e}")

class CleanupByListGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Gestor de Archivos - Limpieza por Lista v2.3")
        self.root.geometry("1100x850")
        self.root.resizable(True, True)
        
        # v2.3 - Variables de configuración
        self.version = "2.3"
        self.licensed = False
        self.running = False
        
        # Variables de configuración
        self.archivo_lista = tk.StringVar()
        self.dry_run = tk.BooleanVar(value=True)
        self.recursive_search = tk.BooleanVar(value=True)
        
        # Config manager y handlers v2.3
        try:
            self.config = ConfigManager()
            self.logger = LoggerManager("limpieza_gui").get_logger()
            self.logger.info("Iniciando GUI Limpieza v2.3 con extensiones configurables")
        except:
            import logging
            logging.basicConfig(level=logging.INFO)
            self.logger = logging.getLogger("limpieza_gui")
            self.config = None
        
        # Variables de estado para ubicaciones
        self.location_status = {}
        self.handlers = {}
        
        # v2.3 - Variables de extensiones
        self.extension_mode = tk.StringVar()
        self.extension_widgets = {}
        
        # Configurar logging
        self.setup_ui()
        self.check_license()
        self.load_default_config()

if __name__ == "__main__":
    print("🚀 Limpieza por Lista v2.3 - Extensiones Configurables")
    try:
        app = CleanupByListGUI()
        app.root.mainloop()
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
'''

    # Escribir archivo actualizado
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(header_v23)
    
    print(f"✅ Archivo actualizado: {output_file}")
    print("📌 Header v2.3 aplicado")

if __name__ == "__main__":
    quick_merge()
