"""
Script de Merge para GUI Limpieza por Lista
=========================================

Combina todas las piezas en un archivo funcional
"""

import os

def merge_limpieza_gui():
    """Combinar todas las piezas en limpieza_lista_gui.py"""
    
    # Archivos de piezas en orden
    pieces = [
        "limpieza_pieza1.py",
        "limpieza_pieza2.py", 
        "limpieza_pieza3.py",
        "limpieza_pieza4.py",
        "limpieza_pieza5.py"
    ]
    
    output_file = "limpieza_lista_gui.py"
    
    print("🔧 Iniciando merge de GUI Limpieza por Lista...")
    
    with open(output_file, 'w', encoding='utf-8') as merged:
        # Header del archivo final
        merged.write('"""\n')
        merged.write('GUI Independiente - Limpieza por Lista\n')
        merged.write('====================================\n\n')
        merged.write('Interfaz gráfica Tkinter con sistema de licencias integrado\n')
        merged.write('Generado automáticamente por merge de piezas\n')
        merged.write('"""\n\n')
        
        for i, piece_file in enumerate(pieces):
            if not os.path.exists(piece_file):
                print(f"❌ Error: {piece_file} no encontrado")
                continue
                
            print(f"📎 Procesando {piece_file}...")
            
            with open(piece_file, 'r', encoding='utf-8') as piece:
                content = piece.read()
                
                # Filtrar contenido según la pieza
                if i == 0:  # Primera pieza - incluir todo excepto docstring
                    lines = content.split('\n')
                    # Saltar docstring inicial
                    start_idx = 0
                    for j, line in enumerate(lines):
                        if line.startswith('"""') and j > 0:
                            start_idx = j + 1
                            break
                        elif line.startswith('import ') or line.startswith('from '):
                            start_idx = j
                            break
                    
                    merged.write('\n'.join(lines[start_idx:]))
                    merged.write('\n\n')
                    
                else:  # Otras piezas - solo funciones/métodos
                    lines = content.split('\n')
                    in_function = False
                    indent_level = 0
                    
                    for line in lines:
                        # Detectar inicio de función
                        if line.startswith('def '):
                            in_function = True
                            indent_level = len(line) - len(line.lstrip())
                            merged.write('    ' + line + '\n')  # Agregar indentación de clase
                        elif in_function:
                            # Continuar con la función
                            if line.strip() == '':
                                merged.write('\n')
                            elif line.startswith(' ' * indent_level) or line.strip() == '':
                                merged.write('    ' + line + '\n')  # Agregar indentación de clase
                            elif not line.startswith(' ') and line.strip():
                                # Nueva función o fin de sección
                                if line.startswith('def '):
                                    merged.write('    ' + line + '\n')
                                elif line.startswith('CleanupByListGUI.'):
                                    merged.write(line + '\n')
                                elif line.startswith('if __name__'):
                                    merged.write('\n' + line + '\n')
                                    in_function = False
                                else:
                                    in_function = False
                            else:
                                merged.write('    ' + line + '\n')
                    
                    merged.write('\n')
    
    print(f"✅ Merge completado: {output_file}")
    
    # Verificar archivo generado
    if os.path.exists(output_file):
        size = os.path.getsize(output_file)
        print(f"📊 Tamaño archivo: {size:,} bytes")
        
        # Contar líneas
        with open(output_file, 'r', encoding='utf-8') as f:
            lines = len(f.readlines())
        print(f"📝 Líneas totales: {lines}")
        
        return True
    else:
        print("❌ Error: Archivo no generado")
        return False

def fix_syntax_errors():
    """Corregir errores de sintaxis comunes"""
    output_file = "limpieza_lista_gui.py"
    
    if not os.path.exists(output_file):
        print("❌ Archivo no encontrado para corrección")
        return False
    
    print("🔧 Corrigiendo errores de sintaxis...")
    
    with open(output_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Correcciones comunes
    fixes = [
        # Eliminar asignaciones de métodos duplicadas
        ("CleanupByListGUI.check_license = check_license", ""),
        ("CleanupByListGUI.setup_ui = setup_ui", ""),
        ("CleanupByListGUI.update_license = update_license", ""),
        ("CleanupByListGUI.select_archivo_lista = select_archivo_lista", ""),
        ("CleanupByListGUI.select_location_path = select_location_path", ""),
        ("CleanupByListGUI.load_default_config = load_default_config", ""),
        ("CleanupByListGUI.log_message = log_message", ""),
        ("CleanupByListGUI.preview_list = preview_list", ""),
        ("CleanupByListGUI.execute_cleanup = execute_cleanup", ""),
        ("CleanupByListGUI.stop_cleanup = stop_cleanup", ""),
        ("CleanupByListGUI._reset_ui = _reset_ui", ""),
        ("CleanupByListGUI._execute_cleanup_thread = _execute_cleanup_thread", ""),
        ("CleanupByListGUI.run = run", ""),
        
        # Corregir problema de main_frame no definido
        ("    control_frame = ttk.LabelFrame(main_frame,", "        control_frame = ttk.LabelFrame(main_frame,"),
        ("def setup_ui_continued(self):", "    def setup_ui_continued(self):"),
        
        # Asegurar indentación correcta de métodos
        ("def check_license(self):", "    def check_license(self):"),
        ("def setup_ui(self):", "    def setup_ui(self):"),
        ("def update_license(self):", "    def update_license(self):"),
        ("def select_archivo_lista(self):", "    def select_archivo_lista(self):"),
        ("def select_location_path(self, location):", "    def select_location_path(self, location):"),
        ("def load_default_config(self):", "    def load_default_config(self):"),
        ("def log_message(self, message):", "    def log_message(self, message):"),
        ("def preview_list(self):", "    def preview_list(self):"),
        ("def execute_cleanup(self):", "    def execute_cleanup(self):"),
        ("def stop_cleanup(self):", "    def stop_cleanup(self):"),
        ("def _reset_ui(self):", "    def _reset_ui(self):"),
        ("def _execute_cleanup_thread(self):", "    def _execute_cleanup_thread(self):"),
        ("def run(self):", "    def run(self):")
    ]
    
    for old, new in fixes:
        content = content.replace(old, new)
    
    # Completar método setup_ui si está incompleto
    if "def setup_ui(self):" in content and "def setup_ui_continued(self):" in content:
        # Integrar setup_ui_continued en setup_ui
        content = content.replace("def setup_ui_continued(self):", "")
        content = content.replace("    def setup_ui_continued(self):", "")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Correcciones aplicadas")
    return True

if __name__ == "__main__":
    print("🔧 Merge GUI Limpieza por Lista")
    print("=" * 40)
    
    success = merge_limpieza_gui()
    if success:
        fix_syntax_errors()
        print("\n✅ Proceso completado")
        print("📄 Archivo generado: limpieza_lista_gui.py")
        print("\n🚀 Para probar:")
        print("python limpieza_lista_gui.py")
    else:
        print("\n❌ Error en el proceso")
