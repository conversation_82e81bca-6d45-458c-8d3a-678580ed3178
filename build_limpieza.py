"""
Script de compilación específico para Limpieza por Lista
========================================================
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def build_limpieza_app():
    """Compilar aplicación Limpieza por Lista"""
    
    print("🔨 COMPILANDO LIMPIEZA POR LISTA")
    print("=" * 50)
    
    # Verificar archivo existe
    if not os.path.exists("limpieza_lista_gui_real.py"):
        print("❌ Error: limpieza_lista_gui_real.py no encontrado")
        return False
    
    # Limpiar builds anteriores
    if os.path.exists("dist"):
        print("🗑️ Limpiando builds anteriores...")
        shutil.rmtree("dist")
    if os.path.exists("build"):
        shutil.rmtree("build")
    
    # Comando PyInstaller
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=LimpiezaPorLista",
        "--icon=logo_igson.png" if os.path.exists("logo_igson.png") else "",
        "--add-data=logo_igson.png;." if os.path.exists("logo_igson.png") else "",
        "--add-data=src;src",
        "--hidden-import=PIL",
        "--hidden-import=PIL.Image",
        "--hidden-import=PIL.ImageTk",
        "--hidden-import=tkinter",
        "--hidden-import=tkinter.ttk",
        "--hidden-import=tkinter.filedialog",
        "--hidden-import=tkinter.messagebox",
        "--hidden-import=tkinter.scrolledtext",
        "limpieza_lista_gui_real.py"
    ]
    
    # Filtrar opciones vacías
    cmd = [arg for arg in cmd if arg]
    
    print(f"📦 Ejecutando: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ Compilación exitosa!")
        
        # Verificar archivo generado
        exe_path = os.path.join("dist", "LimpiezaPorLista.exe")
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📄 Archivo generado: {exe_path}")
            print(f"📏 Tamaño: {size_mb:.1f} MB")
            
            # Crear backup con timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"LimpiezaPorLista_{timestamp}.exe"
            backup_path = os.path.join("dist", backup_name)
            shutil.copy2(exe_path, backup_path)
            print(f"💾 Backup creado: {backup_name}")
            
            return True
        else:
            print("❌ Error: Archivo .exe no generado")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Error en compilación:")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
        return False

def main():
    """Función principal"""
    print("🚀 COMPILADOR LIMPIEZA POR LISTA - IGSON")
    print("=" * 50)
    
    # Verificar PyInstaller
    try:
        subprocess.run(["pyinstaller", "--version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ PyInstaller no instalado. Instalar con: pip install pyinstaller")
        return
    
    # Compilar aplicación
    success = build_limpieza_app()
    
    if success:
        print("\n🎉 COMPILACIÓN COMPLETADA EXITOSAMENTE")
        print("📂 Archivos en carpeta 'dist/'")
    else:
        print("\n💥 COMPILACIÓN FALLÓ")
    
    input("\nPresiona Enter para cerrar...")

if __name__ == "__main__":
    main()
