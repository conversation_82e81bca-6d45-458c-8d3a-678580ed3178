# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['d:\\sports_manager\\gestor_archivos_corporativo\\validacion_duplicados_gui_real.py'],
    pathex=[],
    binaries=[],
    datas=[('src', 'src'), ('location_config_widget.py', '.'), ('logo_igson.png', '.')],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['matplotlib', 'numpy', 'scipy', 'pandas', 'jupyter', 'IPython', 'tornado', 'zmq', 'PIL.ImageQt'],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='IGSON_Validacion_Duplicados',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
