"""
dashboard_pieza5b.py
PIEZA 5B: Métodos de ejecución principales - execute_validacion y execute_limpieza
Gestor de Archivos Corporativo
"""

# CONTINUACIÓN DE LA CLASE GestorDashboard - MÉTODOS DE EJECUCIÓN PRINCIPALES

    def execute_validacion(self):
        """Ejecutar operación de validación completa"""
        # Validar configuración antes de ejecutar
        if not self.validate_validacion_config():
            return
        
        origen = self.validacion_origen_var.get().strip()
        rechazados = self.validacion_rechazados_var.get().strip()
        dry_run = self.validacion_dry_run_var.get()
        
        # Confirmar ejecución si no es dry run
        if not dry_run:
            if not messagebox.askyesno("Confirmar Ejecución", 
                                       "¿Ejecutar validación en modo REAL?\n\n"
                                       "Se moverán archivos duplicados."):
                return
        
        # Preparar UI para ejecución
        self.validacion_execute_btn.config(state='disabled')
        self.validacion_stop_btn.config(state='normal')
        self.operaciones_activas['validacion'] = True
        self.update_operations_indicator()
        
        mode_text = "SIMULACIÓN" if dry_run else "REAL"
        self.update_status(f"Ejecutando validación en modo {mode_text}...", "info")
        
        # Limpiar resultados previos
        self.show_validacion_results("", "summary")
        self.show_validacion_results("", "log")
        self.show_validacion_results("", "stats")
        
        # Inicializar progreso
        self.update_validacion_progress(0, "Iniciando validación...")
        
        def validacion_worker():
            try:
                from apps.validacion_duplicados import ValidacionDuplicados
                
                # Crear instancia de validación
                validacion = ValidacionDuplicados()
                
                # Configurar parámetros
                config = {
                    'carpeta_origen': origen,
                    'carpeta_rechazados': rechazados,
                    'dry_run': dry_run,
                    'backup': getattr(self, 'validacion_backup_var', tk.BooleanVar()).get(),
                    'log_detailed': getattr(self, 'validacion_log_detailed_var', tk.BooleanVar()).get(),
                    'verify_integrity': getattr(self, 'validacion_verify_integrity_var', tk.BooleanVar()).get()
                }
                
                # Callbacks para progreso
                def on_progress(progress, message=""):
                    self.root.after(0, lambda: self.update_validacion_progress(progress, message))
                
                def on_log(message):
                    self.root.after(0, lambda: self.append_validacion_log(message))
                
                def on_file_processed(filename, action, details=""):
                    log_msg = f"📄 {filename}: {action}"
                    if details:
                        log_msg += f" - {details}"
                    self.root.after(0, lambda: self.append_validacion_log(log_msg))
                
                # Ejecutar validación
                self.logger.info(f"Iniciando validación: {config}")
                resultado = validacion.ejecutar_validacion(config, 
                                                         progress_callback=on_progress,
                                                         log_callback=on_log,
                                                         file_callback=on_file_processed)
                
                # Procesar resultado
                self.logger.info(f"Validación completada: {resultado}")
                
                # Formatear y mostrar resultados
                summary_text = self.format_validacion_results(resultado)
                stats_text = self.format_validacion_stats(resultado)
                
                self.root.after(0, lambda: self.show_validacion_results(summary_text, "summary"))
                self.root.after(0, lambda: self.show_validacion_results(stats_text, "stats"))
                
                # Actualizar estadísticas globales
                if not hasattr(self, 'last_operation_stats'):
                    self.last_operation_stats = {}
                self.last_operation_stats['validacion'] = resultado
                self.root.after(0, self.update_operations_stats)
                
                # Finalizar operación
                self.root.after(0, self.finish_validacion)
                
                success_msg = f"Validación completada - {resultado.get('archivos_movidos', 0)} archivos procesados"
                self.root.after(0, lambda: self.update_status(success_msg, "success"))
                
            except Exception as e:
                error_msg = f"❌ Error ejecutando validación: {e}"
                self.logger.error(error_msg)
                self.root.after(0, lambda: self.show_validacion_results(error_msg, "summary"))
                self.root.after(0, lambda: self.update_status(error_msg, "error"))
                self.root.after(0, self.finish_validacion)
                
                # Log del error detallado
                import traceback
                self.logger.error(f"Stacktrace: {traceback.format_exc()}")
        
        # Ejecutar en thread separado
        threading.Thread(target=validacion_worker, daemon=True).start()
    
    def execute_limpieza(self):
        """Ejecutar operación de limpieza completa"""
        # Validar configuración antes de ejecutar
        if not self.validate_limpieza_config():
            return
        
        archivo_lista = self.limpieza_archivo_var.get().strip()
        dry_run = self.limpieza_dry_run_var.get()
        selected_locations = [loc for loc, var in self.limpieza_locations.items() if var.get()]
        
        # Confirmar ejecución si no es dry run
        if not dry_run:
            confirmation_msg = f"¿Ejecutar limpieza en modo REAL?\n\n"
            confirmation_msg += f"Se eliminarán archivos en {len(selected_locations)} ubicaciones:\n"
            for loc in selected_locations:
                confirmation_msg += f"• {loc.upper()}\n"
            
            if not messagebox.askyesno("Confirmar Ejecución", confirmation_msg):
                return
        
        # Preparar UI para ejecución
        self.limpieza_execute_btn.config(state='disabled')
        self.limpieza_stop_btn.config(state='normal')
        self.operaciones_activas['limpieza'] = True
        self.update_operations_indicator()
        
        # Inicializar progreso por ubicación
        for location in selected_locations:
            if location in self.location_progress_labels:
                self.location_progress_labels[location]['progress']['value'] = 0
                self.location_progress_labels[location]['status'].config(text="Preparando...")
                self.location_progress_labels[location]['files'].config(text="0/0")
        
        mode_text = "SIMULACIÓN" if dry_run else "REAL"
        self.update_status(f"Ejecutando limpieza en modo {mode_text}...", "info")
        
        # Limpiar resultados previos
        self.show_limpieza_results("", "summary")
        self.show_limpieza_results("", "location")
        self.show_limpieza_results("", "log")
        
        # Progreso general inicial
        self.update_limpieza_progress(0, None, "Iniciando limpieza...")
        
        def limpieza_worker():
            try:
                from apps.limpieza_lista import LimpiezaLista
                
                # Crear instancia de limpieza
                limpieza = LimpiezaLista()
                
                # Configurar parámetros
                config = {
                    'archivo_lista': archivo_lista,
                    'ubicaciones_seleccionadas': selected_locations,
                    'dry_run': dry_run,
                    'backup': getattr(self, 'limpieza_backup_var', tk.BooleanVar()).get(),
                    'paralelo': getattr(self, 'limpieza_parallel_var', tk.BooleanVar()).get(),
                    'confirmar': getattr(self, 'limpieza_confirm_var', tk.BooleanVar()).get(),
                    'verificar_antes': getattr(self, 'limpieza_verify_before_var', tk.BooleanVar()).get(),
                    'continuar_errores': getattr(self, 'limpieza_skip_errors_var', tk.BooleanVar()).get()
                }
                
                # Callbacks para progreso
                def on_progress(progress, location=None, message=""):
                    self.root.after(0, lambda: self.update_limpieza_progress(progress, location, message))
                
                def on_location_progress(location, progress, found, processed, status):
                    self.root.after(0, lambda: self.update_location_progress(location, progress, found, processed, status))
                
                def on_log(message):
                    self.root.after(0, lambda: self.append_limpieza_log(message))
                
                def on_location_start(location):
                    self.root.after(0, lambda: self.update_location_progress(location, 0, 0, 0, "Conectando..."))
                
                def on_location_complete(location, stats):
                    found = stats.get('archivos_encontrados', 0)
                    deleted = stats.get('archivos_eliminados', 0) 
                    status = f"✅ {deleted}/{found}"
                    self.root.after(0, lambda: self.update_location_progress(location, 100, found, deleted, status))
                
                def on_file_processed(location, filename, action, success):
                    icon = "✅" if success else "❌"
                    log_msg = f"📍 {location.upper()}: {icon} {filename} - {action}"
                    self.root.after(0, lambda: self.append_limpieza_log(log_msg))
                
                # Ejecutar limpieza
                self.logger.info(f"Iniciando limpieza: {config}")
                resultado = limpieza.ejecutar_limpieza(config,
                                                     progress_callback=on_progress,
                                                     location_callback=on_location_progress,
                                                     log_callback=on_log,
                                                     location_start_callback=on_location_start,
                                                     location_complete_callback=on_location_complete,
                                                     file_callback=on_file_processed)
                
                # Procesar resultado
                self.logger.info(f"Limpieza completada: {resultado}")
                
                # Formatear y mostrar resultados
                summary_text = self.format_limpieza_results(resultado)
                location_text = self.format_limpieza_by_location(resultado)
                
                self.root.after(0, lambda: self.show_limpieza_results(summary_text, "summary"))
                self.root.after(0, lambda: self.show_limpieza_results(location_text, "location"))
                
                # Actualizar estadísticas globales
                if not hasattr(self, 'last_operation_stats'):
                    self.last_operation_stats = {}
                self.last_operation_stats['limpieza'] = resultado
                self.root.after(0, self.update_operations_stats)
                
                # Finalizar operación
                self.root.after(0, self.finish_limpieza)
                
                total_eliminados = resultado.get('total_archivos_eliminados', 0)
                total_encontrados = resultado.get('total_archivos_encontrados', 0)
                success_msg = f"Limpieza completada - {total_eliminados}/{total_encontrados} archivos procesados"
                self.root.after(0, lambda: self.update_status(success_msg, "success"))
                
            except Exception as e:
                error_msg = f"❌ Error ejecutando limpieza: {e}"
                self.logger.error(error_msg)
                self.root.after(0, lambda: self.show_limpieza_results(error_msg, "summary"))
                self.root.after(0, lambda: self.update_status(error_msg, "error"))
                self.root.after(0, self.finish_limpieza)
                
                # Log del error detallado
                import traceback
                self.logger.error(f"Stacktrace: {traceback.format_exc()}")
        
        # Ejecutar en thread separado
        threading.Thread(target=limpieza_worker, daemon=True).start()
    
    def stop_validacion(self):
        """Detener operación de validación"""
        if messagebox.askyesno("Confirmar", "¿Detener la operación de validación?\n\nLos archivos ya procesados permanecerán en su estado actual."):
            self.operaciones_activas['validacion'] = False
            self.finish_validacion()
            self.update_status("Validación detenida por el usuario", "warning")
            self.append_validacion_log("🛑 Operación detenida por el usuario")
    
    def stop_limpieza(self):
        """Detener operación de limpieza"""
        if messagebox.askyesno("Confirmar", "¿Detener la operación de limpieza?\n\nLos archivos ya procesados permanecerán eliminados."):
            self.operaciones_activas['limpieza'] = False
            self.finish_limpieza()
            self.update_status("Limpieza detenida por el usuario", "warning")
            self.append_limpieza_log("🛑 Operación detenida por el usuario")
    
    def finish_validacion(self):
        """Finalizar operación de validación"""
        self.validacion_execute_btn.config(state='normal')
        self.validacion_stop_btn.config(state='disabled')
        self.operaciones_activas['validacion'] = False
        self.update_operations_indicator()
        self.update_validacion_progress(100, "Validación completada")
        
        # Log de finalización
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.append_validacion_log(f"🏁 Operación finalizada: {timestamp}")
    
    def finish_limpieza(self):
        """Finalizar operación de limpieza"""
        self.limpieza_execute_btn.config(state='normal')
        self.limpieza_stop_btn.config(state='disabled')
        self.operaciones_activas['limpieza'] = False
        self.update_operations_indicator()
        self.update_limpieza_progress(100, None, "Limpieza completada")
        
        # Actualizar todas las ubicaciones a completadas si no estaban ya
        for location in self.location_progress_labels:
            if "✅" not in self.location_progress_labels[location]['status'].cget('text'):
                self.location_progress_labels[location]['status'].config(text="Terminado")
                self.location_progress_labels[location]['progress']['value'] = 100
        
        # Log de finalización
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.append_limpieza_log(f"🏁 Operación finalizada: {timestamp}")
    
    def format_validacion_stats(self, stats: Dict[str, Any]) -> str:
        """Formatear estadísticas detalladas de validación"""
        if not stats:
            return "❌ No hay estadísticas disponibles\n"
        
        result = "📈 ESTADÍSTICAS DETALLADAS - VALIDACIÓN\n"
        result += "=" * 50 + "\n\n"
        
        # Tiempos de ejecución
        duracion = stats.get('duracion_total', 0)
        result += f"⏱️ RENDIMIENTO:\n"
        result += f"   Duración total: {duracion:.2f} segundos\n"
        
        if duracion > 0:
            archivos = stats.get('archivos_analizados', 0)
            if archivos > 0:
                result += f"   Velocidad: {archivos/duracion:.1f} archivos/segundo\n"
        
        result += f"\n📊 RESUMEN EJECUTIVO:\n"
        result += f"   Archivos analizados: {stats.get('archivos_analizados', 0)}\n"
        result += f"   Duplicados detectados: {stats.get('duplicados_encontrados', 0)}\n"
        result += f"   Archivos movidos: {stats.get('archivos_movidos', 0)}\n"
        result += f"   Errores encontrados: {stats.get('errores', 0)}\n"
        
        # Tasa de éxito
        analizados = stats.get('archivos_analizados', 0)
        movidos = stats.get('archivos_movidos', 0)
        if analizados > 0:
            tasa_exito = (movidos / analizados) * 100
            result += f"   Tasa de procesamiento: {tasa_exito:.1f}%\n"
        
        # Información de ubicaciones verificadas
        ubicaciones_stats = stats.get('ubicaciones_verificadas', {})
        if ubicaciones_stats:
            result += f"\n🌐 UBICACIONES VERIFICADAS:\n"
            for ubicacion_id, ubicacion_info in ubicaciones_stats.items():
                status = "✅" if ubicacion_info.get('conectado', False) else "❌"
                result += f"   {status} {ubicacion_id.upper()}: "
                if ubicacion_info.get('conectado', False):
                    archivos_verificados = ubicacion_info.get('archivos_encontrados', 0)
                    result += f"{archivos_verificados} archivos verificados\n"
                else:
                    result += f"Error de conexión\n"
        
        # Detalles de archivos si es modo simulación
        if stats.get('dry_run', False):
            result += f"\n🧪 MODO SIMULACIÓN:\n"
            result += f"   No se realizaron cambios reales\n"
            result += f"   Todos los duplicados serían movidos a:\n"
            result += f"   {stats.get('carpeta_rechazados', 'N/A')}\n"
        
        return result
    
    def format_limpieza_by_location(self, stats: Dict[str, Any]) -> str:
        """Formatear resultados de limpieza por ubicación"""
        if not stats:
            return "❌ No hay estadísticas por ubicación\n"
        
        result = "🌐 RESULTADOS POR UBICACIÓN\n"
        result += "=" * 40 + "\n\n"
        
        ubicaciones_stats = stats.get('estadisticas_por_ubicacion', {})
        
        for ubicacion_id, ubicacion_stats in ubicaciones_stats.items():
            result += f"📍 {ubicacion_id.upper()}:\n"
            
            if ubicacion_stats.get('conectado', False):
                result += f"   Estado: ✅ Conectado\n"
                result += f"   Archivos encontrados: {ubicacion_stats.get('archivos_encontrados', 0)}\n"
                result += f"   Archivos eliminados: {ubicacion_stats.get('archivos_eliminados', 0)}\n"
                result += f"   Errores: {ubicacion_stats.get('errores', 0)}\n"
                result += f"   Tiempo: {ubicacion_stats.get('tiempo_procesamiento', 0):.2f}s\n"
                
                # Detalles de archivos procesados
                archivos_procesados = ubicacion_stats.get('archivos_procesados', [])
                if archivos_procesados:
                    result += f"   Archivos procesados ({len(archivos_procesados)}):\n"
                    for archivo_info in archivos_procesados[:5]:  # Solo primeros 5
                        nombre = archivo_info.get('nombre', 'N/A')
                        accion = archivo_info.get('accion', 'N/A')
                        exito = archivo_info.get('exito', False)
                        status_icon = "✅" if exito else "❌"
                        result += f"     {status_icon} {nombre} - {accion}\n"
                    
                    if len(archivos_procesados) > 5:
                        result += f"     ... y {len(archivos_procesados) - 5} más\n"
                        
            else:
                result += f"   Estado: ❌ Error de conexión\n"
                error_msg = ubicacion_stats.get('error', 'Error desconocido')
                result += f"   Error: {error_msg}\n"
            
            result += "\n"
        
        # Resumen general
        total_encontrados = sum(loc.get('archivos_encontrados', 0) for loc in ubicaciones_stats.values())
        total_eliminados = sum(loc.get('archivos_eliminados', 0) for loc in ubicaciones_stats.values())
        total_errores = sum(loc.get('errores', 0) for loc in ubicaciones_stats.values())
        
        result += "📊 RESUMEN GENERAL:\n"
        result += f"   Total encontrados: {total_encontrados}\n"
        result += f"   Total eliminados: {total_eliminados}\n"
        result += f"   Total errores: {total_errores}\n"
        
        if total_encontrados > 0:
            efectividad = (total_eliminados / total_encontrados) * 100
            result += f"   Efectividad: {efectividad:.1f}%\n"
        
        return result

# FIN PIEZA 5B
