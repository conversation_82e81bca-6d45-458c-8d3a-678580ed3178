"""
Test Sesión 3 - Handlers Específicos (QNAP + PC)
===============================================

Verifica que los handlers especializados funcionen correctamente
"""

import sys
from pathlib import Path
import logging

# Agregar src al path para imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_qnap_handler():
    """Test del handler especializado para QNAP"""
    print("🗄️  Testing QNAP Handler...")
    
    try:
        from handlers.qnap_handler import QnapHandler
        from core.config_manager import config
        
        # Obtener ubicaciones QNAP
        locations = config.get_enabled_locations()
        qnap_locations = [loc_id for loc_id in locations.keys() if 'qnap' in loc_id.lower()]
        
        if not qnap_locations:
            print("  ⚠️  No hay ubicaciones QNAP configuradas")
            return True
        
        test_location = qnap_locations[0]
        print(f"  🔍 Testing QNAP: {test_location}")
        
        # Crear handler QNAP
        qnap_handler = QnapHandler(test_location)
        print(f"  ✓ QNAP Handler creado")
        
        # Test de información básica
        status = qnap_handler.get_status()
        print(f"  ✓ Status: {status['status']}")
        print(f"  ✓ Device type: {qnap_handler.device_type}")
        
        # Test de información específica de QNAP
        qnap_info = qnap_handler.get_qnap_info()
        print(f"  ✓ Info obtenida - Device: {qnap_info['device_type']}")
        
        if qnap_info.get('qnap_specific'):
            specific_info = qnap_info['qnap_specific']
            
            # Test información RAID
            raid_info = specific_info.get('raid_status', {})
            if raid_info:
                print(f"    📀 RAID: {raid_info.get('raid_type')} - {raid_info.get('status')}")
            
            # Test temperatura
            temp_info = specific_info.get('temperature', {})
            if temp_info:
                print(f"    🌡️  CPU Temp: {temp_info.get('cpu_temp')}")
            
            # Test discos
            disk_health = specific_info.get('disk_health', [])
            print(f"    💽 Discos monitoreados: {len(disk_health)}")
            
            # Test carpetas compartidas
            shared_folders = specific_info.get('shared_folders', [])
            print(f"    📁 Carpetas compartidas: {len(shared_folders)}")
        
        # Test de búsqueda de archivos (sin conexión)
        found_files = qnap_handler.find_file_in_qnap("test.txt", search_subfolders=False)
        print(f"  ✓ Búsqueda de archivo: {len(found_files)} encontrados")
        
        # Test de verificación de existencia
        exists, path = qnap_handler.check_file_exists("test.txt")
        print(f"  ✓ Verificación existencia: {exists}")
        
        # Test de información de archivo
        file_info = qnap_handler.get_file_info("test.txt")
        status = "disponible" if file_info else "no disponible"
        print(f"  ✓ Info de archivo: {status}")
        
        # Test de optimización
        optimize_success, optimize_msg = qnap_handler.optimize_qnap_connection()
        print(f"  ✓ Optimización: {'éxito' if optimize_success else 'falló'}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ QNAP Handler Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pc_handler():
    """Test del handler especializado para PC"""
    print("\n💻 Testing PC Handler...")
    
    try:
        from handlers.pc_handler import PCHandler
        from core.config_manager import config
        
        # Obtener ubicaciones PC
        locations = config.get_enabled_locations()
        pc_locations = [loc_id for loc_id in locations.keys() if 'pc' in loc_id.lower()]
        
        if not pc_locations:
            print("  ⚠️  No hay ubicaciones PC configuradas")
            return True
        
        test_location = pc_locations[0]
        print(f"  🔍 Testing PC: {test_location}")
        
        # Crear handler PC
        pc_handler = PCHandler(test_location)
        print(f"  ✓ PC Handler creado")
        
        # Test de información básica
        status = pc_handler.get_status()
        print(f"  ✓ Status: {status['status']}")
        print(f"  ✓ Device type: {pc_handler.device_type}")
        
        # Test de características del PC
        print(f"  ✓ PC Features:")
        for feature, enabled in pc_handler.pc_features.items():
            status_icon = "✅" if enabled else "❌"
            print(f"    {status_icon} {feature}")
        
        # Test de información específica de PC
        pc_info = pc_handler.get_pc_info()
        print(f"  ✓ Info obtenida - Device: {pc_info['device_type']}")
        
        if pc_info.get('pc_specific'):
            specific_info = pc_info['pc_specific']
            
            # Test información del sistema
            sys_info = specific_info.get('system_info', {})
            if sys_info and 'error' not in sys_info:
                print(f"    💻 OS: {sys_info.get('os', 'N/A')}")
                print(f"    🏠 Hostname: {sys_info.get('hostname', 'N/A')}")
            
            # Test procesos
            processes = specific_info.get('running_processes', [])
            if processes and 'error' not in processes[0]:
                print(f"    ⚙️  Procesos monitoreados: {len(processes)}")
            
            # Test shares de red
            shares = specific_info.get('network_shares', [])
            print(f"    🌐 Network shares: {len(shares)}")
            
            # Test rendimiento
            perf_info = specific_info.get('system_performance', {})
            if perf_info and 'error' not in perf_info:
                cpu_usage = perf_info.get('cpu_usage_percent', 0)
                mem_usage = perf_info.get('memory_usage_percent', 0)
                print(f"    📊 CPU: {cpu_usage}%, RAM: {mem_usage}%")
        
        # Test de búsqueda de archivos
        found_files = pc_handler.find_file_in_pc("*.txt", search_subfolders=False)
        print(f"  ✓ Búsqueda de archivo: {len(found_files)} encontrados")
        
        # Test con filtros de extensión
        filtered_files = pc_handler.find_file_in_pc("*.*", search_subfolders=False, 
                                                   file_extensions=['.txt', '.pdf'])
        print(f"  ✓ Búsqueda filtrada: {len(filtered_files)} archivos txt/pdf")
        
        # Test de verificación de existencia
        exists, path = pc_handler.check_file_exists("test.txt")
        print(f"  ✓ Verificación existencia: {exists}")
        
        # Test de comando remoto
        success, stdout, stderr = pc_handler.execute_remote_command("dir")
        cmd_status = "éxito" if success else "falló"
        print(f"  ✓ Comando remoto: {cmd_status}")
        
        # Test de optimización
        optimize_success, optimize_msg = pc_handler.optimize_pc_connection()
        print(f"  ✓ Optimización: {'éxito' if optimize_success else 'falló'}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ PC Handler Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_handlers_comparison():
    """Test comparativo entre handlers QNAP y PC"""
    print("\n⚖️  Testing Handlers Comparison...")
    
    try:
        from handlers.qnap_handler import QnapHandler
        from handlers.pc_handler import PCHandler
        from core.config_manager import config
        
        locations = config.get_enabled_locations()
        qnap_locations = [loc for loc in locations.keys() if 'qnap' in loc.lower()]
        pc_locations = [loc for loc in locations.keys() if 'pc' in loc.lower()]
        
        print(f"  📊 Comparación de handlers:")
        print(f"    🗄️  QNAP locations: {len(qnap_locations)}")
        print(f"    💻 PC locations: {len(pc_locations)}")
        
        # Comparar características
        if qnap_locations and pc_locations:
            qnap_handler = QnapHandler(qnap_locations[0])
            pc_handler = PCHandler(pc_locations[0])
            
            print(f"\n  🔍 Características específicas:")
            print(f"    QNAP features: {len(qnap_handler.qnap_features)}")
            print(f"    PC features: {len(pc_handler.pc_features)}")
            
            # Test de funcionalidades específicas
            qnap_methods = [method for method in dir(qnap_handler) if method.startswith('_get_') or method.endswith('_qnap')]
            pc_methods = [method for method in dir(pc_handler) if method.startswith('_get_') or method.endswith('_pc')]
            
            print(f"    QNAP métodos específicos: {len(qnap_methods)}")
            print(f"    PC métodos específicos: {len(pc_methods)}")
            
            # Test de herencia común
            common_methods = ['connect', 'disconnect', 'test_connection', 'get_status']
            print(f"\n  🔗 Métodos heredados de BaseConnection:")
            for method in common_methods:
                qnap_has = hasattr(qnap_handler, method)
                pc_has = hasattr(pc_handler, method)
                status = "✅" if qnap_has and pc_has else "❌"
                print(f"    {status} {method}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Handlers Comparison Error: {e}")
        return False

def test_bulk_operations():
    """Test de operaciones masivas en handlers"""
    print("\n📦 Testing Bulk Operations...")
    
    try:
        from handlers.qnap_handler import QnapHandler
        from handlers.pc_handler import PCHandler
        from core.config_manager import config
        
        locations = config.get_enabled_locations()
        
        # Test lista de archivos para eliminación masiva
        test_files = ["archivo1.txt", "archivo2.pdf", "documento.docx", "imagen.jpg", "video.mp4"]
        
        # Test QNAP bulk operations
        qnap_locations = [loc for loc in locations.keys() if 'qnap' in loc.lower()]
        if qnap_locations:
            print(f"  🗄️  Testing QNAP bulk operations:")
            qnap_handler = QnapHandler(qnap_locations[0])
            
            # Test bulk delete (sin conexión real)
            stats = qnap_handler.bulk_delete_files(test_files)
            print(f"    📋 Total files to process: {stats['total_files']}")
            print(f"    🔍 Found files: {stats['found_files']}")
            print(f"    🗑️  Deleted files: {stats['deleted_files']}")
            print(f"    ❌ Failed files: {stats['failed_files']}")
            print(f"    📄 Not found files: {len(stats['not_found_list'])}")
        
        # Test PC bulk operations
        pc_locations = [loc for loc in locations.keys() if 'pc' in loc.lower()]
        if pc_locations:
            print(f"\n  💻 Testing PC bulk operations:")
            pc_handler = PCHandler(pc_locations[0])
            
            # Test bulk delete (sin conexión real)
            stats = pc_handler.bulk_delete_files(test_files, recycle_bin=True)
            print(f"    📋 Total files to process: {stats['total_files']}")
            print(f"    🔍 Found files: {stats['found_files']}")
            print(f"    🗑️  Deleted files: {stats['deleted_files']}")
            print(f"    ❌ Failed files: {stats['failed_files']}")
            print(f"    📄 Not found files: {len(stats['not_found_list'])}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Bulk Operations Error: {e}")
        return False

def test_handlers_integration():
    """Test de integración con sistema base"""
    print("\n🔗 Testing Handlers Integration...")
    
    try:
        from handlers.qnap_handler import QnapHandler
        from handlers.pc_handler import PCHandler
        from core.base_connection import BaseConnection
        from core.config_manager import config
        from core.logger import get_logger
        
        # Test de herencia
        locations = config.get_enabled_locations()
        if not locations:
            print("  ⚠️  No hay ubicaciones para test de integración")
            return True
        
        first_location = list(locations.keys())[0]
        
        # Determinar tipo de handler según la ubicación
        if 'qnap' in first_location.lower():
            handler = QnapHandler(first_location)
            handler_type = "QNAP"
        else:
            handler = PCHandler(first_location)
            handler_type = "PC"
        
        print(f"  🔍 Testing {handler_type} handler integration:")
        
        # Test de herencia de BaseConnection
        print(f"    ✓ Es instancia de BaseConnection: {isinstance(handler, BaseConnection)}")
        
        # Test de logger integrado
        logger = get_logger('conectividad')
        print(f"    ✓ Logger integrado: {handler.logger is not None}")
        
        # Test de configuración
        print(f"    ✓ Configuración cargada: {handler.config is not None}")
        print(f"    ✓ Location ID: {handler.location_id}")
        print(f"    ✓ Device Type: {handler.device_type}")
        
        # Test de métodos base
        base_methods = ['connect', 'disconnect', 'test_connection', 'get_status', 'is_connected']
        for method in base_methods:
            has_method = hasattr(handler, method) and callable(getattr(handler, method))
            print(f"    ✓ Método {method}: {'disponible' if has_method else 'faltante'}")
        
        # Test de estado inicial
        status = handler.get_status()
        print(f"    ✓ Estado inicial: {status['status']}")
        print(f"    ✓ Métodos disponibles: {status['available_methods']}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Handlers Integration Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Ejecuta todos los tests de la Sesión 3"""
    print("🚀 SESIÓN 3 - TEST DE HANDLERS ESPECÍFICOS")
    print("=" * 50)
    
    # Configurar logging para tests
    logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
    
    tests = [
        ("QNAP Handler", test_qnap_handler),
        ("PC Handler", test_pc_handler),
        ("Handlers Comparison", test_handlers_comparison),
        ("Bulk Operations", test_bulk_operations),
        ("Handlers Integration", test_handlers_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*15} {test_name} {'='*15}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  💥 {test_name} CRASHED: {e}")
            results.append((test_name, False))
    
    # Resumen final
    print("\n" + "=" * 60)
    print("📊 RESUMEN DE TESTS - SESIÓN 3")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResultado: {passed}/{len(results)} tests pasaron")
    
    if passed == len(results):
        print("\n🎉 ¡SESIÓN 3 COMPLETADA EXITOSAMENTE!")
        print("✅ Handlers QNAP y PC funcionales")
        print("✅ Funcionalidades específicas implementadas")
        print("✅ Operaciones masivas disponibles")
        print("✅ Integración con sistema base completa")
        print("\n🔄 Listo para Sesión 4: Operación Validación de Duplicados")
    else:
        print("\n⚠️  Algunos tests fallaron - revisar implementación")
        print("💡 Nota: Es normal si fallan por falta de conexiones reales")

if __name__ == "__main__":
    main()
