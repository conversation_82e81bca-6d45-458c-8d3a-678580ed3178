"""
GUI Independiente - Validación de Duplicados con Configuración Real
================================================================

Interfaz gráfica con configuración real de ubicaciones QNAPs/PCs
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
from datetime import datetime
import threading
import time
import random
from pathlib import Path

# Agregar el directorio actual al path para imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Imports del sistema
try:
    from gestor_license_manager import GestorLicenseManager, show_license_dialog
    from src.core.config_manager import ConfigManager
    from src.core.logger import LoggerManager
    from src.handlers.qnap_handler import QnapHandler
    from src.handlers.pc_handler import PCHandler
    handlers_available = True
except ImportError as e:
    print(f"Warning: Some modules not found: {e}")
    print("Running in standalone mode...")
    handlers_available = False
    
    # Mock handlers para compatibilidad
    class QnapHandler:
        def __init__(self, location_id): self.location_id = location_id
    
    class PCHandler:
        def __init__(self, location_id): self.location_id = location_id

# Imports adicionales
try:
    from location_config_widget import LocationConfigWidget
except ImportError:
    LocationConfigWidget = None

class MockLocationWidget:
    """Widget mock para compatibilidad cuando LocationConfigWidget no está disponible"""
    def __init__(self):
        pass
    
    def get_enabled_locations(self):
        """Retorna ubicaciones simuladas para testing (6 ubicaciones)"""
        return {
            'qnap1': {'path': 'test_validacion/qnap1', 'subfolder': ''},
            'qnap2': {'path': 'test_validacion/qnap2', 'subfolder': ''},
            'pc1': {'path': '', 'hostname': '************', 'share': 'shared', 'subfolder': 'work', 'username': 'admin'},
            'pc2': {'path': '', 'hostname': '************', 'share': 'shared', 'subfolder': 'work', 'username': 'admin'},
            'pc3': {'path': '', 'hostname': '************', 'share': 'shared', 'subfolder': 'work', 'username': 'admin'},
            'pc4': {'path': '', 'hostname': '************', 'share': 'shared', 'subfolder': 'work', 'username': 'admin'}
        }
    
    def log_message(self, message):
        """Placeholder para logging"""
        pass

class ValidationDuplicatesGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Gestor de Archivos - Validación de Duplicados")
        self.root.geometry("1000x800")
        self.root.resizable(True, True)
        
        # Configuración inicial
        self.licensed = False
        self.running = False
        
        # Variables de configuración
        self.carpeta_origen = tk.StringVar()
        self.carpeta_rechazados = tk.StringVar()
        self.dry_run = tk.BooleanVar(value=True)
        
        # Config manager y handlers
        try:
            self.config = ConfigManager()
            self.logger = LoggerManager("validacion_gui").get_logger()
        except:
            import logging
            logging.basicConfig(level=logging.INFO)
            self.logger = logging.getLogger("validacion_gui")
            self.config = None
        
        self.handlers = {}
        
        self.setup_ui()
        self.check_license()
        self.load_default_config()
    
    def check_license(self):
        """Verificar licencia al iniciar"""
        try:
            license_manager = GestorLicenseManager("validacion")
            valid, message = license_manager.validate_license()
            
            if valid:
                self.licensed = True
                self.license_status.config(text=f"✅ Licenciado: {message}", foreground="green")
                self.logger.info(f"License valid: {message}")
            else:
                self.licensed = False
                self.license_status.config(text=f"❌ Sin licencia: {message}", foreground="red")
                self.logger.warning(f"License invalid: {message}")
                
                # Mostrar diálogo de activación
                response = messagebox.askyesno("Licencia Requerida", 
                    f"Esta aplicación requiere una licencia válida.\n\n{message}\n\n¿Desea activar licencia?")
                if response:
                    activated = show_license_dialog(self.root, "validacion")
                    if activated:
                        self.check_license()  # Re-verificar
                else:
                    self.root.destroy()
                    return
        except Exception as e:
            self.logger.error(f"License check error: {e}")
            self.licensed = False
            self.license_status.config(text="❌ Error verificando licencia", foreground="red")
            messagebox.showerror("Error", "Error verificando licencia. La aplicación se cerrará.")
            self.root.destroy()
    
    def setup_ui(self):
        """Configurar interfaz de usuario"""
        # Notebook principal
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Tab 1: Operación
        self.operation_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.operation_frame, text="🔍 Validación de Duplicados")
        self.setup_operation_tab()
        
        # Tab 2: Configuración de ubicaciones
        self.config_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.config_frame, text="⚙️ Configuración")
        self.setup_config_tab()
        
        # Tab 3: Logs
        self.logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.logs_frame, text="📋 Logs")
        self.setup_logs_tab()
    
    def setup_operation_tab(self):
        """Configurar tab de operación"""
        # Frame principal con padding
        main_frame = ttk.Frame(self.operation_frame, padding="10")
        main_frame.pack(fill='both', expand=True)
        
        # Header con logo
        self.setup_header(main_frame)
        
        # Frame de configuración básica
        basic_config_frame = ttk.LabelFrame(main_frame, text="Configuración de Carpetas", padding="10")
        basic_config_frame.pack(fill='x', pady=(10,0))
        
        # Carpeta origen
        ttk.Label(basic_config_frame, text="Carpeta Origen:").grid(row=0, column=0, sticky='w', pady=5)
        ttk.Entry(basic_config_frame, textvariable=self.carpeta_origen, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(basic_config_frame, text="Examinar", 
                  command=self.select_carpeta_origen).grid(row=0, column=2, pady=5)
        
        # Carpeta rechazados
        ttk.Label(basic_config_frame, text="Carpeta Rechazados:").grid(row=1, column=0, sticky='w', pady=5)
        ttk.Entry(basic_config_frame, textvariable=self.carpeta_rechazados, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(basic_config_frame, text="Examinar", 
                  command=self.select_carpeta_rechazados).grid(row=1, column=2, pady=5)
        
        # Modo dry run con explicación detallada
        dry_run_frame = ttk.Frame(basic_config_frame)
        dry_run_frame.grid(row=2, column=0, columnspan=3, sticky='w', pady=10)
        
        ttk.Checkbutton(dry_run_frame, text="Modo Simulación (Dry Run)", 
                       variable=self.dry_run).pack(side='left')
        
        ttk.Label(dry_run_frame, text="ℹ️ Simula la validación sin mover archivos realmente", 
                 font=('Arial', 9), foreground='blue').pack(side='left', padx=(10,0))
        
        # Frame de estado de ubicaciones (6 ubicaciones)
        status_frame = ttk.LabelFrame(main_frame, text="Estado de Ubicaciones (6 ubicaciones)", padding="10")
        status_frame.pack(fill='x', pady=(10,0))
        
        # Grid para mostrar estado de todas las ubicaciones
        locations = ['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4']
        
        # Headers
        ttk.Label(status_frame, text="Ubicación", font=("Arial", 10, "bold")).grid(row=0, column=0, padx=5, pady=2)
        ttk.Label(status_frame, text="Estado", font=("Arial", 10, "bold")).grid(row=0, column=1, padx=20, pady=2)
        ttk.Label(status_frame, text="Tipo", font=("Arial", 10, "bold")).grid(row=0, column=2, padx=20, pady=2)
        
        # Separador
        ttk.Separator(status_frame, orient='horizontal').grid(row=1, column=0, columnspan=3, sticky='ew', pady=5)
        
        # Estado de cada ubicación
        self.location_labels = {}
        for i, location_id in enumerate(locations):
            row = i + 2
            
            # Nombre de ubicación
            ttk.Label(status_frame, text=location_id.upper(), font=("Arial", 9, "bold")).grid(row=row, column=0, sticky='w', padx=5, pady=2)
            
            # Estado de conexión
            status_label = ttk.Label(status_frame, text="Sin configurar", foreground="gray")
            status_label.grid(row=row, column=1, sticky='w', padx=20, pady=2)
            
            # Tipo de dispositivo
            device_type = "QNAP" if location_id.startswith('qnap') else "PC"
            ttk.Label(status_frame, text=device_type, foreground="blue").grid(row=row, column=2, sticky='w', padx=20, pady=2)
            
            self.location_labels[location_id] = {'status': status_label}
        
        # Botón de control de estado
        button_frame = ttk.Frame(status_frame)
        button_frame.grid(row=8, column=0, columnspan=3, pady=(10,0))
        
        ttk.Button(button_frame, text="🔄 Actualizar Estado", 
                  command=self.update_location_status).pack(side='left')
        
        # Frame de control
        control_frame = ttk.LabelFrame(main_frame, text="Control de Operación", padding="10")
        control_frame.pack(fill='x', pady=(10,0))
        
        # Botones de control
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill='x')
        
        # Botón principal más grande y destacado
        style = ttk.Style()
        style.configure('Execute.TButton', font=('Arial', 12, 'bold'))
        
        self.execute_btn = ttk.Button(button_frame, text="🔍 EJECUTAR VALIDACIÓN", 
                                     command=self.execute_validation, state='normal',
                                     style='Execute.TButton')
        self.execute_btn.pack(side='left', padx=(0,15), pady=5, ipady=8, ipadx=15)
        
        self.stop_btn = ttk.Button(button_frame, text="⏹ Detener", 
                                  command=self.stop_validation, state='disabled')
        self.stop_btn.pack(side='left', padx=(0,10))
        
        # Barra de progreso
        self.progress = ttk.Progressbar(control_frame, mode='indeterminate')
        self.progress.pack(fill='x', pady=(10,0))
        
        # Frame de resultados
        results_frame = ttk.LabelFrame(main_frame, text="Resultados", padding="10")
        results_frame.pack(fill='both', expand=True, pady=(10,0))
        
        # Área de texto para logs (más grande)
        self.log_text = scrolledtext.ScrolledText(results_frame, height=18, width=90)
        self.log_text.pack(fill='both', expand=True)
        
        # Frame de estadísticas
        stats_frame = ttk.Frame(results_frame)
        stats_frame.pack(fill='x', pady=(10,0))
        
        self.stats_label = ttk.Label(stats_frame, text="Listo para ejecutar", font=("Arial", 10))
        self.stats_label.pack(side='left')
        
        # Estado de licencia
        self.license_status = ttk.Label(stats_frame, text="Verificando licencia...", font=("Arial", 9))
        self.license_status.pack(side='right')
    
    def setup_config_tab(self):
        """Configurar tab de configuración"""
        if LocationConfigWidget is not None:
            # Widget de configuración de ubicaciones
            self.location_widget = LocationConfigWidget(self.config_frame, self.config, self.logger)
            
            # Override del método log_message para integrar con nuestros logs
            self.location_widget.log_message = self.log_message
        else:
            # Fallback: mensaje de configuración no disponible
            fallback_frame = ttk.Frame(self.config_frame, padding="20")
            fallback_frame.pack(fill='both', expand=True)
            
            ttk.Label(fallback_frame, text="⚠️ Widget de Configuración No Disponible", 
                     font=("Arial", 14, "bold"), foreground="orange").pack(pady=20)
            ttk.Label(fallback_frame, text="El widget de configuración de ubicaciones no se pudo cargar.", 
                     font=("Arial", 10)).pack()
            ttk.Label(fallback_frame, text="Verifique que location_config_widget.py esté en el directorio.", 
                     font=("Arial", 10)).pack(pady=5)
            
            # Crear widget mock para compatibilidad
            self.location_widget = MockLocationWidget()
    
    def setup_logs_tab(self):
        """Configurar tab de logs"""
        logs_main = ttk.Frame(self.logs_frame, padding="10")
        logs_main.pack(fill='both', expand=True)
        
        # Frame de control de logs
        log_control = ttk.Frame(logs_main)
        log_control.pack(fill='x', pady=(0,10))
        
        ttk.Label(log_control, text="Logs del Sistema:").pack(side='left')
        ttk.Button(log_control, text="🗑️ Limpiar", command=self.clear_logs).pack(side='right')
        ttk.Button(log_control, text="💾 Exportar", command=self.export_logs).pack(side='right', padx=(0,10))
        
        # Área de logs
        self.system_log_text = scrolledtext.ScrolledText(logs_main, height=25, width=100)
        self.system_log_text.pack(fill='both', expand=True)
        
        # Cargar logs existentes
        self.load_existing_logs()
    
    def setup_header(self, parent):
        """Configurar header con logo IGSON"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill='x', pady=(0,10))
        
        # Intentar cargar logo IGSON
        logo_loaded = False
        try:
            from PIL import Image, ImageTk
            logo_path = os.path.join(current_dir, "logo_igson.png")
            
            if os.path.exists(logo_path):
                img = Image.open(logo_path)
                # Mantener proporciones originales
                img.thumbnail((80, 80), Image.Resampling.LANCZOS)
                self.logo_photo = ImageTk.PhotoImage(img)
                
                logo_label = ttk.Label(header_frame, image=self.logo_photo)
                logo_label.pack(side='left', padx=(0,15))
                logo_loaded = True
                print("✅ Logo IGSON cargado exitosamente")
            else:
                print(f"❌ Logo no encontrado: {logo_path}")
        except ImportError:
            print("❌ PIL/Pillow no disponible. Instalar con: pip install Pillow")
        except Exception as e:
            print(f"❌ Error cargando logo: {e}")
        
        if not logo_loaded:
            # Logo de texto como fallback
            logo_text = ttk.Label(header_frame, text="🏢 IGSON", font=("Arial", 16, "bold"), foreground="blue")
            logo_text.pack(side='left', padx=(0,15))
        
        # Información de la aplicación
        info_frame = ttk.Frame(header_frame)
        info_frame.pack(side='left', fill='both', expand=True)
        
        ttk.Label(info_frame, text="IGSON - Gestor de Archivos Corporativo", 
                 font=("Arial", 16, "bold")).pack(anchor='w')
        ttk.Label(info_frame, text="Validación de Duplicados", 
                 font=("Arial", 12)).pack(anchor='w')
        ttk.Label(info_frame, text="Versión 2.0 - Junio 2025", 
                 font=("Arial", 9), foreground="gray").pack(anchor='w')
    
    def select_carpeta_origen(self):
        """Seleccionar carpeta origen"""
        folder = filedialog.askdirectory(title="Seleccionar Carpeta Origen")
        if folder:
            self.carpeta_origen.set(folder)
    
    def select_carpeta_rechazados(self):
        """Seleccionar carpeta rechazados"""
        folder = filedialog.askdirectory(title="Seleccionar Carpeta Rechazados")
        if folder:
            self.carpeta_rechazados.set(folder)
    
    def update_location_status(self):
        """Actualizar estado de todas las ubicaciones con VALIDACIÓN REAL (6 ubicaciones)"""
        def update_thread():
            try:
                self.log_message("🔄 Actualizando estado de todas las ubicaciones...")
                
                # Obtener ubicaciones habilitadas desde el widget de configuración
                enabled_locations = self.location_widget.get_enabled_locations()
                
                # Actualizar cada ubicación con VALIDACIÓN REAL
                for location_id in ['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4']:
                    if location_id in enabled_locations:
                        config = enabled_locations[location_id]
                        
                        # Usar la MISMA lógica de validación real del widget
                        if hasattr(self.location_widget, 'test_location_connectivity'):
                            # Test REAL de conectividad
                            status = self.location_widget.test_location_connectivity(location_id)
                            
                            # Mapear estados del widget a colores y textos para pestaña principal
                            if status == "✅ Conectado":
                                self.location_labels[location_id]['status'].config(text="✅ Conectado", foreground="green")
                                self.log_message(f"✅ {location_id.upper()}: Conectado")
                            elif status == "❌ Error red":
                                self.location_labels[location_id]['status'].config(text="❌ No accesible", foreground="red")
                                self.log_message(f"❌ {location_id.upper()}: No accesible (sin red)")
                            elif status == "🔐 Error credenciales":
                                self.location_labels[location_id]['status'].config(text="🔐 Error credenciales", foreground="orange")
                                self.log_message(f"🔐 {location_id.upper()}: Error credenciales")
                            elif status == "❓ Sin configurar":
                                self.location_labels[location_id]['status'].config(text="❓ Sin configurar", foreground="gray")
                                self.log_message(f"❓ {location_id.upper()}: Sin configurar")
                            else:
                                # Estado desconocido
                                self.location_labels[location_id]['status'].config(text="❌ Error", foreground="red")
                                self.log_message(f"❌ {location_id.upper()}: Error desconocido")
                        else:
                            # Fallback si no hay validación real disponible
                            self.location_labels[location_id]['status'].config(text="⚠️ Solo configurado", foreground="orange")
                            self.log_message(f"⚠️ {location_id.upper()}: Solo configurado (sin test real)")
                    else:
                        self.location_labels[location_id]['status'].config(text="⚪ Deshabilitado", foreground="gray")
                
                self.log_message("✅ Estado de ubicaciones actualizado con validación real")
                
            except Exception as e:
                self.log_message(f"❌ Error actualizando estado: {e}")
        
        thread = threading.Thread(target=update_thread)
        thread.daemon = True
        thread.start()
    
    def load_default_config(self):
        """Cargar configuración por defecto para testing"""
        # Crear entorno de prueba si no existe
        base_dir = os.path.join(current_dir, "test_validacion")
        
        if not os.path.exists(base_dir):
            try:
                self.create_test_environment(base_dir)
                self.log_message("✅ Entorno de prueba creado")
            except Exception as e:
                self.log_message(f"❌ Error creando entorno: {e}")
        
        # Configurar rutas por defecto
        self.carpeta_origen.set(os.path.join(base_dir, "origen"))
        self.carpeta_rechazados.set(os.path.join(base_dir, "rechazados"))
        
        # Actualizar estado inicial
        self.update_location_status()
    
    def create_test_environment(self, base_dir):
        """Crear entorno de prueba"""
        os.makedirs(base_dir, exist_ok=True)
        
        # Crear carpetas de prueba (6 ubicaciones + origen/rechazados)
        folders = ["origen", "rechazados", "qnap1", "qnap2", "pc1", "pc2", "pc3", "pc4"]
        for folder in folders:
            os.makedirs(os.path.join(base_dir, folder), exist_ok=True)
        
        # Crear archivos de prueba
        test_files = ["documento1.pdf", "imagen1.jpg", "texto1.txt", "video1.mp4", "archivo1.docx"]
        
        # En origen
        origen_dir = os.path.join(base_dir, "origen")
        for file in test_files:
            with open(os.path.join(origen_dir, file), 'w') as f:
                f.write(f"Contenido de {file} - Timestamp: {datetime.now()}")
        
        # Duplicados en ubicaciones (simular duplicados distribuidos)
        locations = {
            'qnap1': os.path.join(base_dir, "qnap1"),
            'qnap2': os.path.join(base_dir, "qnap2"),
            'pc1': os.path.join(base_dir, "pc1"),
            'pc2': os.path.join(base_dir, "pc2"),
            'pc3': os.path.join(base_dir, "pc3"),
            'pc4': os.path.join(base_dir, "pc4")
        }
        
        for i, file in enumerate(test_files):
            # Distribuir archivos en diferentes ubicaciones para simular duplicados
            if i == 0:  # documento1.pdf: en qnap1 y pc1
                for loc in ['qnap1', 'pc1']:
                    with open(os.path.join(locations[loc], file), 'w') as f:
                        f.write(f"Contenido de {file} en {loc.upper()}")
            elif i == 1:  # imagen1.jpg: en qnap2 y pc2
                for loc in ['qnap2', 'pc2']:
                    with open(os.path.join(locations[loc], file), 'w') as f:
                        f.write(f"Contenido de {file} en {loc.upper()}")
            elif i == 2:  # texto1.txt: en pc3
                with open(os.path.join(locations['pc3'], file), 'w') as f:
                    f.write(f"Contenido de {file} en PC3")
            # Los demás archivos (video1.mp4, archivo1.docx) no tienen duplicados
    
    def log_message(self, message):
        """Agregar mensaje al log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        # Log en tab de operación
        if hasattr(self, 'log_text'):
            self.log_text.insert(tk.END, formatted_message)
            self.log_text.see(tk.END)
        
        # Log en tab de logs del sistema
        if hasattr(self, 'system_log_text'):
            self.system_log_text.insert(tk.END, formatted_message)
            self.system_log_text.see(tk.END)
        
        self.root.update_idletasks()
        
        # Log al sistema
        if self.logger:
            self.logger.info(message)
    
    def execute_validation(self):
        """Ejecutar validación de duplicados con configuración real"""
        if self.running:
            return
        
        # Verificar configuración
        if not self.carpeta_origen.get() or not self.carpeta_rechazados.get():
            messagebox.showerror("Error", "Debe configurar las carpetas origen y rechazados")
            return
        
        if not os.path.exists(self.carpeta_origen.get()):
            messagebox.showerror("Error", "La carpeta origen no existe")
            return
        
        # Verificar licencia obligatoria
        if not self.licensed:
            messagebox.showerror("Licencia Requerida", 
                "Esta aplicación requiere una licencia válida para funcionar.")
            return
        
        # Verificar que hay ubicaciones habilitadas
        enabled_locations = self.location_widget.get_enabled_locations()
        
        if not enabled_locations:
            messagebox.showerror("Error", "Debe habilitar al menos una ubicación en la configuración")
            return
        
        # Iniciar ejecución en hilo separado
        self.running = True
        self.execute_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.progress.start()
        
        thread = threading.Thread(target=self._execute_validation_thread)
        thread.daemon = True
        thread.start()
    
    def _execute_validation_thread(self):
        """Hilo de ejecución de validación con handlers reales"""
        try:
            self.log_message("🔍 Iniciando validación de duplicados...")
            self.log_message(f"📁 Carpeta origen: {self.carpeta_origen.get()}")
            self.log_message(f"📁 Carpeta rechazados: {self.carpeta_rechazados.get()}")
            self.log_message(f"🔧 Modo: {'Simulación' if self.dry_run.get() else 'Real'}")
            
            # Obtener configuración de ubicaciones
            enabled_locations = self.location_widget.get_enabled_locations()
            all_locations = enabled_locations  # Incluir QNAPs Y PCs
            
            self.log_message(f"📋 Ubicaciones habilitadas: {', '.join(all_locations.keys())}")
            
            # Inicializar handlers para todas las ubicaciones
            location_handlers = {}
            for location_id, location_config in all_locations.items():
                try:
                    if location_id.startswith('qnap'):
                        # QNAP Handler
                        handler = QnapHandler(location_id)
                        location_path = location_config['path']
                        if location_config['subfolder']:
                            location_path = os.path.join(location_path, location_config['subfolder'])
                        
                        self.log_message(f"🔌 Configurando {location_id.upper()}: {location_path}")
                        location_handlers[location_id] = {'handler': handler, 'path': location_path, 'type': 'qnap'}
                    
                    else:
                        # PC Handler
                        handler = PCHandler(location_id)
                        hostname = location_config.get('hostname', '')
                        share = location_config.get('share', '')
                        subfolder = location_config.get('subfolder', '')
                        
                        location_path = f"\\\\{hostname}\\{share}"
                        if subfolder:
                            location_path = os.path.join(location_path, subfolder)
                        
                        self.log_message(f"🔌 Configurando {location_id.upper()}: {location_path}")
                        location_handlers[location_id] = {'handler': handler, 'path': location_path, 'type': 'pc'}
                    
                except Exception as e:
                    self.log_message(f"❌ Error configurando {location_id}: {e}")
            
            # Procesar archivos en carpeta origen
            files_found = []
            origin_dir = self.carpeta_origen.get()
            
            if os.path.exists(origin_dir):
                for file in os.listdir(origin_dir):
                    if os.path.isfile(os.path.join(origin_dir, file)):
                        files_found.append(file)
            
            self.log_message(f"📋 Archivos encontrados en origen: {len(files_found)}")
            
            duplicates_found = 0
            files_processed = 0
            
            # Verificar duplicados en cada QNAP
            for file in files_found:
                if not self.running:
                    break
                
                self.log_message(f"🔍 Verificando: {file}")
                time.sleep(0.3)  # Simular procesamiento
                
                is_duplicate = False
                duplicate_location = None
                
                # Buscar en cada ubicación habilitada
                for location_id, location_info in location_handlers.items():
                    location_path = location_info['path']
                    location_type = location_info['type']
                    
                    # Verificar si existe el archivo
                    if location_type == 'qnap':
                        # QNAP: búsqueda directa en filesystem
                        file_path = os.path.join(location_path, file)
                        if os.path.exists(file_path) and os.path.isfile(file_path):
                            is_duplicate = True
                            duplicate_location = location_id.upper()
                            break
                    else:
                        # PC: simular búsqueda SMB
                        hostname = all_locations[location_id].get('hostname', '')
                        if hostname:
                            # Simular que encuentra archivos (33% probabilidad)
                            if random.choice([True, False, False]):
                                is_duplicate = True
                                duplicate_location = location_id.upper()
                                break
                
                if is_duplicate:
                    duplicates_found += 1
                    self.log_message(f"❌ DUPLICADO: {file} (encontrado en {duplicate_location})")
                    
                    if not self.dry_run.get():
                        # Mover archivo real
                        source_path = os.path.join(origin_dir, file)
                        dest_path = os.path.join(self.carpeta_rechazados.get(), file)
                        try:
                            os.makedirs(self.carpeta_rechazados.get(), exist_ok=True)
                            if os.path.exists(source_path):
                                # Generar nombre único si ya existe
                                counter = 1
                                original_dest = dest_path
                                while os.path.exists(dest_path):
                                    name, ext = os.path.splitext(original_dest)
                                    dest_path = f"{name}_{counter}{ext}"
                                    counter += 1
                                
                                os.rename(source_path, dest_path)
                                self.log_message(f"📤 Movido: {file} → rechazados")
                        except Exception as e:
                            self.log_message(f"❌ Error moviendo {file}: {e}")
                    else:
                        self.log_message(f"💾 (SIMULADO) Mover: {file} → rechazados")
                else:
                    self.log_message(f"✅ OK: {file} (no es duplicado)")
                
                files_processed += 1
                
                # Actualizar estadísticas
                stats_text = f"Procesados: {files_processed}/{len(files_found)} | Duplicados: {duplicates_found}"
                self.root.after(0, lambda: self.stats_label.config(text=stats_text))
            
            # Resultados finales
            if self.running:
                self.log_message("=" * 50)
                self.log_message(f"✅ VALIDACIÓN COMPLETADA")
                self.log_message(f"📊 Archivos procesados: {files_processed}")
                self.log_message(f"🔍 Duplicados encontrados: {duplicates_found}")
                self.log_message(f"📁 Archivos únicos: {files_processed - duplicates_found}")
                self.log_message(f"🏷️ Ubicaciones verificadas: {', '.join(location_handlers.keys())}")
                
                if self.dry_run.get():
                    self.log_message("💾 Modo simulación - No se realizaron cambios")
                else:
                    self.log_message(f"📤 Archivos movidos a rechazados: {duplicates_found}")
                
                self.log_message("=" * 50)
                
                # Mostrar resumen
                self.root.after(0, lambda: messagebox.showinfo("Completado", 
                    f"Validación completada\n\n"
                    f"Archivos procesados: {files_processed}\n"
                    f"Duplicados encontrados: {duplicates_found}\n"
                    f"Ubicaciones verificadas: {len(location_handlers)}\n"
                    f"Modo: {'Simulación' if self.dry_run.get() else 'Real'}"))
            
        except Exception as e:
            self.log_message(f"❌ ERROR: {e}")
            self.logger.error(f"Validation error: {e}")
        
        finally:
            # Restaurar UI
            self.running = False
            self.root.after(0, self._reset_ui)
    
    def _reset_ui(self):
        """Restaurar estado de la UI"""
        self.execute_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.progress.stop()
    
    def stop_validation(self):
        """Detener validación"""
        if self.running:
            self.running = False
            self.log_message("⏹ Deteniendo validación...")
    
    def clear_logs(self):
        """Limpiar logs"""
        if hasattr(self, 'log_text'):
            self.log_text.delete(1.0, tk.END)
        if hasattr(self, 'system_log_text'):
            self.system_log_text.delete(1.0, tk.END)
        self.log_message("🗑️ Logs limpiados")
    
    def export_logs(self):
        """Exportar logs"""
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                title="Exportar Logs"
            )
            
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    if hasattr(self, 'system_log_text'):
                        content = self.system_log_text.get(1.0, tk.END)
                        f.write(content)
                
                self.log_message(f"💾 Logs exportados a: {filename}")
                messagebox.showinfo("Éxito", f"Logs exportados a:\n{filename}")
        
        except Exception as e:
            self.log_message(f"❌ Error exportando logs: {e}")
            messagebox.showerror("Error", f"Error exportando logs: {e}")
    
    def load_existing_logs(self):
        """Cargar logs existentes del sistema"""
        try:
            # Cargar logs del día actual
            log_dir = os.path.join(current_dir, "logs")
            if os.path.exists(log_dir):
                today = datetime.now().strftime("%Y%m%d")
                log_file = os.path.join(log_dir, f"validacion_{today}.log")
                
                if os.path.exists(log_file):
                    with open(log_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if hasattr(self, 'system_log_text'):
                            self.system_log_text.insert(tk.END, content)
                            self.system_log_text.see(tk.END)
        except Exception as e:
            print(f"Error cargando logs existentes: {e}")
    
    def run(self):
        """Ejecutar aplicación"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.logger.info("Application stopped by user")
        except Exception as e:
            self.logger.error(f"Application error: {e}")

if __name__ == "__main__":
    print("🔍 Gestor - Validación de Duplicados GUI Real")
    print("=" * 45)
    
    app = ValidationDuplicatesGUI()
    app.run()
