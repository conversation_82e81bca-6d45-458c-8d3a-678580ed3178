2025-06-17 08:42:39 - gestor.sistema - INFO - <PERSON>gger 'sistema' inicializado - Sistema general y errores
2025-06-17 08:42:39 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 08:42:39 - gestor.sistema - INFO - === GESTOR DE ARCHIVOS CORPORATIVO INICIADO ===
2025-06-17 08:42:39 - gestor.sistema - INFO - Directorio de trabajo: D:\sports_manager\gestor_archivos_corporativo
2025-06-17 08:42:39 - gestor.sistema - INFO - Python: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-06-17 08:42:40 - gestor.sistema - INFO - Aplicación: Gestor de Archivos Corporativo v1.0
2025-06-17 08:42:40 - gestor.sistema - INFO - Modo: Desarrollo
2025-06-17 08:42:40 - gestor.sistema - INFO - Ubicaciones configuradas: 6
2025-06-17 08:42:40 - gestor.sistema - WARNING - Advertencias de configuración:
2025-06-17 08:42:40 - gestor.sistema - WARNING -   - Location qnap1: No username configured
2025-06-17 08:42:40 - gestor.sistema - WARNING -   - Location qnap2: No username configured
2025-06-17 08:42:40 - gestor.sistema - WARNING -   - Location pc1: No username configured
2025-06-17 08:42:40 - gestor.sistema - WARNING -   - Location pc2: No username configured
2025-06-17 08:42:40 - gestor.sistema - WARNING -   - Location pc3: No username configured
2025-06-17 08:42:40 - gestor.sistema - WARNING -   - Location pc4: No username configured
2025-06-17 08:42:40 - gestor.sistema - INFO - Iniciando interfaz gráfica...
2025-06-17 08:42:41 - gestor.sistema - ERROR - Error fatal en la aplicación: 'GestorDashboard' object has no attribute 'refresh_all'
2025-06-17 08:42:41 - gestor.sistema - ERROR - Detalles del error:
Traceback (most recent call last):
  File "d:\sports_manager\gestor_archivos_corporativo\main.py", line 103, in main
    dashboard = GestorDashboard()
  File "d:\sports_manager\gestor_archivos_corporativo\dashboard.py", line 30, in __init__
    self.setup_ui()  # Crear ventana principal PRIMERO
    ~~~~~~~~~~~~~^^
  File "d:\sports_manager\gestor_archivos_corporativo\dashboard.py", line 88, in setup_ui
    self.create_main_structure()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "d:\sports_manager\gestor_archivos_corporativo\dashboard.py", line 138, in create_main_structure
    self.setup_status_bar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "d:\sports_manager\gestor_archivos_corporativo\dashboard.py", line 220, in setup_status_bar
    command=self.refresh_all).pack(side=tk.RIGHT, padx=5)
            ^^^^^^^^^^^^^^^^
AttributeError: 'GestorDashboard' object has no attribute 'refresh_all'. Did you mean: 'refresh_logs'?
2025-06-17 08:43:59 - gestor.sistema - INFO - === GESTOR DE ARCHIVOS CORPORATIVO FINALIZADO ===
2025-06-17 08:44:13 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 08:44:13 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 08:44:13 - gestor.sistema - INFO - === GESTOR DE ARCHIVOS CORPORATIVO INICIADO ===
2025-06-17 08:44:13 - gestor.sistema - INFO - Directorio de trabajo: D:\sports_manager\gestor_archivos_corporativo
2025-06-17 08:44:13 - gestor.sistema - INFO - Python: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-06-17 08:44:13 - gestor.sistema - INFO - Aplicación: Gestor de Archivos Corporativo v1.0
2025-06-17 08:44:13 - gestor.sistema - INFO - Modo: Desarrollo
2025-06-17 08:44:13 - gestor.sistema - INFO - Ubicaciones configuradas: 6
2025-06-17 08:44:13 - gestor.sistema - WARNING - Advertencias de configuración:
2025-06-17 08:44:13 - gestor.sistema - WARNING -   - Location qnap1: No username configured
2025-06-17 08:44:13 - gestor.sistema - WARNING -   - Location qnap2: No username configured
2025-06-17 08:44:13 - gestor.sistema - WARNING -   - Location pc1: No username configured
2025-06-17 08:44:13 - gestor.sistema - WARNING -   - Location pc2: No username configured
2025-06-17 08:44:13 - gestor.sistema - WARNING -   - Location pc3: No username configured
2025-06-17 08:44:13 - gestor.sistema - WARNING -   - Location pc4: No username configured
2025-06-17 08:44:13 - gestor.sistema - INFO - Iniciando interfaz gráfica...
2025-06-17 08:44:13 - gestor.sistema - ERROR - Error fatal en la aplicación: 'GestorDashboard' object has no attribute 'refresh_all'
2025-06-17 08:44:13 - gestor.sistema - ERROR - Detalles del error:
Traceback (most recent call last):
  File "d:\sports_manager\gestor_archivos_corporativo\main.py", line 103, in main
    dashboard = GestorDashboard()
  File "d:\sports_manager\gestor_archivos_corporativo\dashboard.py", line 30, in __init__
    self.setup_ui()  # Crear ventana principal PRIMERO
    ~~~~~~~~~~~~~^^
  File "d:\sports_manager\gestor_archivos_corporativo\dashboard.py", line 88, in setup_ui
    self.create_main_structure()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "d:\sports_manager\gestor_archivos_corporativo\dashboard.py", line 138, in create_main_structure
    self.setup_status_bar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "d:\sports_manager\gestor_archivos_corporativo\dashboard.py", line 220, in setup_status_bar
    command=self.refresh_all).pack(side=tk.RIGHT, padx=5)
            ^^^^^^^^^^^^^^^^
AttributeError: 'GestorDashboard' object has no attribute 'refresh_all'. Did you mean: 'refresh_logs'?
2025-06-17 08:44:22 - gestor.sistema - INFO - === GESTOR DE ARCHIVOS CORPORATIVO FINALIZADO ===
2025-06-17 08:44:29 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 08:44:29 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 08:44:37 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 08:44:37 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 08:45:06 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 08:45:06 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 08:45:10 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 08:45:10 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 08:51:36 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 08:51:36 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 08:52:30 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 08:52:30 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 08:53:49 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 08:53:49 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 08:55:40 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 08:55:40 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 09:19:13 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 09:19:13 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 09:23:51 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 09:23:51 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 09:25:55 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 09:25:55 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 09:27:35 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 09:27:35 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 13:57:34 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 13:57:34 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 13:58:52 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 13:58:52 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 14:02:17 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 14:02:17 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 14:05:10 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 14:05:10 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 14:09:11 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 14:09:11 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 14:10:16 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 14:10:16 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 14:14:28 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 14:14:28 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 14:16:19 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 14:16:19 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 14:22:35 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 14:22:35 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 14:22:52 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 14:22:52 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 14:30:02 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 14:30:02 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 14:30:22 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 14:30:22 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 14:30:38 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 14:30:38 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 14:30:38 - gestor.sistema - INFO - === GESTOR DE ARCHIVOS CORPORATIVO INICIADO ===
2025-06-17 14:30:38 - gestor.sistema - INFO - Directorio de trabajo: D:\sports_manager\gestor_archivos_corporativo
2025-06-17 14:30:38 - gestor.sistema - INFO - Python: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
2025-06-17 14:30:38 - gestor.sistema - INFO - Aplicación: Gestor de Archivos Corporativo v1.0
2025-06-17 14:30:38 - gestor.sistema - INFO - Modo: Desarrollo
2025-06-17 14:30:38 - gestor.sistema - INFO - Ubicaciones configuradas: 6
2025-06-17 14:30:38 - gestor.sistema - WARNING - Advertencias de configuración:
2025-06-17 14:30:38 - gestor.sistema - WARNING -   - Location qnap1: No username configured
2025-06-17 14:30:38 - gestor.sistema - WARNING -   - Location qnap2: No username configured
2025-06-17 14:30:38 - gestor.sistema - WARNING -   - Location pc1: No username configured
2025-06-17 14:30:38 - gestor.sistema - WARNING -   - Location pc2: No username configured
2025-06-17 14:30:38 - gestor.sistema - WARNING -   - Location pc3: No username configured
2025-06-17 14:30:38 - gestor.sistema - WARNING -   - Location pc4: No username configured
2025-06-17 14:30:38 - gestor.sistema - INFO - Iniciando interfaz gráfica...
2025-06-17 14:30:38 - gestor.sistema - ERROR - Error fatal en la aplicación: 'GestorDashboard' object has no attribute 'refresh_all'
2025-06-17 14:30:38 - gestor.sistema - ERROR - Detalles del error:
Traceback (most recent call last):
  File "D:\sports_manager\gestor_archivos_corporativo\main_with_logo.py", line 200, in main
    dashboard = GestorDashboard()
  File "D:\sports_manager\gestor_archivos_corporativo\dashboard.py", line 30, in __init__
    self.setup_ui()  # Crear ventana principal PRIMERO
    ~~~~~~~~~~~~~^^
  File "D:\sports_manager\gestor_archivos_corporativo\dashboard.py", line 88, in setup_ui
    self.create_main_structure()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\sports_manager\gestor_archivos_corporativo\dashboard.py", line 138, in create_main_structure
    self.setup_status_bar()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\sports_manager\gestor_archivos_corporativo\dashboard.py", line 220, in setup_status_bar
    command=self.refresh_all).pack(side=tk.RIGHT, padx=5)
            ^^^^^^^^^^^^^^^^
AttributeError: 'GestorDashboard' object has no attribute 'refresh_all'. Did you mean: 'refresh_logs'?
2025-06-17 14:34:13 - gestor.sistema - INFO - === GESTOR DE ARCHIVOS CORPORATIVO FINALIZADO ===
2025-06-17 14:34:18 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 14:34:18 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
2025-06-17 14:38:41 - gestor.sistema - INFO - Logger 'sistema' inicializado - Sistema general y errores
2025-06-17 14:38:41 - gestor.sistema - INFO - === Sistema de Logging Iniciado ===
