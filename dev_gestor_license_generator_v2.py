#!/usr/bin/env python3
"""
Generador de Licencias Gestor v2 - Solo Validación y Limpieza
============================================================

Versión simplificada para las aplicaciones de validación y limpieza únicamente
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import sys
from datetime import datetime, timedelta
import base64
import json
import hashlib
from pathlib import Path

# Agregar directorio actual al path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

class LicenseGeneratorV2:
    """Generador de licencias simplificado para Validación y Limpieza"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Generador de Licencias - Gestor v2.0")
        self.root.geometry("600x700")
        self.root.resizable(False, False)
        
        # Variables
        self.client_name = tk.StringVar()
        self.app_selection = tk.StringVar(value="validacion")
        self.license_type = tk.StringVar(value="trial")
        self.trial_days = tk.IntVar(value=30)
        self.max_executions = tk.IntVar(value=50)
        
        # Clave de cifrado (en producción debería ser más segura)
        self.encryption_key = "IGSON_GESTOR_2025_SECRET_KEY"
        
        self.setup_ui()
    
    def setup_ui(self):
        """Configurar interfaz de usuario"""
        # Header
        header_frame = ttk.Frame(self.root, padding="20")
        header_frame.pack(fill='x')
        
        ttk.Label(header_frame, text="🏢 IGSON - Generador de Licencias v2", 
                 font=("Arial", 16, "bold")).pack()
        ttk.Label(header_frame, text="Gestor de Archivos Corporativo", 
                 font=("Arial", 12)).pack()
        ttk.Label(header_frame, text="Validación y Limpieza", 
                 font=("Arial", 10), foreground="blue").pack(pady=(0,10))
        
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill='both', expand=True)
        
        # Información del cliente
        client_frame = ttk.LabelFrame(main_frame, text="Información del Cliente", padding="15")
        client_frame.pack(fill='x', pady=(0,15))
        
        ttk.Label(client_frame, text="Nombre del Cliente:").pack(anchor='w')
        ttk.Entry(client_frame, textvariable=self.client_name, width=40).pack(fill='x', pady=(5,0))
        
        # Selección de aplicación
        app_frame = ttk.LabelFrame(main_frame, text="Aplicación", padding="15")
        app_frame.pack(fill='x', pady=(0,15))
        
        ttk.Radiobutton(app_frame, text="🔍 Validación de Duplicados", 
                       variable=self.app_selection, value="validacion").pack(anchor='w', pady=2)
        ttk.Radiobutton(app_frame, text="🗑️ Limpieza por Lista", 
                       variable=self.app_selection, value="limpieza").pack(anchor='w', pady=2)
        
        # Tipo de licencia
        license_frame = ttk.LabelFrame(main_frame, text="Tipo de Licencia", padding="15")
        license_frame.pack(fill='x', pady=(0,15))
        
        ttk.Radiobutton(license_frame, text="Trial (Limitada en tiempo)", 
                       variable=self.license_type, value="trial",
                       command=self.on_license_type_change).pack(anchor='w', pady=2)
        ttk.Radiobutton(license_frame, text="Completa (Sin limitaciones)", 
                       variable=self.license_type, value="full",
                       command=self.on_license_type_change).pack(anchor='w', pady=2)
        
        # Configuración de límites
        limits_frame = ttk.LabelFrame(main_frame, text="Configuración de Límites", padding="15")
        limits_frame.pack(fill='x', pady=(0,15))
        
        # Trial days
        self.trial_frame = ttk.Frame(limits_frame)
        self.trial_frame.pack(fill='x', pady=2)
        
        ttk.Label(self.trial_frame, text="Días de prueba:").pack(side='left')
        ttk.Spinbox(self.trial_frame, from_=7, to=365, textvariable=self.trial_days, 
                   width=10).pack(side='left', padx=(10,0))
        
        # Max executions
        exec_frame = ttk.Frame(limits_frame)
        exec_frame.pack(fill='x', pady=2)
        
        ttk.Label(exec_frame, text="Máximo ejecuciones:").pack(side='left')
        ttk.Spinbox(exec_frame, from_=10, to=1000, textvariable=self.max_executions, 
                   width=10).pack(side='left', padx=(10,0))
        
        # Vista previa
        preview_frame = ttk.LabelFrame(main_frame, text="Vista Previa", padding="15")
        preview_frame.pack(fill='both', expand=True, pady=(0,15))
        
        self.preview_text = tk.Text(preview_frame, height=8, width=50, wrap='word')
        scrollbar = ttk.Scrollbar(preview_frame, orient='vertical', command=self.preview_text.yview)
        self.preview_text.configure(yscrollcommand=scrollbar.set)
        
        self.preview_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # Botones
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(0,10))
        
        ttk.Button(button_frame, text="🔄 Actualizar Vista Previa", 
                  command=self.update_preview).pack(side='left', padx=(0,10))
        ttk.Button(button_frame, text="💾 Generar Licencia", 
                  command=self.generate_license).pack(side='left', padx=(0,10))
        ttk.Button(button_frame, text="📂 Validar Licencia", 
                  command=self.validate_license_file).pack(side='left')
        
        # Estado
        self.status_label = ttk.Label(main_frame, text="Listo para generar licencias", 
                                     foreground="blue")
        self.status_label.pack(pady=(10,0))
        
        # Configuración inicial
        self.on_license_type_change()
        self.update_preview()
    
    def on_license_type_change(self):
        """Manejar cambio de tipo de licencia"""
        if self.license_type.get() == "trial":
            for widget in self.trial_frame.winfo_children():
                widget.configure(state='normal')
        else:
            for widget in self.trial_frame.winfo_children():
                if isinstance(widget, ttk.Spinbox):
                    widget.configure(state='disabled')
    
    def update_preview(self):
        """Actualizar vista previa de la licencia"""
        license_data = self.create_license_data()
        
        preview = f"""LICENCIA GESTOR v2.0
{'='*40}

Cliente: {license_data['client_name'] or 'No especificado'}
Aplicación: {license_data['app_name']}
Tipo: {license_data['license_type_display']}

Generada: {license_data['issue_date']}
Válida hasta: {license_data['expiry_date'] or 'Sin límite'}

Límites:
- Ejecuciones máximas: {license_data['max_executions']}
- Hardware ID: {license_data['hardware_id']}

Estado: ✅ Válida
"""
        
        self.preview_text.delete(1.0, tk.END)
        self.preview_text.insert(1.0, preview)
    
    def create_license_data(self):
        """Crear datos de la licencia"""
        app_names = {
            "validacion": "Validación de Duplicados",
            "limpieza": "Limpieza por Lista"
        }
        
        license_types = {
            "trial": "Trial",
            "full": "Completa"
        }
        
        # Fechas
        issue_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        if self.license_type.get() == "trial":
            expiry_date = (datetime.now() + timedelta(days=self.trial_days.get())).strftime("%Y-%m-%d %H:%M:%S")
        else:
            expiry_date = None
        
        # Hardware ID simulado
        hardware_id = hashlib.md5(f"{self.client_name.get()}-{self.app_selection.get()}".encode()).hexdigest()[:8].upper()
        
        return {
            "version": "2.0",
            "client_name": self.client_name.get(),
            "app_id": self.app_selection.get(),
            "app_name": app_names[self.app_selection.get()],
            "license_type": self.license_type.get(),
            "license_type_display": license_types[self.license_type.get()],
            "issue_date": issue_date,
            "expiry_date": expiry_date,
            "max_executions": self.max_executions.get(),
            "hardware_id": hardware_id,
            "generator": "IGSON License Generator v2.0"
        }
    
    def encrypt_license(self, license_data):
        """Cifrar datos de licencia (simulado)"""
        json_data = json.dumps(license_data, indent=2)
        
        # Cifrado simple con base64 (en producción usar cifrado real)
        encrypted_data = base64.b64encode(json_data.encode()).decode()
        
        # Añadir checksum
        checksum = hashlib.sha256(encrypted_data.encode()).hexdigest()[:16]
        
        return f"IGSON-LIC-V2:{checksum}:{encrypted_data}"
    
    def generate_license(self):
        """Generar archivo de licencia"""
        if not self.client_name.get().strip():
            messagebox.showerror("Error", "Debe especificar el nombre del cliente")
            return
        
        try:
            # Crear datos de licencia
            license_data = self.create_license_data()
            
            # Cifrar licencia
            encrypted_license = self.encrypt_license(license_data)
            
            # Generar nombre de archivo
            safe_client = "".join(c for c in self.client_name.get() if c.isalnum() or c in (' ', '-', '_')).strip()
            safe_client = safe_client.replace(' ', '_')
            
            filename = f"license_{safe_client}_{self.app_selection.get()}.lic"
            
            # Guardar archivo
            file_path = filedialog.asksaveasfilename(
                title="Guardar Licencia",
                defaultextension=".lic",
                filetypes=[("License files", "*.lic"), ("All files", "*.*")],
                initialname=filename
            )
            
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(encrypted_license)
                
                self.status_label.config(text=f"✅ Licencia generada: {Path(file_path).name}", 
                                       foreground="green")
                
                messagebox.showinfo("Éxito", 
                    f"Licencia generada exitosamente:\n\n"
                    f"Cliente: {license_data['client_name']}\n"
                    f"Aplicación: {license_data['app_name']}\n"
                    f"Tipo: {license_data['license_type_display']}\n"
                    f"Archivo: {Path(file_path).name}")
            
        except Exception as e:
            self.status_label.config(text=f"❌ Error: {e}", foreground="red")
            messagebox.showerror("Error", f"Error generando licencia: {e}")
    
    def validate_license_file(self):
        """Validar archivo de licencia existente"""
        file_path = filedialog.askopenfilename(
            title="Seleccionar Licencia",
            filetypes=[("License files", "*.lic"), ("All files", "*.*")]
        )
        
        if not file_path:
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                license_content = f.read().strip()
            
            # Validar formato
            if not license_content.startswith("IGSON-LIC-V2:"):
                raise ValueError("Formato de licencia inválido")
            
            # Extraer partes
            parts = license_content.split(":", 2)
            if len(parts) != 3:
                raise ValueError("Estructura de licencia inválida")
            
            header, checksum, encrypted_data = parts
            
            # Validar checksum
            expected_checksum = hashlib.sha256(encrypted_data.encode()).hexdigest()[:16]
            if checksum != expected_checksum:
                raise ValueError("Licencia corrupta o modificada")
            
            # Descifrar
            decrypted_data = base64.b64decode(encrypted_data.encode()).decode()
            license_data = json.loads(decrypted_data)
            
            # Validar expiración
            is_expired = False
            expiry_status = "Sin límite"
            
            if license_data.get('expiry_date'):
                expiry_date = datetime.strptime(license_data['expiry_date'], "%Y-%m-%d %H:%M:%S")
                if datetime.now() > expiry_date:
                    is_expired = True
                    expiry_status = f"❌ Expirada el {license_data['expiry_date']}"
                else:
                    days_left = (expiry_date - datetime.now()).days
                    expiry_status = f"✅ Válida ({days_left} días restantes)"
            
            # Mostrar información
            status_icon = "❌" if is_expired else "✅"
            validation_result = f"""VALIDACIÓN DE LICENCIA
{'='*40}

{status_icon} Estado: {'EXPIRADA' if is_expired else 'VÁLIDA'}

Cliente: {license_data.get('client_name', 'N/A')}
Aplicación: {license_data.get('app_name', 'N/A')}
Tipo: {license_data.get('license_type_display', 'N/A')}

Generada: {license_data.get('issue_date', 'N/A')}
Expiración: {expiry_status}

Ejecuciones máximas: {license_data.get('max_executions', 'N/A')}
Hardware ID: {license_data.get('hardware_id', 'N/A')}

Generador: {license_data.get('generator', 'N/A')}
"""
            
            # Mostrar en preview
            self.preview_text.delete(1.0, tk.END)
            self.preview_text.insert(1.0, validation_result)
            
            if is_expired:
                self.status_label.config(text="❌ Licencia EXPIRADA", foreground="red")
                messagebox.showwarning("Licencia Expirada", "La licencia ha expirado")
            else:
                self.status_label.config(text="✅ Licencia VÁLIDA", foreground="green")
                messagebox.showinfo("Licencia Válida", "La licencia es válida")
            
        except Exception as e:
            self.status_label.config(text=f"❌ Error validando: {e}", foreground="red")
            messagebox.showerror("Error", f"Error validando licencia: {e}")
    
    def run(self):
        """Ejecutar aplicación"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🏢 IGSON - Generador de Licencias v2.0")
    print("📋 Aplicaciones: Validación y Limpieza")
    print("=" * 40)
    
    app = LicenseGeneratorV2()
    app.run()
