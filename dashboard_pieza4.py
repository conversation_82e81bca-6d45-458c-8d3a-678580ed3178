"""
dashboard_pieza4.py
PIEZA 4: Sistema de Logs Avanzado + Pestaña Logs completa
Gestor de Archivos Corporativo
"""

# CONTINUACIÓN DE LA CLASE GestorDashboard - SISTEMA DE LOGS AVANZADO

    def create_logs_tab(self):
        """Crear pestaña de logs avanzada"""
        logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(logs_frame, text="📝 Logs")
        
        # Header con controles principales
        header_frame = ttk.Frame(logs_frame)
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(header_frame, text="📝 Sistema de Logs", 
                 style='Title.TLabel').pack(side=tk.LEFT)
        
        # Controles de tiempo real
        realtime_frame = ttk.Frame(header_frame)
        realtime_frame.pack(side=tk.RIGHT)
        
        self.logs_realtime_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(realtime_frame, text="🔴 En Vivo", 
                       variable=self.logs_realtime_var,
                       command=self.toggle_realtime_logs).pack(side=tk.RIGHT, padx=5)
        
        # Frame principal con múltiples vistas
        main_logs_frame = ttk.Frame(logs_frame)
        main_logs_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        # Panel de control lateral
        control_panel = ttk.LabelFrame(main_logs_frame, text="🎛️ Control", padding="10")
        control_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # Categorías de logs
        ttk.Label(control_panel, text="📂 Categoría:", style='Status.TLabel').pack(anchor=tk.W, pady=(0, 5))
        
        self.log_categories = ['sistema', 'limpieza', 'validacion', 'conectividad', 'todos']
        category_frame = ttk.Frame(control_panel)
        category_frame.pack(fill=tk.X, pady=(0, 10))
        
        for category in self.log_categories:
            rb = ttk.Radiobutton(category_frame, text=category.capitalize(), 
                                variable=self.log_category_var, value=category,
                                command=self.refresh_logs)
            rb.pack(anchor=tk.W, pady=1)
        
        # Niveles de log
        ttk.Separator(control_panel, orient='horizontal').pack(fill=tk.X, pady=10)
        ttk.Label(control_panel, text="📊 Nivel:", style='Status.TLabel').pack(anchor=tk.W, pady=(0, 5))
        
        self.log_level_var = tk.StringVar(value='todos')
        log_levels = ['todos', 'debug', 'info', 'warning', 'error']
        
        for level in log_levels:
            rb = ttk.Radiobutton(control_panel, text=level.upper(), 
                                variable=self.log_level_var, value=level,
                                command=self.refresh_logs)
            rb.pack(anchor=tk.W, pady=1)
        
        # Filtros avanzados
        ttk.Separator(control_panel, orient='horizontal').pack(fill=tk.X, pady=10)
        ttk.Label(control_panel, text="🔍 Filtros:", style='Status.TLabel').pack(anchor=tk.W, pady=(0, 5))
        
        # Filtro por texto
        ttk.Label(control_panel, text="Texto:", style='Small.TLabel').pack(anchor=tk.W)
        self.log_filter_var = tk.StringVar()
        self.log_filter_entry = ttk.Entry(control_panel, textvariable=self.log_filter_var, width=15)
        self.log_filter_entry.pack(fill=tk.X, pady=(0, 5))
        self.log_filter_entry.bind('<KeyRelease>', lambda e: self.root.after(500, self.refresh_logs))
        
        # Filtro por fecha
        ttk.Label(control_panel, text="Fecha:", style='Small.TLabel').pack(anchor=tk.W, pady=(5, 0))
        self.log_date_var = tk.StringVar(value='hoy')
        date_options = ['hoy', 'ayer', '7_dias', '30_dias', 'todos']
        date_combo = ttk.Combobox(control_panel, textvariable=self.log_date_var,
                                 values=date_options, state='readonly', width=12)
        date_combo.pack(fill=tk.X, pady=(0, 10))
        date_combo.bind('<<ComboboxSelected>>', lambda e: self.refresh_logs())
        
        # Opciones de visualización
        ttk.Separator(control_panel, orient='horizontal').pack(fill=tk.X, pady=10)
        ttk.Label(control_panel, text="👁️ Vista:", style='Status.TLabel').pack(anchor=tk.W, pady=(0, 5))
        
        self.log_show_timestamps_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_panel, text="Timestamps", 
                       variable=self.log_show_timestamps_var,
                       command=self.refresh_logs).pack(anchor=tk.W, pady=1)
        
        self.log_show_levels_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_panel, text="Niveles", 
                       variable=self.log_show_levels_var,
                       command=self.refresh_logs).pack(anchor=tk.W, pady=1)
        
        self.log_show_modules_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_panel, text="Módulos", 
                       variable=self.log_show_modules_var,
                       command=self.refresh_logs).pack(anchor=tk.W, pady=1)
        
        self.log_word_wrap_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_panel, text="Word Wrap", 
                       variable=self.log_word_wrap_var,
                       command=self.toggle_word_wrap).pack(anchor=tk.W, pady=1)
        
        # Controles de cantidad
        ttk.Separator(control_panel, orient='horizontal').pack(fill=tk.X, pady=10)
        ttk.Label(control_panel, text="📏 Cantidad:", style='Status.TLabel').pack(anchor=tk.W, pady=(0, 5))
        
        self.log_lines_var = tk.StringVar(value='100')
        lines_options = ['50', '100', '500', '1000', 'todas']
        lines_combo = ttk.Combobox(control_panel, textvariable=self.log_lines_var,
                                  values=lines_options, state='readonly', width=12)
        lines_combo.pack(fill=tk.X, pady=(0, 10))
        lines_combo.bind('<<ComboboxSelected>>', lambda e: self.refresh_logs())
        
        # Botones de acción
        ttk.Separator(control_panel, orient='horizontal').pack(fill=tk.X, pady=10)
        
        ttk.Button(control_panel, text="🔄 Actualizar", 
                  command=self.refresh_logs).pack(fill=tk.X, pady=2)
        
        ttk.Button(control_panel, text="🧹 Limpiar Vista", 
                  command=self.clear_log_display).pack(fill=tk.X, pady=2)
        
        ttk.Button(control_panel, text="💾 Exportar", 
                  command=self.export_logs).pack(fill=tk.X, pady=2)
        
        ttk.Button(control_panel, text="🗑️ Limpiar Logs", 
                  command=self.clear_log_files).pack(fill=tk.X, pady=2)
        
        ttk.Button(control_panel, text="📊 Estadísticas", 
                  command=self.show_log_statistics).pack(fill=tk.X, pady=2)
        
        # Área principal de logs con pestañas
        logs_content_frame = ttk.Frame(main_logs_frame)
        logs_content_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Notebook para diferentes vistas de logs
        self.logs_notebook = ttk.Notebook(logs_content_frame)
        self.logs_notebook.pack(fill=tk.BOTH, expand=True)
        
        # Tab 1: Vista principal
        main_log_frame = ttk.Frame(self.logs_notebook)
        self.logs_notebook.add(main_log_frame, text="📄 Principal")
        
        self.logs_text = tk.Text(main_log_frame, wrap=tk.WORD, font=('Consolas', 9),
                                state='disabled', cursor='arrow')
        logs_scroll = ttk.Scrollbar(main_log_frame, orient=tk.VERTICAL, command=self.logs_text.yview)
        logs_h_scroll = ttk.Scrollbar(main_log_frame, orient=tk.HORIZONTAL, command=self.logs_text.xview)
        self.logs_text.configure(yscrollcommand=logs_scroll.set, xscrollcommand=logs_h_scroll.set)
        
        self.logs_text.grid(row=0, column=0, sticky='nsew')
        logs_scroll.grid(row=0, column=1, sticky='ns')
        logs_h_scroll.grid(row=1, column=0, sticky='ew')
        
        main_log_frame.grid_rowconfigure(0, weight=1)
        main_log_frame.grid_columnconfigure(0, weight=1)
        
        # Configurar tags para colores de niveles
        self.setup_log_tags()
        
        # Tab 2: Vista en tiempo real
        realtime_frame = ttk.Frame(self.logs_notebook)
        self.logs_notebook.add(realtime_frame, text="🔴 Tiempo Real")
        
        self.realtime_logs_text = tk.Text(realtime_frame, wrap=tk.WORD, font=('Consolas', 9),
                                         state='disabled', cursor='arrow', bg='#001122', fg='#00ff00')
        realtime_scroll = ttk.Scrollbar(realtime_frame, orient=tk.VERTICAL, command=self.realtime_logs_text.yview)
        self.realtime_logs_text.configure(yscrollcommand=realtime_scroll.set)
        
        self.realtime_logs_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        realtime_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Tab 3: Búsqueda avanzada
        search_frame = ttk.Frame(self.logs_notebook)
        self.logs_notebook.add(search_frame, text="🔍 Búsqueda")
        
        # Controles de búsqueda
        search_controls = ttk.Frame(search_frame)
        search_controls.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(search_controls, text="Buscar:", style='Status.TLabel').pack(side=tk.LEFT)
        self.search_entry = ttk.Entry(search_controls, width=30)
        self.search_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        ttk.Button(search_controls, text="🔍 Buscar", 
                  command=self.search_logs).pack(side=tk.LEFT, padx=5)
        
        self.search_case_var = tk.BooleanVar()
        ttk.Checkbutton(search_controls, text="Case sensitive", 
                       variable=self.search_case_var).pack(side=tk.LEFT, padx=5)
        
        # Resultados de búsqueda
        self.search_results_text = tk.Text(search_frame, wrap=tk.WORD, font=('Consolas', 9),
                                          state='disabled', cursor='arrow')
        search_results_scroll = ttk.Scrollbar(search_frame, orient=tk.VERTICAL, 
                                            command=self.search_results_text.yview)
        self.search_results_text.configure(yscrollcommand=search_results_scroll.set)
        
        self.search_results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=(0, 10))
        search_results_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=(0, 10))
        
        # Status bar para logs
        logs_status_frame = ttk.Frame(logs_frame)
        logs_status_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.logs_status_label = ttk.Label(logs_status_frame, text="Logs cargados", style='Small.TLabel')
        self.logs_status_label.pack(side=tk.LEFT)
        
        self.logs_count_label = ttk.Label(logs_status_frame, text="", style='Small.TLabel')
        self.logs_count_label.pack(side=tk.RIGHT)
        
        # Cargar logs iniciales
        self.refresh_logs()
    
    def setup_log_tags(self):
        """Configurar tags para colores de niveles de log"""
        self.logs_text.tag_configure('DEBUG', foreground='#808080')
        self.logs_text.tag_configure('INFO', foreground='#0000FF')
        self.logs_text.tag_configure('WARNING', foreground='#FF8C00')
        self.logs_text.tag_configure('ERROR', foreground='#FF0000', font=('Consolas', 9, 'bold'))
        self.logs_text.tag_configure('CRITICAL', foreground='#8B0000', font=('Consolas', 9, 'bold'))
        
        # Tags para búsqueda
        self.logs_text.tag_configure('SEARCH_HIGHLIGHT', background='#FFFF00')
        self.search_results_text.tag_configure('SEARCH_HIGHLIGHT', background='#FFFF00')
    
    def toggle_realtime_logs(self):
        """Activar/desactivar logs en tiempo real"""
        if self.logs_realtime_var.get():
            self.start_realtime_logs()
            self.logs_notebook.select(1)  # Cambiar a tab de tiempo real
        else:
            self.stop_realtime_logs()
    
    def start_realtime_logs(self):
        """Iniciar monitoreo de logs en tiempo real"""
        self.realtime_logs_active = True
        self.update_status("Logs en tiempo real activados", "info")
        
        def realtime_worker():
            try:
                from core.logger import gestor_logger
                
                # Obtener archivos de log actuales
                log_files = gestor_logger.get_log_files()
                file_positions = {}
                
                # Inicializar posiciones
                for category, file_path in log_files.items():
                    try:
                        file_positions[file_path] = Path(file_path).stat().st_size if Path(file_path).exists() else 0
                    except:
                        file_positions[file_path] = 0
                
                while self.realtime_logs_active and not self.shutdown_threads:
                    try:
                        new_lines = []
                        
                        for category, file_path in log_files.items():
                            if not Path(file_path).exists():
                                continue
                            
                            current_size = Path(file_path).stat().st_size
                            last_position = file_positions.get(file_path, 0)
                            
                            if current_size > last_position:
                                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                    f.seek(last_position)
                                    new_content = f.read()
                                    new_lines.extend([(category, line) for line in new_content.split('\n') if line.strip()])
                                
                                file_positions[file_path] = current_size
                        
                        if new_lines:
                            self.root.after(0, lambda: self.append_realtime_logs(new_lines))
                        
                        time.sleep(1)  # Check cada segundo
                        
                    except Exception as e:
                        self.logger.error(f"Error en realtime logs: {e}")
                        time.sleep(5)
                        
            except Exception as e:
                self.logger.error(f"Error fatal en realtime worker: {e}")
        
        self.realtime_thread = threading.Thread(target=realtime_worker, daemon=True)
        self.realtime_thread.start()
    
    def stop_realtime_logs(self):
        """Detener monitoreo de logs en tiempo real"""
        self.realtime_logs_active = False
        self.update_status("Logs en tiempo real desactivados", "info")
    
    def append_realtime_logs(self, new_lines):
        """Agregar nuevas líneas a logs en tiempo real"""
        try:
            self.realtime_logs_text.config(state='normal')
            
            for category, line in new_lines:
                timestamp = datetime.now().strftime("%H:%M:%S")
                formatted_line = f"[{timestamp}] [{category.upper()}] {line}\n"
                
                self.realtime_logs_text.insert(tk.END, formatted_line)
                
                # Mantener solo últimas 1000 líneas
                line_count = int(self.realtime_logs_text.index('end-1c').split('.')[0])
                if line_count > 1000:
                    self.realtime_logs_text.delete(1.0, f"{line_count-1000}.0")
            
            # Auto-scroll al final
            self.realtime_logs_text.see(tk.END)
            self.realtime_logs_text.config(state='disabled')
            
        except Exception as e:
            self.logger.error(f"Error agregando logs en tiempo real: {e}")
    
    def toggle_word_wrap(self):
        """Activar/desactivar word wrap"""
        wrap_mode = tk.WORD if self.log_word_wrap_var.get() else tk.NONE
        self.logs_text.config(wrap=wrap_mode)
        self.search_results_text.config(wrap=wrap_mode)
    
    def refresh_logs(self):
        """Actualizar vista de logs con filtros aplicados"""
        try:
            self.logs_text.config(state='normal')
            self.logs_text.delete(1.0, tk.END)
            
            # Mostrar mensaje de carga
            self.logs_text.insert(1.0, "🔄 Cargando logs...\n")
            self.logs_text.config(state='disabled')
            self.logs_text.update()
            
            def load_logs_worker():
                try:
                    from core.logger import gestor_logger
                    
                    category = self.log_category_var.get()
                    level_filter = self.log_level_var.get()
                    text_filter = self.log_filter_var.get().strip()
                    date_filter = self.log_date_var.get()
                    max_lines = self.log_lines_var.get()
                    
                    log_files = gestor_logger.get_log_files()
                    all_lines = []
                    
                    # Determinar archivos a leer
                    if category == 'todos':
                        files_to_read = log_files.values()
                    elif category in log_files:
                        files_to_read = [log_files[category]]
                    else:
                        files_to_read = []
                    
                    # Leer archivos
                    for file_path in files_to_read:
                        if not Path(file_path).exists():
                            continue
                        
                        try:
                            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                lines = f.readlines()
                                
                            # Aplicar filtros
                            filtered_lines = self.apply_log_filters(lines, level_filter, text_filter, date_filter)
                            all_lines.extend(filtered_lines)
                            
                        except Exception as e:
                            all_lines.append(f"❌ Error leyendo {file_path}: {e}\n")
                    
                    # Limitar número de líneas
                    if max_lines != 'todas':
                        max_count = int(max_lines)
                        if len(all_lines) > max_count:
                            all_lines = all_lines[-max_count:]
                    
                    # Formatear y mostrar
                    formatted_content = self.format_log_lines(all_lines)
                    
                    self.root.after(0, lambda: self.display_log_content(formatted_content, len(all_lines)))
                    
                except Exception as e:
                    error_msg = f"❌ Error cargando logs: {e}\n"
                    self.root.after(0, lambda: self.display_log_content(error_msg, 0))
            
            threading.Thread(target=load_logs_worker, daemon=True).start()
            
        except Exception as e:
            self.logs_text.config(state='normal')
            self.logs_text.delete(1.0, tk.END)
            self.logs_text.insert(1.0, f"❌ Error: {e}\n")
            self.logs_text.config(state='disabled')
    
    def apply_log_filters(self, lines, level_filter, text_filter, date_filter):
        """Aplicar filtros a las líneas de log"""
        from datetime import datetime, timedelta
        
        filtered_lines = []
        today = datetime.now().date()
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Filtro por nivel
            if level_filter != 'todos':
                if level_filter.upper() not in line:
                    continue
            
            # Filtro por texto
            if text_filter:
                if text_filter.lower() not in line.lower():
                    continue
            
            # Filtro por fecha
            if date_filter != 'todos':
                try:
                    # Extraer fecha de la línea (formato: YYYY-MM-DD)
                    date_part = line.split(' - ')[0]
                    log_date = datetime.strptime(date_part, '%Y-%m-%d %H:%M:%S').date()
                    
                    if date_filter == 'hoy' and log_date != today:
                        continue
                    elif date_filter == 'ayer' and log_date != today - timedelta(days=1):
                        continue
                    elif date_filter == '7_dias' and (today - log_date).days > 7:
                        continue
                    elif date_filter == '30_dias' and (today - log_date).days > 30:
                        continue
                        
                except:
                    # Si no puede parsear la fecha, incluir la línea
                    pass
            
            filtered_lines.append(line)
        
        return filtered_lines
    
    def format_log_lines(self, lines):
        """Formatear líneas de log para mostrar"""
        formatted_lines = []
        
        for line in lines:
            if not line.strip():
                continue
            
            # Aplicar opciones de visualización
            formatted_line = line
            
            if not self.log_show_timestamps_var.get():
                # Remover timestamp (primera parte antes del primer -)
                parts = line.split(' - ', 1)
                if len(parts) > 1:
                    formatted_line = parts[1]
            
            if not self.log_show_levels_var.get():
                # Remover nivel de log
                for level in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']:
                    formatted_line = formatted_line.replace(f' - {level} - ', ' - ')
            
            if not self.log_show_modules_var.get():
                # Remover módulo (parte después del nivel)
                parts = formatted_line.split(' - ')
                if len(parts) >= 3:
                    formatted_line = f"{parts[0]} - {' - '.join(parts[2:])}"
            
            formatted_lines.append(formatted_line)
        
        return '\n'.join(formatted_lines)
    
    def display_log_content(self, content, line_count):
        """Mostrar contenido de logs en la interfaz"""
        try:
            self.logs_text.config(state='normal')
            self.logs_text.delete(1.0, tk.END)
            
            if content.strip():
                self.logs_text.insert(1.0, content)
                
                # Aplicar colores por nivel
                self.apply_log_colors()
                
                # Auto-scroll al final
                self.logs_text.see(tk.END)
            else:
                self.logs_text.insert(1.0, "ℹ️ No hay logs que coincidan con los filtros aplicados\n")
            
            self.logs_text.config(state='disabled')
            
            # Actualizar status
            self.logs_count_label.config(text=f"{line_count} líneas")
            
        except Exception as e:
            self.logger.error(f"Error mostrando logs: {e}")
    
    def apply_log_colors(self):
        """Aplicar colores a diferentes niveles de log"""
        try:
            content = self.logs_text.get(1.0, tk.END)
            lines = content.split('\n')
            
            for i, line in enumerate(lines):
                line_start = f"{i+1}.0"
                line_end = f"{i+1}.end"
                
                if 'DEBUG' in line:
                    self.logs_text.tag_add('DEBUG', line_start, line_end)
                elif 'INFO' in line:
                    self.logs_text.tag_add('INFO', line_start, line_end)
                elif 'WARNING' in line:
                    self.logs_text.tag_add('WARNING', line_start, line_end)
                elif 'ERROR' in line:
                    self.logs_text.tag_add('ERROR', line_start, line_end)
                elif 'CRITICAL' in line:
                    self.logs_text.tag_add('CRITICAL', line_start, line_end)
                    
        except Exception as e:
            self.logger.debug(f"Error aplicando colores: {e}")

# FIN PIEZA 4
