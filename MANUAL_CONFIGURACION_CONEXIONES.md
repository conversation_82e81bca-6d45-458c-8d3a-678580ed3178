# Manual de Configuración de Conexiones
## Gestor de Archivos Corporativo

**Versión:** 2.0  
**Fecha:** Junio 2025  
**Aplicaciones:** Validación de Duplicados & Limpieza por Lista  

---

## 🎯 Introducción

Este manual explica cómo configurar las conexiones a dispositivos NAS (QNAP) y PCs de workstation en el Gestor de Archivos Corporativo. El sistema utiliza un **método híbrido** con failover automático entre conexiones simbólicas y rutas de red.

### Aplicaciones Incluidas
- **IGSON_Validacion_Duplicados.exe** - Valida archivos duplicados entre origen y QNAPs
- **LimpiezaPorLista.exe** - Elimina archivos específicos de múltiples ubicaciones

---

## 📁 Estructura de Configuración

### Archivos Principales
```
├── config.json              # Configuración principal
├── encryption.key           # Clave de encriptación (auto-generada)
├── gestor_validacion_license.lic  # Licencia Validación
├── gestor_limpieza_license.lic    # Licencia Limpieza
└── logs/                    # Logs por operación y fecha
    ├── validacion_YYYYMMDD.log
    └── limpieza_YYYYMMDD.log
```

### Tipos de Conexión Soportados
- **Symlinks (Enlaces Simbólicos)**: Rutas locales mapeadas (ej: `C:\Links\qnap1`)
- **Network Paths (Rutas de Red)**: Rutas UNC directas (ej: `\\qnap1\share`)
- **Híbrido**: Fallback automático entre ambos métodos

---

## ⚙️ Configuración Básica

### Estructura del config.json

```json
{
  "app_info": {
    "name": "Gestor de Archivos Corporativo",
    "version": "1.0",
    "dev_mode": false
  },
  "connection_settings": {
    "connection_type": "hybrid",
    "timeout_seconds": 30,
    "retry_attempts": 3
  },
  "locations": {
    "qnap1": {
      "name": "QNAP Storage 1",
      "network_path": "\\\\qnap1\\share",
      "symlink_path": "C:\\Links\\qnap1",
      "enabled": true,
      "connection_priority": ["symlink", "network"],
      "credentials": {
        "username": "admin",
        "password_encrypted": "gAAAAABh...",
        "domain": "WORKGROUP"
      }
    }
  }
}
```

### Parámetros de Conexión

| Parámetro | Descripción | Valores |
|-----------|-------------|---------|
| `connection_type` | Método de conexión | `"symlink"`, `"network"`, `"hybrid"` |
| `timeout_seconds` | Timeout por conexión | `15-60` segundos |
| `retry_attempts` | Intentos de reconexión | `1-5` intentos |
| `connection_priority` | Orden de fallback | `["symlink", "network"]` o `["network", "symlink"]` |

---

## 🖥️ Configuración de Dispositivos QNAP

### Configuración Estándar

```json
"qnap1": {
  "name": "QNAP Storage Principal",
  "network_path": "\\\\*************\\share",
  "symlink_path": "Q:\\",
  "enabled": true,
  "connection_priority": ["symlink", "network"],
  "credentials": {
    "username": "admin",
    "password_encrypted": "",
    "domain": "WORKGROUP"
  }
}
```

### Tipos de Configuración QNAP

#### 1. **Por IP Fija**
```json
"network_path": "\\\\*************\\PublicShare"
```

#### 2. **Por Nombre de Host**
```json
"network_path": "\\\\qnap-storage01\\datos"
```

#### 3. **Con Dominio Corporativo**
```json
"credentials": {
  "username": "administrador",
  "password_encrypted": "encrypted_password",
  "domain": "EMPRESA.LOCAL"
}
```

#### 4. **Acceso Público/Guest**
```json
"credentials": {
  "username": "",
  "password_encrypted": "",
  "domain": "WORKGROUP"
}
```

### Configuración de Enlaces Simbólicos

#### Crear Symlinks para QNAP

**Método 1: Script PowerShell (Recomendado)**
```powershell
# Ejecutar como Administrador
New-Item -ItemType SymbolicLink -Path "C:\Links\qnap1" -Target "\\*************\share"
New-Item -ItemType SymbolicLink -Path "C:\Links\qnap2" -Target "\\*************\backup"
```

**Método 2: CMD (Alternativo)**
```cmd
mklink /D "C:\Links\qnap1" "\\*************\share"
mklink /D "C:\Links\qnap2" "\\*************\backup"
```

**Método 3: Mapeo de Unidades de Red**
```json
"symlink_path": "Q:\\"  // Unidad mapeada manualmente
```

---

## 💻 Configuración de PCs de Workstation

### Configuración Estándar

```json
"pc1": {
  "name": "PC Workstation 1",
  "network_path": "\\\\pc-01\\shared",
  "symlink_path": "C:\\Links\\pc1",
  "enabled": true,
  "connection_priority": ["network", "symlink"],
  "credentials": {
    "username": "administrador",
    "password_encrypted": "encrypted_password",
    "domain": "EMPRESA.LOCAL"
  }
}
```

### Configuraciones por Escenario

#### 1. **PC con Dominio Corporativo**
```json
"pc1": {
  "network_path": "\\\\pc-workstation-01\\shared",
  "credentials": {
    "username": "admin.sistemas",
    "password_encrypted": "gAAAAABh_encrypted_password",
    "domain": "EMPRESA.LOCAL"
  }
}
```

#### 2. **PC con Workgroup Local**
```json
"pc2": {
  "network_path": "\\\\192.168.1.50\\shared",
  "credentials": {
    "username": "administrador",
    "password_encrypted": "gAAAAABh_encrypted_password",
    "domain": "WORKGROUP"
  }
}
```

#### 3. **PC con Usuario Local**
```json
"pc3": {
  "network_path": "\\\\PC-PRODUCCION\\work",
  "credentials": {
    "username": "PC-PRODUCCION\\operador",
    "password_encrypted": "gAAAAABh_encrypted_password",
    "domain": ""
  }
}
```

### Configuración de Comparticiones SMB

#### En el PC Destino (Windows)

**1. Habilitar Compartir Archivos**
```cmd
# Como Administrador
netsh advfirewall firewall set rule group="File and Printer Sharing" new enable=Yes
net share shared=C:\SharedFolder /grant:Everyone,FULL
```

**2. Configurar Permisos**
```cmd
# Permisos de red
net share shared /delete
net share shared=C:\SharedFolder /grant:"Domain\usuario",FULL
```

**3. Configurar Usuarios SMB**
```cmd
# Crear usuario local
net user operador Password123 /add
net localgroup "Usuarios de escritorio remoto" operador /add
```

---

## 🔄 Sistema de Failover y Detección

### Método Híbrido de Conexión

El sistema prueba automáticamente ambos métodos según la prioridad configurada:

```python
# Ejemplo del flujo de conexión
1. Intenta método primario (symlink o network)
   ↓ Si falla ↓
2. Intenta método secundario (network o symlink)  
   ↓ Si falla ↓
3. Marca ubicación como "Sin conexión"
```

### Prioridades Recomendadas

#### Para QNAP (NAS)
```json
"connection_priority": ["symlink", "network"]
```
**Razón**: Los symlinks son más rápidos para NAS con muchos archivos

#### Para PCs
```json
"connection_priority": ["network", "symlink"]
```
**Razón**: Las rutas UNC directas son más confiables para PCs

### Configuración de Timeouts

```json
"connection_settings": {
  "timeout_seconds": 30,        // Timeout por intento
  "retry_attempts": 3,          // Reintentos automáticos
  "connection_check_interval": 60  // Seconds entre checks
}
```

---

## 🔍 Detección Automática de QNAPs

### Funcionamiento del Auto-Discovery

El sistema puede detectar QNAPs automáticamente en la red local:

```json
"auto_discovery": {
  "enabled": true,
  "scan_range": "***********-254",
  "qnap_ports": [80, 443, 8080],
  "timeout_per_host": 2
}
```

### Protocolos de Detección

#### 1. **Detección por Puerto HTTP**
```python
# Busca servicios QNAP en puertos estándar
ports = [80, 443, 8080, 5000]
```

#### 2. **Detección por Banner SMB**
```python
# Identifica dispositivos con comparticiones QNAP
smb_scan_for_qnap_shares()
```

#### 3. **Detección por MDNS/Bonjour**
```python
# Busca servicios _qnap._tcp en la red local
```

### Configuración Manual de QNAPs No Detectados

Si la detección automática falla:

```json
"manual_qnaps": [
  {
    "ip": "*************",
    "name": "QNAP-NAS-01",
    "shares": ["Public", "homes", "backup"],
    "web_port": 8080
  }
]
```

---

## 🔐 Gestión de Credenciales

### Encriptación de Contraseñas

Las contraseñas se almacenan encriptadas usando Fernet (AES 128):

```python
# La aplicación maneja automáticamente:
"password_encrypted": "gAAAAABh_abc123_encrypted_password"
```

### Configuración de Credenciales por Interfaz

**1. Abrir la aplicación**
**2. Ir a la pestaña "Configuración"**
**3. Seleccionar la ubicación**
**4. Introducir credenciales:**
   - Usuario
   - Contraseña (se encripta automáticamente)
   - Dominio (opcional)

### Credenciales por Tipo de Dispositivo

#### QNAP NAS
```json
"credentials": {
  "username": "admin",           // Usuario administrador
  "password_encrypted": "...",   // Contraseña encriptada
  "domain": "WORKGROUP"          // Normalmente WORKGROUP
}
```

#### PC con Dominio
```json
"credentials": {
  "username": "DOMINIO\\usuario", // Formato completo
  "password_encrypted": "...",
  "domain": "EMPRESA.LOCAL"       // Dominio AD
}
```

#### PC Local
```json
"credentials": {
  "username": "administrador",   // Usuario local
  "password_encrypted": "...",
  "domain": ""                   // Vacío para local
}
```

---

## 🛠️ Configuración de Operaciones

### Validación de Duplicados

```json
"operations": {
  "validacion_duplicados": {
    "carpeta_origen": "C:\\Temp\\archivos_origen",
    "carpeta_rechazados": "C:\\Temp\\rechazados",
    "qnaps_a_revisar": ["qnap1", "qnap2"],
    "enabled": true
  }
}
```

### Limpieza por Lista

```json
"operations": {
  "limpieza_lista": {
    "ubicaciones_limpieza": ["qnap1", "qnap2", "pc1", "pc2", "pc3", "pc4"],
    "carpetas_objetivo": {
      "qnap1": "\\datos\\produccion",
      "qnap2": "\\backup\\files",
      "pc1": "\\shared\\work",
      "pc2": "\\shared\\work",
      "pc3": "\\shared\\work",
      "pc4": "\\shared\\work"
    },
    "enabled": true
  }
}
```

---

## 🧪 Testing y Verificación

### Test de Conectividad Individual

En la interfaz de la aplicación:

1. **Dashboard de Conectividad**
   - ✅ Verde: Conectado correctamente
   - ❌ Rojo: Error de conexión
   - ⚪ Gris: Deshabilitado
   - ❓ Amarillo: Sin configurar

2. **Test Manual por Ubicación**
   - Click en "Test" junto a cada ubicación
   - Verifica ambos métodos (symlink + network)

### Tests desde Línea de Comandos

#### Test SMB Manual
```cmd
# Test de conectividad básica
net use \\*************\share /user:admin password

# Verificar comparticiones disponibles
net view \\*************
```

#### Test de Symlinks
```cmd
# Verificar si el symlink funciona
dir C:\Links\qnap1

# Test de escritura
echo test > C:\Links\qnap1\test.txt
```

### Logs de Diagnóstico

```
logs/
├── conectividad_YYYYMMDD.log    # Tests de conexión
├── validacion_YYYYMMDD.log      # Operación validación
├── limpieza_YYYYMMDD.log        # Operación limpieza
└── sistema_YYYYMMDD.log         # Errores generales
```

---

## 🚨 Troubleshooting

### Problemas Comunes

#### 1. **Error "Acceso Denegado"**
```
Causa: Credenciales incorrectas o permisos insuficientes
Solución:
- Verificar usuario/contraseña en la configuración
- Comprobar permisos de la carpeta compartida
- Probar acceso manual: \\servidor\share
```

#### 2. **"No se puede encontrar la ruta de red"**
```
Causa: Problema de conectividad de red o nombre incorrecto
Solución:
- Ping al servidor: ping *************
- Verificar puertos SMB: telnet ************* 445
- Usar IP en lugar de nombre si hay problemas DNS
```

#### 3. **Symlinks No Funcionan**
```
Causa: Permisos insuficientes o target incorrecto
Solución:
- Ejecutar como Administrador
- Verificar que el target existe
- Recrear el symlink con ruta correcta
```

#### 4. **QNAP No Detectado Automáticamente**
```
Causa: Configuración de red o firewall
Solución:
- Verificar que el QNAP está en la misma red
- Comprobar puertos abiertos (80, 443, 5000)
- Configurar manualmente la IP del QNAP
```

### Comandos de Diagnóstico

```cmd
# Verificar conectividad de red
ping qnap1
telnet qnap1 445

# Listar recursos compartidos
net view \\qnap1

# Test de autenticación
net use \\qnap1\share /user:admin

# Verificar symlinks activos
dir C:\Links
```

---

## 📝 Ejemplos de Configuración Completa

### Escenario 1: Empresa Pequeña (2 QNAP + 2 PCs)

```json
{
  "locations": {
    "qnap1": {
      "name": "QNAP Principal",
      "network_path": "\\\\*************\\datos",
      "symlink_path": "Q:\\",
      "enabled": true,
      "connection_priority": ["symlink", "network"],
      "credentials": {
        "username": "admin",
        "password_encrypted": "gAAAAABh_qnap_password",
        "domain": "WORKGROUP"
      }
    },
    "qnap2": {
      "name": "QNAP Backup",
      "network_path": "\\\\*************\\backup",
      "symlink_path": "R:\\",
      "enabled": true,
      "connection_priority": ["symlink", "network"],
      "credentials": {
        "username": "backup",
        "password_encrypted": "gAAAAABh_backup_password",
        "domain": "WORKGROUP"
      }
    },
    "pc1": {
      "name": "PC Producción",
      "network_path": "\\\\PC-PROD\\shared",
      "symlink_path": "C:\\Links\\pc1",
      "enabled": true,
      "connection_priority": ["network", "symlink"],
      "credentials": {
        "username": "operador",
        "password_encrypted": "gAAAAABh_pc_password",
        "domain": ""
      }
    },
    "pc2": {
      "name": "PC Administración",
      "network_path": "\\\\PC-ADMIN\\work",
      "symlink_path": "C:\\Links\\pc2",
      "enabled": true,
      "connection_priority": ["network", "symlink"],
      "credentials": {
        "username": "administrador",
        "password_encrypted": "gAAAAABh_admin_password",
        "domain": ""
      }
    }
  }
}
```

### Escenario 2: Empresa Mediana con Dominio AD

```json
{
  "locations": {
    "qnap1": {
      "name": "QNAP Departamental",
      "network_path": "\\\\qnap-dept.empresa.local\\shared",
      "symlink_path": "D:\\NAS\\Departamental",
      "enabled": true,
      "connection_priority": ["symlink", "network"],
      "credentials": {
        "username": "svc-gestor",
        "password_encrypted": "gAAAAABh_service_account",
        "domain": "EMPRESA.LOCAL"
      }
    },
    "pc1": {
      "name": "WS-CONTABILIDAD-01",
      "network_path": "\\\\ws-cont-01.empresa.local\\docs",
      "symlink_path": "C:\\Links\\contabilidad",
      "enabled": true,
      "connection_priority": ["network", "symlink"],
      "credentials": {
        "username": "EMPRESA\\svc-archivo",
        "password_encrypted": "gAAAAABh_domain_service",
        "domain": "EMPRESA.LOCAL"
      }
    }
  }
}
```

### Escenario 3: Configuración Mixta (Local + Cloud)

```json
{
  "locations": {
    "qnap_local": {
      "name": "QNAP Local Office",
      "network_path": "\\\\10.0.1.100\\production",
      "symlink_path": "P:\\",
      "enabled": true,
      "connection_priority": ["symlink", "network"],
      "credentials": {
        "username": "admin",
        "password_encrypted": "gAAAAABh_local_nas",
        "domain": "WORKGROUP"
      }
    },
    "qnap_remote": {
      "name": "QNAP Sucursal",
      "network_path": "\\\\vpn-nas.empresa.com\\backup",
      "symlink_path": "C:\\RemoteNAS",
      "enabled": true,
      "connection_priority": ["network", "symlink"],
      "credentials": {
        "username": "sync-user",
        "password_encrypted": "gAAAAABh_remote_nas",
        "domain": "WORKGROUP"
      }
    }
  }
}
```

---

## 🔧 Configuración Avanzada

### Optimización de Rendimiento

```json
"performance_settings": {
  "max_concurrent_connections": 4,
  "connection_pool_size": 10,
  "cache_credentials": true,
  "cache_duration_minutes": 30
}
```

### Configuración de Red

```json
"network_settings": {
  "smb_version": "3.0",
  "encryption_required": false,
  "buffer_size_kb": 64,
  "tcp_keepalive": true
}
```

### Monitoreo y Alertas

```json
"monitoring": {
  "health_check_interval": 300,
  "alert_on_connection_loss": true,
  "retry_failed_connections": true,
  "max_connection_failures": 5
}
```

---

## 📞 Soporte Técnico

### Información de Contacto
- **Desarrollador**: IGSON
- **Versión**: 2.0
- **Fecha**: Junio 2025

### Logs para Soporte

Incluir en consultas de soporte:
```
logs/sistema_YYYYMMDD.log
logs/conectividad_YYYYMMDD.log
config.json (sin contraseñas)
```

### Comandos de Diagnóstico Rápido

```cmd
# Estado general del sistema
systeminfo | findstr /B /C:"OS"
ipconfig /all

# Test de red corporativa
nslookup qnap1
ping -t *************

# Verificar servicios SMB
sc query lanmanserver
sc query lanmanworkstation
```

---

*Manual de Configuración v2.0 - Gestor de Archivos Corporativo*  
*© 2025 IGSON - Todos los derechos reservados*