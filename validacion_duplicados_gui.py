"""
GUI Independiente - Validación de Duplicados
===========================================

Interfaz gráfica Tkinter con sistema de licencias integrado
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
from datetime import datetime
import threading
import time
from pathlib import Path

# Agregar el directorio actual al path para imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Imports del sistema
try:
    from gestor_license_manager import GestorLicenseManager, show_license_dialog
    from src.core.config_manager import ConfigManager
    from src.core.logger import LoggerManager
    from src.core.utils import create_test_environment
except ImportError as e:
    print(f"Warning: Some modules not found: {e}")
    print("Running in standalone mode...")

class ValidationDuplicatesGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Gestor de Archivos - Validación de Duplicados")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # Configuración inicial
        self.licensed = False
        self.running = False
        
        # Variables de configuración
        self.carpeta_origen = tk.StringVar()
        self.carpeta_rechazados = tk.StringVar()
        self.dry_run = tk.BooleanVar(value=True)
        
        # Configurar logging
        try:
            self.logger = LoggerManager("validacion_gui").get_logger()
        except:
            import logging
            logging.basicConfig(level=logging.INFO)
            self.logger = logging.getLogger("validacion_gui")
        
        self.setup_ui()
        self.check_license()
        self.load_default_config()
    
    def check_license(self):
        """Verificar licencia al iniciar"""
        try:
            license_manager = GestorLicenseManager("validacion")
            valid, message = license_manager.validate_license()
            
            if valid:
                self.licensed = True
                self.license_status.config(text=f"✅ Licenciado: {message}", foreground="green")
                self.logger.info(f"License valid: {message}")
            else:
                self.licensed = False
                self.license_status.config(text=f"❌ Sin licencia: {message}", foreground="red")
                self.logger.warning(f"License invalid: {message}")
                
                # Ofrecer activación
                response = messagebox.askyesno("Licencia", 
                    f"Licencia no válida: {message}\n\n¿Desea activar licencia?")
                if response:
                    activated = show_license_dialog(self.root, "validacion")
                    if activated:
                        self.check_license()  # Re-verificar
        except Exception as e:
            self.logger.error(f"License check error: {e}")
            self.licensed = False
            self.license_status.config(text="❌ Error verificando licencia", foreground="red")
    
    def setup_ui(self):
        """Configurar interfaz de usuario"""
        # Frame principal con logo
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill='both', expand=True)
        
        # Header con logo IGSON
        self.setup_header(main_frame)
        
        # Frame de configuración
        config_frame = ttk.LabelFrame(main_frame, text="Configuración", padding="10")
        config_frame.pack(fill='x', pady=(10,0))
        
        # Carpeta origen
        ttk.Label(config_frame, text="Carpeta Origen:").grid(row=0, column=0, sticky='w', pady=5)
        ttk.Entry(config_frame, textvariable=self.carpeta_origen, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(config_frame, text="Examinar", 
                  command=self.select_carpeta_origen).grid(row=0, column=2, pady=5)
        
        # Carpeta rechazados
        ttk.Label(config_frame, text="Carpeta Rechazados:").grid(row=1, column=0, sticky='w', pady=5)
        ttk.Entry(config_frame, textvariable=self.carpeta_rechazados, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(config_frame, text="Examinar", 
                  command=self.select_carpeta_rechazados).grid(row=1, column=2, pady=5)
        
        # Modo dry run
        ttk.Checkbutton(config_frame, text="Modo Simulación (Dry Run)", 
                       variable=self.dry_run).grid(row=2, column=0, columnspan=2, sticky='w', pady=10)
        
        # Frame de control
        control_frame = ttk.LabelFrame(main_frame, text="Control", padding="10")
        control_frame.pack(fill='x', pady=(10,0))
        
        # Botones de control
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill='x')
        
        self.execute_btn = ttk.Button(button_frame, text="🔍 Ejecutar Validación", 
                                     command=self.execute_validation, state='normal')
        self.execute_btn.pack(side='left', padx=(0,10))
        
        self.stop_btn = ttk.Button(button_frame, text="⏹ Detener", 
                                  command=self.stop_validation, state='disabled')
        self.stop_btn.pack(side='left', padx=(0,10))
        
        # Barra de progreso
        self.progress = ttk.Progressbar(control_frame, mode='indeterminate')
        self.progress.pack(fill='x', pady=(10,0))
        
        # Frame de resultados
        results_frame = ttk.LabelFrame(main_frame, text="Resultados", padding="10")
        results_frame.pack(fill='both', expand=True, pady=(10,0))
        
        # Área de texto para logs
        self.log_text = scrolledtext.ScrolledText(results_frame, height=15, width=80)
        self.log_text.pack(fill='both', expand=True)
        
        # Frame de estadísticas
        stats_frame = ttk.Frame(results_frame)
        stats_frame.pack(fill='x', pady=(10,0))
        
        self.stats_label = ttk.Label(stats_frame, text="Listo para ejecutar", font=("Arial", 10))
        self.stats_label.pack(side='left')
        
        # Estado de licencia
        self.license_status = ttk.Label(stats_frame, text="Verificando licencia...", font=("Arial", 9))
        self.license_status.pack(side='right')
    
    def setup_header(self, parent):
        """Configurar header con logo IGSON"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill='x', pady=(0,10))
        
        # Intentar cargar logo IGSON
        logo_loaded = False
        try:
            from PIL import Image, ImageTk
            logo_path = os.path.join(current_dir, "logo_igson.png")
            print(f"Buscando logo en: {logo_path}")
            print(f"Logo existe: {os.path.exists(logo_path)}")
            
            if os.path.exists(logo_path):
                img = Image.open(logo_path)
                img = img.resize((80, 80), Image.Resampling.LANCZOS)
                self.logo_photo = ImageTk.PhotoImage(img)
                
                logo_label = ttk.Label(header_frame, image=self.logo_photo)
                logo_label.pack(side='left', padx=(0,15))
                logo_loaded = True
                print("✅ Logo IGSON cargado exitosamente")
            else:
                print("❌ Logo no encontrado en la ruta")
        except ImportError as e:
            print(f"❌ PIL no disponible: {e}")
        except Exception as e:
            print(f"❌ Error cargando logo: {e}")
        
        if not logo_loaded:
            # Logo de texto si no se pudo cargar
            ttk.Label(header_frame, text="🏢", font=("Arial", 40)).pack(side='left', padx=(0,15))
        
        # Información de la aplicación
        info_frame = ttk.Frame(header_frame)
        info_frame.pack(side='left', fill='both', expand=True)
        
        ttk.Label(info_frame, text="IGSON - Gestor de Archivos Corporativo", 
                 font=("Arial", 16, "bold")).pack(anchor='w')
        ttk.Label(info_frame, text="Validación de Duplicados", 
                 font=("Arial", 12)).pack(anchor='w')
        ttk.Label(info_frame, text="Versión 1.0 - Junio 2025", 
                 font=("Arial", 9), foreground="gray").pack(anchor='w')
    
    def select_carpeta_origen(self):
        """Seleccionar carpeta origen"""
        folder = filedialog.askdirectory(title="Seleccionar Carpeta Origen")
        if folder:
            self.carpeta_origen.set(folder)
    
    def select_carpeta_rechazados(self):
        """Seleccionar carpeta rechazados"""
        folder = filedialog.askdirectory(title="Seleccionar Carpeta Rechazados")
        if folder:
            self.carpeta_rechazados.set(folder)
    
    def load_default_config(self):
        """Cargar configuración por defecto para testing"""
        # Crear entorno de prueba si no existe
        base_dir = os.path.join(current_dir, "test_validacion")
        
        if not os.path.exists(base_dir):
            try:
                os.makedirs(base_dir, exist_ok=True)
                
                # Crear carpetas de prueba
                origen_dir = os.path.join(base_dir, "origen")
                rechazados_dir = os.path.join(base_dir, "rechazados")
                qnap1_dir = os.path.join(base_dir, "qnap1")
                qnap2_dir = os.path.join(base_dir, "qnap2")
                
                for directory in [origen_dir, rechazados_dir, qnap1_dir, qnap2_dir]:
                    os.makedirs(directory, exist_ok=True)
                
                # Crear archivos de prueba
                test_files = ["documento1.pdf", "imagen1.jpg", "texto1.txt", "video1.mp4", "archivo1.docx"]
                
                # En origen
                for file in test_files:
                    with open(os.path.join(origen_dir, file), 'w') as f:
                        f.write(f"Contenido de {file}")
                
                # Duplicados en qnaps (simular duplicados)
                for file in test_files[:3]:  # Primeros 3 son duplicados
                    with open(os.path.join(qnap1_dir, file), 'w') as f:
                        f.write(f"Contenido de {file}")
                    if file == "imagen1.jpg":  # También en qnap2
                        with open(os.path.join(qnap2_dir, file), 'w') as f:
                            f.write(f"Contenido de {file}")
                
                self.log_message("✅ Entorno de prueba creado")
                
            except Exception as e:
                self.log_message(f"❌ Error creando entorno: {e}")
        
        # Configurar rutas por defecto
        self.carpeta_origen.set(os.path.join(base_dir, "origen"))
        self.carpeta_rechazados.set(os.path.join(base_dir, "rechazados"))
    
    def update_license(self):
        """Actualizar licencia"""
        activated = show_license_dialog(self.root, "validacion")
        if activated:
            self.check_license()
    
    def log_message(self, message):
        """Agregar mensaje al log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def execute_validation(self):
        """Ejecutar validación de duplicados"""
        if self.running:
            return
        
        # Verificar configuración
        if not self.carpeta_origen.get() or not self.carpeta_rechazados.get():
            messagebox.showerror("Error", "Debe configurar las carpetas origen y rechazados")
            return
        
        if not os.path.exists(self.carpeta_origen.get()):
            messagebox.showerror("Error", "La carpeta origen no existe")
            return
        
        # Verificar licencia obligatoria
        if not self.licensed:
            messagebox.showerror("Licencia Requerida", 
                "Esta aplicación requiere una licencia válida para funcionar.")
            return
        
        # Iniciar ejecución en hilo separado
        self.running = True
        self.execute_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.progress.start()
        
        thread = threading.Thread(target=self._execute_validation_thread)
        thread.daemon = True
        thread.start()
    
    def _execute_validation_thread(self):
        """Hilo de ejecución de validación"""
        try:
            self.log_message("🔍 Iniciando validación de duplicados...")
            self.log_message(f"📁 Carpeta origen: {self.carpeta_origen.get()}")
            self.log_message(f"📁 Carpeta rechazados: {self.carpeta_rechazados.get()}")
            self.log_message(f"🔧 Modo: {'Simulación' if self.dry_run.get() else 'Real'}")
            
            # Simular proceso de validación
            files_found = []
            origin_dir = self.carpeta_origen.get()
            
            if os.path.exists(origin_dir):
                for file in os.listdir(origin_dir):
                    if os.path.isfile(os.path.join(origin_dir, file)):
                        files_found.append(file)
            
            self.log_message(f"📋 Archivos encontrados en origen: {len(files_found)}")
            
            duplicates_found = 0
            files_processed = 0
            
            # Simular verificación de duplicados
            for file in files_found:
                if not self.running:
                    break
                
                self.log_message(f"🔍 Verificando: {file}")
                time.sleep(0.5)  # Simular procesamiento
                
                # Simular lógica de duplicados (los primeros 3 archivos son duplicados)
                if files_processed < 3:
                    duplicates_found += 1
                    qnap_location = "QNAP1" if files_processed < 2 else "QNAP2"
                    self.log_message(f"❌ DUPLICADO: {file} (encontrado en {qnap_location})")
                    
                    if not self.dry_run.get():
                        # Mover archivo (simulado)
                        source_path = os.path.join(origin_dir, file)
                        dest_path = os.path.join(self.carpeta_rechazados.get(), file)
                        try:
                            os.makedirs(self.carpeta_rechazados.get(), exist_ok=True)
                            if os.path.exists(source_path):
                                os.rename(source_path, dest_path)
                                self.log_message(f"📤 Movido: {file} → rechazados")
                        except Exception as e:
                            self.log_message(f"❌ Error moviendo {file}: {e}")
                    else:
                        self.log_message(f"💾 (SIMULADO) Mover: {file} → rechazados")
                else:
                    self.log_message(f"✅ OK: {file} (no es duplicado)")
                
                files_processed += 1
                
                # Actualizar estadísticas
                stats_text = f"Procesados: {files_processed}/{len(files_found)} | Duplicados: {duplicates_found}"
                self.root.after(0, lambda: self.stats_label.config(text=stats_text))
            
            # Resultados finales
            if self.running:
                self.log_message("=" * 50)
                self.log_message(f"✅ VALIDACIÓN COMPLETADA")
                self.log_message(f"📊 Archivos procesados: {files_processed}")
                self.log_message(f"🔍 Duplicados encontrados: {duplicates_found}")
                self.log_message(f"📁 Archivos únicos: {files_processed - duplicates_found}")
                
                if self.dry_run.get():
                    self.log_message("💾 Modo simulación - No se realizaron cambios")
                else:
                    self.log_message(f"📤 Archivos movidos a rechazados: {duplicates_found}")
                
                self.log_message("=" * 50)
                
                # Mostrar resumen
                self.root.after(0, lambda: messagebox.showinfo("Completado", 
                    f"Validación completada\n\n"
                    f"Archivos procesados: {files_processed}\n"
                    f"Duplicados encontrados: {duplicates_found}\n"
                    f"Modo: {'Simulación' if self.dry_run.get() else 'Real'}"))
            
        except Exception as e:
            self.log_message(f"❌ ERROR: {e}")
            self.logger.error(f"Validation error: {e}")
        
        finally:
            # Restaurar UI
            self.running = False
            self.root.after(0, self._reset_ui)
    
    def _reset_ui(self):
        """Restaurar estado de la UI"""
        self.execute_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.progress.stop()
    
    def stop_validation(self):
        """Detener validación"""
        if self.running:
            self.running = False
            self.log_message("⏹ Deteniendo validación...")
    
    def run(self):
        """Ejecutar aplicación"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.logger.info("Application stopped by user")
        except Exception as e:
            self.logger.error(f"Application error: {e}")

if __name__ == "__main__":
    print("🔍 Gestor - Validación de Duplicados GUI")
    print("=" * 40)
    
    app = ValidationDuplicatesGUI()
    app.run()
