"""
dashboard_pieza5c.py
PIEZA 5C: Métodos auxiliares finales + cierre de aplicación
Gestor de Archivos Corporativo
"""

# CONTINUACIÓN DE LA CLASE GestorDashboard - MÉTODOS AUXILIARES FINALES

    def update_limpieza_progress(self, progress: int, location: str = None, message: str = ""):
        """Actualizar progreso de limpieza"""
        try:
            if hasattr(self, 'limpieza_progress'):
                self.limpieza_progress['value'] = progress
                self.limpieza_progress_label.config(text=f"{progress}%")
                
                if message:
                    self.update_status(message, "info")
                
                self.root.update_idletasks()
        except Exception as e:
            self.logger.error(f"Error actualizando progreso limpieza: {e}")
    
    def update_location_progress(self, location: str, progress: int, found: int, processed: int, status: str):
        """Actualizar progreso individual por ubicación"""
        try:
            if location in self.location_progress_labels:
                widgets = self.location_progress_labels[location]
                widgets['progress']['value'] = progress
                widgets['status'].config(text=status[:12])  # Truncar para que quepa
                widgets['files'].config(text=f"{processed}/{found}")
                
                # Color según estado
                if "✅" in status:
                    widgets['status'].config(foreground='#2E7D32')
                elif "❌" in status:
                    widgets['status'].config(foreground='#C62828')
                elif "Conectando" in status or "Preparando" in status:
                    widgets['status'].config(foreground='#1976D2')
                else:
                    widgets['status'].config(foreground='black')
                    
        except Exception as e:
            self.logger.error(f"Error actualizando progreso ubicación {location}: {e}")
    
    def show_limpieza_results(self, text: str, tab: str):
        """Mostrar resultados de limpieza en pestaña específica"""
        try:
            widget_map = {
                "summary": getattr(self, 'limpieza_summary_text', None),
                "location": getattr(self, 'limpieza_location_text', None),
                "log": getattr(self, 'limpieza_log_text', None)
            }
            
            widget = widget_map.get(tab)
            if widget:
                widget.config(state='normal')
                widget.delete(1.0, tk.END)
                widget.insert(1.0, text)
                widget.see(tk.END)
                widget.config(state='disabled')
                
        except Exception as e:
            self.logger.error(f"Error mostrando resultados limpieza: {e}")
    
    def append_limpieza_log(self, message: str):
        """Agregar mensaje al log de limpieza"""
        try:
            if hasattr(self, 'limpieza_log_text'):
                self.limpieza_log_text.config(state='normal')
                timestamp = datetime.now().strftime("%H:%M:%S")
                self.limpieza_log_text.insert(tk.END, f"[{timestamp}] {message}\n")
                self.limpieza_log_text.see(tk.END)
                self.limpieza_log_text.config(state='disabled')
        except Exception as e:
            self.logger.error(f"Error agregando log limpieza: {e}")
    
    def show_connection_config(self):
        """Mostrar configuración de conexiones"""
        config_window = tk.Toplevel(self.root)
        config_window.title("⚙️ Configuración de Conexiones")
        config_window.geometry("700x600")
        config_window.transient(self.root)
        config_window.grab_set()
        
        # Header
        header_frame = ttk.Frame(config_window)
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(header_frame, text="⚙️ Configuración de Conexiones", 
                 style='Title.TLabel').pack(side=tk.LEFT)
        
        # Info
        info_frame = ttk.LabelFrame(config_window, text="ℹ️ Información", padding="10")
        info_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        info_text = """
Esta ventana permite ver la configuración actual de conexiones.
Para modificar las configuraciones, edite los archivos JSON en la carpeta 'config/'.

Archivo principal: config/config.json
Credenciales: Se gestionan automáticamente por el sistema
        """
        
        ttk.Label(info_frame, text=info_text.strip(), justify=tk.LEFT).pack()
        
        # Configuración actual
        config_frame = ttk.LabelFrame(config_window, text="📋 Configuración Actual", padding="10")
        config_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        config_text = tk.Text(config_frame, wrap=tk.WORD, font=('Consolas', 9), state='disabled')
        config_scroll = ttk.Scrollbar(config_frame, orient=tk.VERTICAL, command=config_text.yview)
        config_text.configure(yscrollcommand=config_scroll.set)
        
        config_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        config_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Cargar configuración
        def load_config():
            try:
                from core.config_manager import config_manager
                
                config_content = []
                config_content.append("📋 CONFIGURACIÓN DE CONEXIONES\n")
                config_content.append("=" * 50 + "\n\n")
                
                locations = config_manager.get_enabled_locations()
                config_content.append(f"📊 Ubicaciones habilitadas: {len(locations)}\n\n")
                
                for location_id in locations:
                    location_config = config_manager.get_location_config(location_id)
                    if location_config:
                        config_content.append(f"📍 {location_id.upper()}:\n")
                        config_content.append(f"   Tipo: {location_config.get('type', 'N/A')}\n")
                        config_content.append(f"   Ruta de red: {location_config.get('network_path', 'N/A')}\n")
                        config_content.append(f"   Ruta simbólica: {location_config.get('symlink_path', 'N/A')}\n")
                        config_content.append(f"   Habilitado: {location_config.get('enabled', False)}\n")
                        
                        # Info de credenciales (sin mostrar contraseñas)
                        creds = location_config.get('credentials', {})
                        if creds:
                            config_content.append(f"   Usuario: {creds.get('username', 'N/A')}\n")
                            config_content.append(f"   Dominio: {creds.get('domain', 'N/A')}\n")
                            config_content.append(f"   Contraseña: {'***' if creds.get('password_encrypted') else 'No configurada'}\n")
                        
                        config_content.append("\n")
                
                config_text.config(state='normal')
                config_text.delete(1.0, tk.END)
                config_text.insert(1.0, ''.join(config_content))
                config_text.config(state='disabled')
                
            except Exception as e:
                config_text.config(state='normal')
                config_text.delete(1.0, tk.END)
                config_text.insert(1.0, f"❌ Error cargando configuración: {e}\n")
                config_text.config(state='disabled')
        
        load_config()
        
        # Botones
        buttons_frame = ttk.Frame(config_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(buttons_frame, text="🔄 Actualizar", command=load_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Cerrar", command=config_window.destroy).pack(side=tk.RIGHT, padx=5)
    
    def show_connection_test_results(self, results):
        """Mostrar resultados de test de conexiones"""
        results_window = tk.Toplevel(self.root)
        results_window.title("📊 Resultados de Test")
        results_window.geometry("600x500")
        results_window.transient(self.root)
        
        # Header
        header_frame = ttk.Frame(results_window)
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(header_frame, text="📊 Resultados del Test de Conexiones", 
                 style='Header.TLabel').pack()
        
        # Resumen
        summary_frame = ttk.LabelFrame(results_window, text="📈 Resumen", padding="10")
        summary_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        online_count = sum(1 for _, result in results if result['connected'])
        total_count = len(results)
        
        ttk.Label(summary_frame, text=f"Ubicaciones online: {online_count}/{total_count}").pack(anchor=tk.W)
        
        if online_count > 0:
            response_times = [r['response_time'] for _, r in results if r['connected'] and 'response_time' in r]
            if response_times:
                avg_time = sum(response_times) / len(response_times)
                ttk.Label(summary_frame, text=f"Tiempo promedio: {avg_time:.2f}s").pack(anchor=tk.W)
        
        # Detalles
        details_frame = ttk.LabelFrame(results_window, text="📋 Detalles", padding="10")
        details_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        details_text = tk.Text(details_frame, wrap=tk.WORD, font=('Consolas', 9))
        details_scroll = ttk.Scrollbar(details_frame, orient=tk.VERTICAL, command=details_text.yview)
        details_text.configure(yscrollcommand=details_scroll.set)
        
        details_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        details_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Generar reporte detallado
        report_content = []
        report_content.append("📊 REPORTE DETALLADO DE CONEXIONES\n")
        report_content.append("=" * 50 + "\n\n")
        
        for location_id, result in results:
            status = "✅ ONLINE" if result['connected'] else "❌ OFFLINE"
            report_content.append(f"📍 {location_id.upper()}: {status}\n")
            
            if result['connected']:
                if 'response_time' in result:
                    report_content.append(f"   Tiempo respuesta: {result['response_time']:.2f}s\n")
                if 'method' in result:
                    report_content.append(f"   Método conexión: {result['method']}\n")
                if 'write_access' in result:
                    access = "✅ Lectura/Escritura" if result['write_access'] else "⚠️ Solo lectura"
                    report_content.append(f"   Acceso: {access}\n")
            else:
                error = result.get('error', 'Error desconocido')
                report_content.append(f"   Error: {error}\n")
            
            report_content.append("\n")
        
        details_text.insert(1.0, ''.join(report_content))
        details_text.config(state='disabled')
        
        # Botones
        buttons_frame = ttk.Frame(results_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        def export_results():
            try:
                export_path = filedialog.asksaveasfilename(
                    title="Exportar Resultados",
                    defaultextension=".txt",
                    filetypes=[("Archivos de texto", "*.txt")]
                )
                
                if export_path:
                    with open(export_path, 'w', encoding='utf-8') as f:
                        f.write(''.join(report_content))
                    messagebox.showinfo("Exportar", "Resultados exportados correctamente")
            except Exception as e:
                messagebox.showerror("Error", f"Error exportando: {e}")
        
        ttk.Button(buttons_frame, text="💾 Exportar", command=export_results).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Cerrar", command=results_window.destroy).pack(side=tk.RIGHT, padx=5)
        
        self.update_status("Test de conexiones completado", "success")
    
    def show_detailed_report(self):
        """Mostrar reporte detallado de conectividad"""
        report_window = tk.Toplevel(self.root)
        report_window.title("📊 Reporte Detallado de Conectividad")
        report_window.geometry("800x600")
        report_window.transient(self.root)
        
        # Text widget para el reporte
        report_text = tk.Text(report_window, wrap=tk.WORD, font=('Consolas', 10))
        report_scroll = ttk.Scrollbar(report_window, orient=tk.VERTICAL, command=report_text.yview)
        report_text.configure(yscrollcommand=report_scroll.set)
        
        report_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        report_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
        # Generar reporte
        report = self.generate_connectivity_report()
        report_text.insert(1.0, report)
        report_text.config(state='disabled')
        
        # Botón cerrar
        ttk.Button(report_window, text="Cerrar", command=report_window.destroy).pack(pady=10)
    
    def generate_connectivity_report(self) -> str:
        """Generar reporte detallado de conectividad"""
        report = []
        report.append("="*80)
        report.append("REPORTE DETALLADO DE CONECTIVIDAD")
        report.append("Gestor de Archivos Corporativo v1.0")
        report.append("="*80)
        report.append(f"Generado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Estadísticas generales
        online_count = 0
        total_count = len(getattr(self, 'connectivity_labels', {}))
        
        for location_id, label in getattr(self, 'connectivity_labels', {}).items():
            if hasattr(label, 'cget') and "🟢" in label.cget('text'):
                online_count += 1
        
        report.append("RESUMEN EJECUTIVO:")
        report.append(f"- Total ubicaciones configuradas: {total_count}")
        report.append(f"- Ubicaciones online: {online_count}")
        report.append(f"- Ubicaciones offline: {total_count - online_count}")
        availability = (online_count/total_count)*100 if total_count > 0 else 0
        report.append(f"- Porcentaje disponibilidad: {availability:.1f}%")
        report.append("")
        
        # Detalles por ubicación
        report.append("DETALLES POR UBICACIÓN:")
        report.append("-" * 50)
        
        for location_id in getattr(self, 'connectivity_labels', {}):
            if hasattr(self, 'connectivity_labels'):
                status_text = self.connectivity_labels[location_id].cget('text')
                
                report.append(f"\n{location_id.upper()}:")
                report.append(f"  Estado: {status_text}")
                
                if hasattr(self, 'connectivity_details') and location_id in self.connectivity_details:
                    details_text = self.connectivity_details[location_id].cget('text')
                    report.append(f"  Detalles: {details_text}")
                
                # Información adicional del estado interno
                if hasattr(self, 'connectivity_status') and location_id in self.connectivity_status:
                    status_data = self.connectivity_status[location_id]
                    if status_data.get('connected', False):
                        if 'response_time' in status_data:
                            report.append(f"  Tiempo respuesta: {status_data['response_time']:.2f}s")
                        if 'method' in status_data:
                            report.append(f"  Método conexión: {status_data['method']}")
                        if 'write_access' in status_data:
                            access = "✅ Lectura/Escritura" if status_data['write_access'] else "⚠️ Solo lectura"
                            report.append(f"  Acceso: {access}")
        
        report.append("")
        report.append("="*80)
        
        return "\n".join(report)
    
    def on_closing(self):
        """Manejar cierre de la aplicación"""
        try:
            # Verificar si hay operaciones activas
            if any(self.operaciones_activas.values()):
                active_ops = [op for op, active in self.operaciones_activas.items() if active]
                op_text = ", ".join(active_ops)
                
                if not messagebox.askokcancel("Operaciones Activas", 
                    f"Hay operaciones en ejecución: {op_text}\n\n"
                    f"¿Cerrar la aplicación de todas formas?\n"
                    f"Las operaciones en curso se detendrán."):
                    return
            
            # Detener threads
            self.shutdown_threads = True
            
            # Detener logs en tiempo real si están activos
            if hasattr(self, 'realtime_logs_active'):
                self.realtime_logs_active = False
            
            # Guardar configuración automáticamente
            try:
                if hasattr(self, 'validacion_origen_var') and self.validacion_origen_var.get():
                    self.save_validacion_config()
                if hasattr(self, 'limpieza_archivo_var') and self.limpieza_archivo_var.get():
                    self.save_limpieza_config()
            except:
                pass  # No es crítico si falla el guardado
            
            # Log de cierre
            self.logger.info("=== APLICACIÓN CERRADA POR EL USUARIO ===")
            
            # Cerrar ventana
            self.root.destroy()
            
        except Exception as e:
            self.logger.error(f"Error en cierre de aplicación: {e}")
            # Forzar cierre si hay error
            self.root.destroy()
    
    def run(self):
        """Ejecutar la aplicación principal"""
        try:
            self.logger.info("Iniciando bucle principal de la aplicación")
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
            
        except Exception as e:
            self.logger.error(f"Error en ejecución del dashboard: {e}")
            messagebox.showerror("Error Fatal", 
                               f"Error ejecutando la aplicación:\n{e}\n\n"
                               f"Consulte los logs para más detalles.")
        finally:
            self.logger.info("=== GESTOR DE ARCHIVOS CORPORATIVO FINALIZADO ===")

# === MÉTODOS ESTÁTICOS Y UTILITARIOS ===

def main():
    """Función principal para ejecutar el dashboard"""
    import sys
    
    # Configurar logging básico
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger('dashboard.main')
    
    try:
        logger.info("=== INICIANDO GESTOR DE ARCHIVOS CORPORATIVO ===")
        
        # Verificar dependencias críticas
        try:
            import tkinter
            import threading
            from pathlib import Path
        except ImportError as e:
            logger.error(f"Dependencia faltante: {e}")
            print(f"❌ Error: Dependencia faltante - {e}")
            input("Presione Enter para salir...")
            sys.exit(1)
        
        # Crear y ejecutar dashboard
        dashboard = GestorDashboard()
        dashboard.run()
        
    except KeyboardInterrupt:
        logger.info("Aplicación interrumpida por el usuario (Ctrl+C)")
        print("\n🛑 Aplicación detenida por el usuario")
        
    except Exception as e:
        logger.error(f"Error fatal en main: {e}")
        print(f"❌ Error fatal: {e}")
        
        # Mostrar información de debug si es necesario
        if "--debug" in sys.argv:
            import traceback
            print("\n🔍 Información de debug:")
            traceback.print_exc()
        
        input("Presione Enter para salir...")
        sys.exit(1)

if __name__ == "__main__":
    main()

# FIN PIEZA 5C - DASHBOARD COMPLETO
