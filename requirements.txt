# Gestor de Archivos Corporativo - Dependencias
# ============================================

# Seguridad y sistema
cryptography>=41.0.0    # Encriptación de credenciales
psutil>=5.9.0           # Información del sistema y procesos
pywin32>=307            # APIs Windows (SMB, servicios)

# Interfaz gráfica
Pillow>=10.0.0          # Manejo de imágenes para logos

# Utilidades
requests>=2.31.0        # HTTP requests (funciones futuras)

# Compilación
pyinstaller>=6.0.0      # Compilar a .exe

# Dependencias nativas (incluidas en Python 3.11+)
# tkinter              # GUI - viene con Python
# pathlib              # Manejo rutas - nativo Python 3.4+
# logging              # Logging - nativo
# json                 # JSON - nativo
# threading            # Threading - nativo
# subprocess           # Comandos sistema - nativo
# hashlib              # Hashing - nativo
# base64               # Encoding - nativo
# configparser         # Configuración - nativo
