"""
dashboard_pieza1.py
PIEZA 1: Estructura base + imports + logo + inicialización
Gestor de Archivos Corporativo
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
from pathlib import Path
import logging
from typing import Dict, Any
from datetime import datetime

# Importaciones para imágenes
try:
    import PIL.Image
    import PIL.ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

class GestorDashboard:
    """Dashboard principal con interfaz por pestañas - VERSIÓN COMPLETA"""
    
    def __init__(self):
        """Inicializar dashboard"""
        self.setup_logging()
        self.setup_ui()  # Crear ventana principal PRIMERO
        self.setup_variables()  # Variables DESPUÉS de la ventana
        self.create_tabs()  # Crear pestañas AL FINAL
        self.setup_threads()  # Threads para auto-actualización
        self.load_saved_config()  # Cargar configuración guardada
        self.initial_connectivity_check()  # Check inicial de conectividad
        
        self.logger.info("Dashboard completo inicializado correctamente")
    
    def setup_logging(self):
        """Configurar sistema de logging"""
        try:
            from core.logger import gestor_logger
            self.logger = gestor_logger.get_logger('sistema')
        except ImportError:
            logging.basicConfig(level=logging.INFO)
            self.logger = logging.getLogger('dashboard')
    
    def setup_variables(self):
        """Configurar variables de estado"""
        # Estado de operaciones
        self.operaciones_activas = {'validacion': False, 'limpieza': False}
        
        # Variables de configuración - validación
        self.validacion_origen_var = tk.StringVar()
        self.validacion_rechazados_var = tk.StringVar() 
        self.validacion_dry_run_var = tk.BooleanVar(value=True)
        
        # Variables de configuración - limpieza
        self.limpieza_archivo_var = tk.StringVar()
        self.limpieza_dry_run_var = tk.BooleanVar(value=True)
        
        # Variables de estado y logs
        self.connectivity_status = {}
        self.log_category_var = tk.StringVar(value='sistema')
        self.auto_refresh_logs = tk.BooleanVar(value=False)
        
        # Variables de estadísticas
        self.last_operation_stats = {}
        
        # Variables para threads
        self.shutdown_threads = False
    
    def setup_ui(self):
        """Configurar interfaz de usuario principal"""
        # Ventana principal
        self.root = tk.Tk()
        self.root.title("Gestor de Archivos Corporativo v1.0")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 700)
        
        # Configurar icono de ventana
        self.setup_window_icon()
        
        # Configurar estilos
        self.setup_styles()
        
        # Crear estructura principal
        self.create_main_structure()
        
        # Configurar eventos de ventana
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_window_icon(self):
        """Configurar icono de la ventana"""
        try:
            logo_path = Path(__file__).parent.parent.parent / "logo_igson.png"
            if logo_path.exists() and PIL_AVAILABLE:
                # Cargar como icono de ventana
                icon_img = PIL.Image.open(logo_path)
                icon_img = icon_img.resize((32, 32), PIL.Image.Resampling.LANCZOS)
                self.window_icon = PIL.ImageTk.PhotoImage(icon_img)
                self.root.iconphoto(False, self.window_icon)
                self.logger.info("Icono de ventana cargado correctamente")
        except Exception as e:
            self.logger.debug(f"No se pudo cargar el icono de ventana: {e}")
    
    def setup_styles(self):
        """Configurar estilos personalizados mejorados"""
        style = ttk.Style()
        
        # Estilos para estado de conectividad
        style.configure('Online.TLabel', foreground='#2E7D32', font=('Arial', 9, 'bold'))  # Verde oscuro
        style.configure('Offline.TLabel', foreground='#C62828', font=('Arial', 9, 'bold'))  # Rojo oscuro
        style.configure('Warning.TLabel', foreground='#F57C00', font=('Arial', 9, 'bold'))  # Naranja
        style.configure('Testing.TLabel', foreground='#1976D2', font=('Arial', 9, 'bold'))  # Azul
        
        # Estilos para texto
        style.configure('Status.TLabel', font=('Arial', 9))
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Subtitle.TLabel', font=('Arial', 10))
        style.configure('Small.TLabel', font=('Arial', 8), foreground='gray')
        
        # Estilos para botones
        style.configure('Action.TButton', font=('Arial', 9, 'bold'))
        style.configure('Danger.TButton', font=('Arial', 9, 'bold'))
    
    def create_main_structure(self):
        """Crear estructura principal de la aplicación"""
        # Header con logo y título
        self.create_header()
        
        # Notebook principal
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        # Barra de estado
        self.setup_status_bar()
    
    def create_header(self):
        """Crear header con logo y título"""
        header_frame = ttk.Frame(self.root)
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Logo principal
        if PIL_AVAILABLE:
            try:
                logo_path = Path(__file__).parent.parent.parent / "logo_igson.png"
                if logo_path.exists():
                    img = PIL.Image.open(logo_path)
                    img = img.resize((64, 64), PIL.Image.Resampling.LANCZOS)
                    self.logo_main = PIL.ImageTk.PhotoImage(img)
                    
                    ttk.Label(header_frame, image=self.logo_main).pack(side=tk.LEFT, padx=10)
                    self.logger.info("Logo principal cargado correctamente")
            except Exception as e:
                self.logger.debug(f"Logo principal no disponible: {e}")
        
        # Información del título
        title_frame = ttk.Frame(header_frame)
        title_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=20)
        
        ttk.Label(title_frame, text="Gestor de Archivos Corporativo", 
                 style='Title.TLabel').pack(anchor=tk.W)
        ttk.Label(title_frame, text="Sistema de Validación y Limpieza de Archivos", 
                 style='Subtitle.TLabel').pack(anchor=tk.W)
        ttk.Label(title_frame, text="v1.0 - Desarrollado por Igson", 
                 style='Small.TLabel').pack(anchor=tk.W)
        
        # Información de estado en tiempo real
        status_info_frame = ttk.Frame(header_frame)
        status_info_frame.pack(side=tk.RIGHT, padx=10)
        
        # Reloj en tiempo real
        self.clock_label = ttk.Label(status_info_frame, text="", style='Status.TLabel')
        self.clock_label.pack(anchor=tk.E)
        
        # Contador de conexiones
        self.connection_summary_label = ttk.Label(status_info_frame, text="Conexiones: 0/6", style='Status.TLabel')
        self.connection_summary_label.pack(anchor=tk.E)
        
        # Iniciar actualización del reloj
        self.update_clock()
        
        # Separador
        ttk.Separator(self.root, orient='horizontal').pack(fill=tk.X, padx=10, pady=5)
    
    def setup_status_bar(self):
        """Configurar barra de estado avanzada"""
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=10, pady=5)
        
        # Logo pequeño en la barra
        if PIL_AVAILABLE:
            try:
                logo_path = Path(__file__).parent.parent.parent / "logo_igson.png"
                if logo_path.exists():
                    img = PIL.Image.open(logo_path)
                    img = img.resize((16, 16), PIL.Image.Resampling.LANCZOS)
                    self.logo_small = PIL.ImageTk.PhotoImage(img)
                    
                    ttk.Label(self.status_frame, image=self.logo_small).pack(side=tk.LEFT, padx=5)
            except Exception as e:
                self.logger.debug(f"Logo pequeño no disponible: {e}")
        
        # Mensaje de estado principal
        self.status_label = ttk.Label(self.status_frame, text="Sistema iniciado - Listo", style='Status.TLabel')
        self.status_label.pack(side=tk.LEFT, padx=10)
        
        # Frame para controles de la derecha
        right_controls = ttk.Frame(self.status_frame)
        right_controls.pack(side=tk.RIGHT)
        
        # Indicador de operaciones activas
        self.operations_indicator = ttk.Label(right_controls, text="⭕ Sin operaciones", style='Status.TLabel')
        self.operations_indicator.pack(side=tk.RIGHT, padx=5)
        
        # Botón de actualización general
        ttk.Button(right_controls, text="🔄 Actualizar", 
                  command=self.refresh_all).pack(side=tk.RIGHT, padx=5)
        
        # Botón de configuración
        ttk.Button(right_controls, text="⚙️ Config", 
                  command=self.show_config_dialog).pack(side=tk.RIGHT, padx=5)
    
    def update_clock(self):
        """Actualizar reloj en tiempo real"""
        try:
            current_time = datetime.now().strftime("%H:%M:%S")
            current_date = datetime.now().strftime("%Y-%m-%d")
            self.clock_label.config(text=f"{current_date} {current_time}")
            
            # Programar próxima actualización
            self.root.after(1000, self.update_clock)
        except Exception as e:
            self.logger.error(f"Error actualizando reloj: {e}")
    
    def update_status(self, message: str, level: str = "info"):
        """Actualizar mensaje de estado con nivel"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        status_message = f"[{timestamp}] {message}"
        
        # Colores según nivel
        colors = {
            "info": "black",
            "warning": "#F57C00",
            "error": "#C62828",
            "success": "#2E7D32"
        }
        
        self.status_label.config(text=status_message)
        if level in colors:
            self.status_label.config(foreground=colors[level])
        
        # Log del mensaje
        if level == "error":
            self.logger.error(message)
        elif level == "warning":
            self.logger.warning(message)
        else:
            self.logger.info(message)
    
    def update_operations_indicator(self):
        """Actualizar indicador de operaciones activas"""
        active_ops = [op for op, active in self.operaciones_activas.items() if active]
        
        if not active_ops:
            self.operations_indicator.config(text="⭕ Sin operaciones", foreground="gray")
        elif len(active_ops) == 1:
            op_names = {"validacion": "Validación", "limpieza": "Limpieza"}
            op_name = op_names.get(active_ops[0], active_ops[0])
            self.operations_indicator.config(text=f"🔄 {op_name}", foreground="#1976D2")
        else:
            self.operations_indicator.config(text=f"🔄 {len(active_ops)} operaciones", foreground="#1976D2")
    
    def update_connection_counter(self):
        """Actualizar contador de conexiones"""
        try:
            online_count = 0
            total_count = len(getattr(self, 'connectivity_labels', {}))
            
            for location_id, label in getattr(self, 'connectivity_labels', {}).items():
                if hasattr(label, 'cget') and "🟢" in label.cget('text'):
                    online_count += 1
            
            # Actualizar en header
            self.connection_summary_label.config(text=f"Conexiones: {online_count}/{total_count}")
            
            # Color según estado
            if online_count == total_count:
                color = "#2E7D32"  # Verde
            elif online_count > total_count // 2:
                color = "#F57C00"  # Naranja
            else:
                color = "#C62828"  # Rojo
            
            self.connection_summary_label.config(foreground=color)
            
        except Exception as e:
            self.logger.error(f"Error actualizando contador de conexiones: {e}")
    
    def show_config_dialog(self):
        """Mostrar diálogo de configuración"""
        config_window = tk.Toplevel(self.root)
        config_window.title("Configuración")
        config_window.geometry("400x300")
        config_window.transient(self.root)
        config_window.grab_set()
        
        ttk.Label(config_window, text="Configuración del Sistema", 
                 style='Header.TLabel').pack(pady=10)
        
        # Notebook para diferentes configuraciones
        config_notebook = ttk.Notebook(config_window)
        config_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Tab: General
        general_frame = ttk.Frame(config_notebook)
        config_notebook.add(general_frame, text="General")
        
        ttk.Label(general_frame, text="Configuración general del sistema").pack(pady=10)
        
        # Auto-refresh logs
        ttk.Checkbutton(general_frame, text="Auto-actualizar logs", 
                       variable=self.auto_refresh_logs).pack(anchor=tk.W, padx=10, pady=5)
        
        # Tab: Conexiones
        conn_frame = ttk.Frame(config_notebook)
        config_notebook.add(conn_frame, text="Conexiones")
        
        ttk.Label(conn_frame, text="Configuración de conexiones").pack(pady=10)
        ttk.Label(conn_frame, text="(Configuración avanzada en archivos JSON)").pack(pady=5)
        
        # Botones
        buttons_frame = ttk.Frame(config_window)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(buttons_frame, text="Cerrar", 
                  command=config_window.destroy).pack(side=tk.RIGHT, padx=5)
        ttk.Button(buttons_frame, text="Aplicar", 
                  command=lambda: self.apply_config(config_window)).pack(side=tk.RIGHT, padx=5)
    
    def apply_config(self, config_window):
        """Aplicar configuración"""
        try:
            # Aquí se aplicarían los cambios de configuración
            self.update_status("Configuración aplicada", "success")
            config_window.destroy()
        except Exception as e:
            self.update_status(f"Error aplicando configuración: {e}", "error")

# FIN PIEZA 1
