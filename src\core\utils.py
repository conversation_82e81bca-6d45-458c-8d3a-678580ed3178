"""
Gestor de Archivos Corporativo - Utilidades Comunes
==================================================

Funciones helper reutilizables para todo el proyecto
"""

import os
import sys
import shutil
import hashlib
import platform
import subprocess
import tempfile
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
import logging

def get_app_directory() -> Path:
    """Obtiene directorio base de la aplicación"""
    if getattr(sys, 'frozen', False):
        # Ejecutable compilado
        return Path(sys.executable).parent
    else:
        # Desarrollo
        return Path(__file__).parent.parent

def get_logs_directory() -> Path:
    """Obtiene directorio de logs"""
    app_dir = get_app_directory()
    logs_dir = app_dir / "logs"
    logs_dir.mkdir(exist_ok=True)
    return logs_dir

def get_config_directory() -> Path:
    """Obtiene directorio de configuración"""
    app_dir = get_app_directory()
    config_dir = app_dir / "config"
    config_dir.mkdir(exist_ok=True)
    return config_dir

def sanitize_filename(filename: str) -> str:
    """Limpia nombre de archivo para uso seguro"""
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    return filename.strip()

def format_file_size(size_bytes: int) -> str:
    """Formatea tamaño de archivo en formato legible"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024.0 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def get_file_hash(file_path: Path, algorithm: str = 'md5') -> Optional[str]:
    """Calcula hash de archivo para comparación"""
    try:
        hash_obj = hashlib.new(algorithm)
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)
        return hash_obj.hexdigest()
    except Exception as e:
        logging.error(f"Error calculating hash for {file_path}: {e}")
        return None

def is_file_locked(file_path: Path) -> bool:
    """Verifica si un archivo está siendo usado por otro proceso"""
    try:
        with open(file_path, 'r+'):
            pass
        return False
    except (IOError, OSError, PermissionError):
        return True

def safe_delete_file(file_path: Path, max_retries: int = 3) -> bool:
    """Elimina archivo con reintentos si está bloqueado"""
    for attempt in range(max_retries):
        try:
            if file_path.exists():
                file_path.unlink()
                return True
            return True  # Ya no existe
        except (OSError, PermissionError) as e:
            if attempt < max_retries - 1:
                logging.warning(f"Retry {attempt + 1} deleting {file_path}: {e}")
                time.sleep(1)
            else:
                logging.error(f"Failed to delete {file_path} after {max_retries} attempts: {e}")
                return False
    return False

def safe_move_file(source: Path, destination: Path, overwrite: bool = False) -> bool:
    """Mueve archivo de forma segura con validaciones"""
    try:
        # Verificar que source existe
        if not source.exists():
            logging.error(f"Source file does not exist: {source}")
            return False
        
        # Crear directorio destino si no existe
        destination.parent.mkdir(parents=True, exist_ok=True)
        
        # Verificar si destino existe
        if destination.exists():
            if not overwrite:
                logging.error(f"Destination exists and overwrite=False: {destination}")
                return False
            else:
                # Eliminar destino existente
                if not safe_delete_file(destination):
                    return False
        
        # Mover archivo
        shutil.move(str(source), str(destination))
        logging.info(f"File moved: {source} → {destination}")
        return True
        
    except Exception as e:
        logging.error(f"Error moving file {source} to {destination}: {e}")
        return False

def get_free_space(path: Path) -> Optional[int]:
    """Obtiene espacio libre en bytes para una ruta"""
    try:
        if platform.system() == 'Windows':
            free_bytes = shutil.disk_usage(path)[2]
        else:
            statvfs = os.statvfs(path)
            free_bytes = statvfs.f_frsize * statvfs.f_bavail
        return free_bytes
    except Exception as e:
        logging.error(f"Error getting free space for {path}: {e}")
        return None

def ping_host(hostname: str, timeout: int = 5) -> bool:
    """Hace ping a un host para verificar conectividad"""
    try:
        if platform.system().lower() == 'windows':
            cmd = ['ping', '-n', '1', '-w', str(timeout * 1000), hostname]
        else:
            cmd = ['ping', '-c', '1', '-W', str(timeout), hostname]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout + 2)
        return result.returncode == 0
    except Exception as e:
        logging.error(f"Error pinging {hostname}: {e}")
        return False

def extract_hostname_from_path(network_path: str) -> Optional[str]:
    """Extrae hostname de una ruta de red UNC"""
    try:
        if network_path.startswith('\\\\'):
            parts = network_path[2:].split('\\')
            if parts:
                return parts[0]
    except Exception:
        pass
    return None

def is_network_path(path: str) -> bool:
    """Verifica si una ruta es de red (UNC)"""
    return path.startswith('\\\\')

def is_valid_path(path: Union[str, Path]) -> bool:
    """Verifica si una ruta es válida y accesible"""
    try:
        path_obj = Path(path)
        return path_obj.exists() and path_obj.is_dir()
    except Exception:
        return False

def create_temp_file(suffix: str = '.tmp', prefix: str = 'gestor_') -> Path:
    """Crea archivo temporal con limpieza automática"""
    temp_fd, temp_path = tempfile.mkstemp(suffix=suffix, prefix=prefix)
    os.close(temp_fd)  # Cerrar file descriptor
    return Path(temp_path)

def read_text_file(file_path: Path, encoding: str = 'utf-8') -> Optional[List[str]]:
    """Lee archivo de texto y retorna líneas"""
    try:
        with open(file_path, 'r', encoding=encoding) as f:
            lines = [line.strip() for line in f.readlines() if line.strip()]
        return lines
    except Exception as e:
        logging.error(f"Error reading text file {file_path}: {e}")
        return None

def write_text_file(file_path: Path, lines: List[str], encoding: str = 'utf-8') -> bool:
    """Escribe líneas a archivo de texto"""
    try:
        file_path.parent.mkdir(parents=True, exist_ok=True)
        with open(file_path, 'w', encoding=encoding) as f:
            for line in lines:
                f.write(f"{line}\n")
        return True
    except Exception as e:
        logging.error(f"Error writing text file {file_path}: {e}")
        return False

def validate_file_list(file_list: List[str], base_path: Optional[Path] = None) -> Dict[str, List[str]]:
    """Valida lista de archivos y categoriza por estado"""
    result = {
        'valid': [],
        'not_found': [],
        'invalid_names': []
    }
    
    for filename in file_list:
        # Validar nombre de archivo
        if not filename or '..' in filename or any(char in filename for char in '<>:"|?*'):
            result['invalid_names'].append(filename)
            continue
        
        # Si hay base_path, verificar existencia
        if base_path:
            file_path = base_path / filename
            if file_path.exists():
                result['valid'].append(filename)
            else:
                result['not_found'].append(filename)
        else:
            result['valid'].append(filename)
    
    return result

def format_duration(seconds: float) -> str:
    """Formatea duración en formato legible"""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds // 60
        secs = seconds % 60
        return f"{int(minutes)}m {secs:.0f}s"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        return f"{int(hours)}h {int(minutes)}m"

def get_timestamp_string() -> str:
    """Obtiene timestamp actual en formato string"""
    return time.strftime("%Y%m%d_%H%M%S")

def get_system_info() -> Dict[str, str]:
    """Obtiene información del sistema"""
    return {
        'platform': platform.platform(),
        'system': platform.system(),
        'release': platform.release(),
        'version': platform.version(),
        'machine': platform.machine(),
        'processor': platform.processor(),
        'hostname': platform.node(),
        'python_version': platform.python_version()
    }

class ProgressTracker:
    """Seguimiento simple de progreso para operaciones largas"""
    
    def __init__(self, total: int, description: str = "Processing"):
        self.total = total
        self.current = 0
        self.description = description
        self.start_time = time.time()
        self.logger = logging.getLogger(__name__)
    
    def update(self, increment: int = 1) -> None:
        """Actualiza progreso"""
        self.current += increment
        percentage = (self.current / self.total) * 100 if self.total > 0 else 0
        
        elapsed = time.time() - self.start_time
        if self.current > 0:
            eta = (elapsed / self.current) * (self.total - self.current)
            eta_str = format_duration(eta)
        else:
            eta_str = "N/A"
        
        self.logger.info(f"{self.description}: {self.current}/{self.total} ({percentage:.1f}%) - ETA: {eta_str}")
    
    def is_complete(self) -> bool:
        """Verifica si el progreso está completo"""
        return self.current >= self.total
    
    def get_progress_info(self) -> Dict[str, Union[int, float, str]]:
        """Obtiene información detallada del progreso"""
        elapsed = time.time() - self.start_time
        percentage = (self.current / self.total) * 100 if self.total > 0 else 0
        
        if self.current > 0:
            eta = (elapsed / self.current) * (self.total - self.current)
            eta_str = format_duration(eta)
        else:
            eta_str = "N/A"
        
        return {
            'current': self.current,
            'total': self.total,
            'percentage': percentage,
            'elapsed': elapsed,
            'elapsed_str': format_duration(elapsed),
            'eta': eta_str,
            'description': self.description
        }

if __name__ == "__main__":
    # Test básico de utilidades
    print("🛠️  Utils Test")
    print("=" * 30)
    
    # Test directorios
    print(f"App Directory: {get_app_directory()}")
    print(f"Logs Directory: {get_logs_directory()}")
    print(f"Config Directory: {get_config_directory()}")
    
    # Test formateo
    print(f"\nFile Size: {format_file_size(1024*1024*1.5)}")
    print(f"Duration: {format_duration(3661.5)}")
    print(f"Timestamp: {get_timestamp_string()}")
    
    # Test validación de archivos
    test_files = ["archivo1.txt", "archivo2.pdf", "archivo..invalido", "archivo<malo>.txt"]
    validation = validate_file_list(test_files)
    print(f"\nFile Validation:")
    print(f"  Valid: {validation['valid']}")
    print(f"  Invalid: {validation['invalid_names']}")
    
    # Test información del sistema
    sys_info = get_system_info()
    print(f"\nSystem: {sys_info['system']} {sys_info['release']}")
    print(f"Python: {sys_info['python_version']}")
    
    # Test progress tracker
    print(f"\nProgress Tracker Test:")
    progress = ProgressTracker(100, "Test Operation")
    for i in range(0, 101, 25):
        progress.current = i
        info = progress.get_progress_info()
        print(f"  {info['current']}/{info['total']} ({info['percentage']:.1f}%)")
