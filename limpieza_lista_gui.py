"""
GUI Independiente - Limpieza por Lista
====================================

Interfaz gráfica Tkinter con sistema de licencias integrado
Generado automáticamente por merge de piezas
"""


import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
from datetime import datetime
import threading
import time
from pathlib import Path

# Agregar el directorio actual al path para imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Imports del sistema
try:
    from gestor_license_manager import GestorLicenseManager, show_license_dialog
    from src.core.config_manager import ConfigManager
    from src.core.logger import LoggerManager
    from src.core.utils import create_test_environment
except ImportError as e:
    print(f"Warning: Some modules not found: {e}")
    print("Running in standalone mode...")

class CleanupByListGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Gestor de Archivos - Limpieza por Lista")
        self.root.geometry("900x750")
        self.root.resizable(True, True)
        
        # Configuración inicial
        self.licensed = False
        self.running = False
        
        # Variables de configuración
        self.archivo_lista = tk.StringVar()
        self.dry_run = tk.BooleanVar(value=True)
        
        # Variables de ubicaciones (6 ubicaciones: 2 QNAPs + 4 PCs)
        self.ubicaciones = {
            "QNAP1": {"enabled": tk.BooleanVar(value=True), "path": tk.StringVar()},
            "QNAP2": {"enabled": tk.BooleanVar(value=True), "path": tk.StringVar()},
            "PC1": {"enabled": tk.BooleanVar(value=True), "path": tk.StringVar()},
            "PC2": {"enabled": tk.BooleanVar(value=True), "path": tk.StringVar()},
            "PC3": {"enabled": tk.BooleanVar(value=True), "path": tk.StringVar()},
            "PC4": {"enabled": tk.BooleanVar(value=True), "path": tk.StringVar()}
        }
        
        # Configurar logging
        try:
            self.logger = LoggerManager("limpieza_gui").get_logger()
        except:
            import logging
            logging.basicConfig(level=logging.INFO)
            self.logger = logging.getLogger("limpieza_gui")
        
        self.setup_ui()
        self.check_license()
        self.load_default_config()
    
    def setup_header(self, parent):
        """Configurar header con logo IGSON"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill='x', pady=(0,10))
        
        # Intentar cargar logo IGSON
        try:
            from PIL import Image, ImageTk
            logo_path = os.path.join(current_dir, "logo_igson.png")
            if os.path.exists(logo_path):
                img = Image.open(logo_path)
                img = img.resize((80, 80), Image.Resampling.LANCZOS)
                self.logo_photo = ImageTk.PhotoImage(img)
                
                logo_label = ttk.Label(header_frame, image=self.logo_photo)
                logo_label.pack(side='left', padx=(0,15))
            else:
                # Logo de texto si no hay imagen
                ttk.Label(header_frame, text="🏢", font=("Arial", 40)).pack(side='left', padx=(0,15))
        except ImportError:
            # Logo de texto si no hay PIL
            ttk.Label(header_frame, text="🏢", font=("Arial", 40)).pack(side='left', padx=(0,15))
        
        # Información de la aplicación
        info_frame = ttk.Frame(header_frame)
        info_frame.pack(side='left', fill='both', expand=True)
        
        ttk.Label(info_frame, text="IGSON - Gestor de Archivos Corporativo", 
                 font=("Arial", 16, "bold")).pack(anchor='w')
        ttk.Label(info_frame, text="Limpieza por Lista", 
                 font=("Arial", 12)).pack(anchor='w')
        ttk.Label(info_frame, text="Versión 1.0 - Junio 2025", 
                 font=("Arial", 9), foreground="gray").pack(anchor='w')


        def check_license(self):
        """Verificar licencia al iniciar"""
        try:
            license_manager = GestorLicenseManager("limpieza")
            valid, message = license_manager.validate_license()

            if valid:
                self.licensed = True
                self.license_status.config(text=f"✅ Licenciado: {message}", foreground="green")
                self.logger.info(f"License valid: {message}")
            else:
                self.licensed = False
                self.license_status.config(text=f"❌ Sin licencia: {message}", foreground="red")
                self.logger.warning(f"License invalid: {message}")

                # Ofrecer activación
                response = messagebox.askyesno("Licencia", 
                    f"Licencia no válida: {message}\n\n¿Desea activar licencia?")
                if response:
                    activated = show_license_dialog(self.root, "limpieza")
                    if activated:
                        self.check_license()  # Re-verificar
        except Exception as e:
            self.logger.error(f"License check error: {e}")
            self.licensed = False
            self.license_status.config(text="❌ Error verificando licencia", foreground="red")

        def setup_ui(self):
        """Configurar interfaz de usuario"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill='both', expand=True)

        # Header con logo IGSON
        self.setup_header(main_frame)

        # Frame de configuración de archivo
        file_frame = ttk.LabelFrame(main_frame, text="Archivo de Lista", padding="10")
        file_frame.pack(fill='x', pady=(10,0))

        ttk.Label(file_frame, text="Archivo TXT con lista:").grid(row=0, column=0, sticky='w', pady=5)
        ttk.Entry(file_frame, textvariable=self.archivo_lista, width=60).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(file_frame, text="Examinar", 
                  command=self.select_archivo_lista).grid(row=0, column=2, pady=5)

        # Modo dry run
        ttk.Checkbutton(file_frame, text="Modo Simulación (Dry Run)", 
                       variable=self.dry_run).grid(row=1, column=0, columnspan=2, sticky='w', pady=10)

        # Frame de ubicaciones
        locations_frame = ttk.LabelFrame(main_frame, text="Ubicaciones (6 ubicaciones)", padding="10")
        locations_frame.pack(fill='x', pady=(10,0))

        # Crear checkboxes y campos para ubicaciones
        row = 0
        for location, vars_dict in self.ubicaciones.items():
            # Checkbox para habilitar/deshabilitar
            ttk.Checkbutton(locations_frame, text=location, 
                           variable=vars_dict["enabled"]).grid(row=row, column=0, sticky='w', pady=2)

            # Campo de ruta
            ttk.Entry(locations_frame, textvariable=vars_dict["path"], width=50).grid(row=row, column=1, padx=5, pady=2)

            # Botón examinar
            ttk.Button(locations_frame, text="...", width=3,
                      command=lambda loc=location: self.select_location_path(loc)).grid(row=row, column=2, pady=2)

            row += 1

        def update_license(self):
        """Actualizar licencia"""
        activated = show_license_dialog(self.root, "limpieza")
        if activated:
            self.check_license()

        def select_archivo_lista(self):
        """Seleccionar archivo de lista"""
        file_path = filedialog.askopenfilename(
            title="Seleccionar archivo de lista",
            filetypes=[("Archivos de texto", "*.txt"), ("Todos los archivos", "*.*")]
        )
        if file_path:
            self.archivo_lista.set(file_path)

        def select_location_path(self, location):
        """Seleccionar ruta para ubicación específica"""
        folder = filedialog.askdirectory(title=f"Seleccionar ruta para {location}")
        if folder:
            self.ubicaciones[location]["path"].set(folder)


        
        """Continuar configuración UI - Controles"""
        # Frame de control
            control_frame = ttk.LabelFrame(main_frame, text="Control", padding="10")
        control_frame.pack(fill='x', pady=(10,0))

        # Botones de control
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill='x')

        self.execute_btn = ttk.Button(button_frame, text="🗑️ Ejecutar Limpieza", 
                                     command=self.execute_cleanup, state='normal')
        self.execute_btn.pack(side='left', padx=(0,10))

        self.stop_btn = ttk.Button(button_frame, text="⏹ Detener", 
                                  command=self.stop_cleanup, state='disabled')
        self.stop_btn.pack(side='left', padx=(0,10))

        ttk.Button(button_frame, text="🔄 Actualizar Licencia", 
                  command=self.update_license).pack(side='left', padx=(0,10))

        ttk.Button(button_frame, text="📄 Previsualizar Lista", 
                  command=self.preview_list).pack(side='left', padx=(0,10))

        # Barra de progreso general
        self.progress = ttk.Progressbar(control_frame, mode='determinate')
        self.progress.pack(fill='x', pady=(10,5))

        # Frame de progreso por ubicación
        self.progress_frame = ttk.Frame(control_frame)
        self.progress_frame.pack(fill='x', pady=5)

        self.location_progress = {}
        col = 0
        for location in self.ubicaciones.keys():
            frame = ttk.Frame(self.progress_frame)
            frame.grid(row=0, column=col, padx=5, sticky='ew')

            ttk.Label(frame, text=location, font=("Arial", 8)).pack()
            progress_bar = ttk.Progressbar(frame, mode='determinate', length=100)
            progress_bar.pack(fill='x')

            self.location_progress[location] = progress_bar
            col += 1

        # Frame de resultados
        results_frame = ttk.LabelFrame(main_frame, text="Resultados", padding="10")
        results_frame.pack(fill='both', expand=True, pady=(10,0))

        # Área de texto para logs
        self.log_text = scrolledtext.ScrolledText(results_frame, height=12, width=90)
        self.log_text.pack(fill='both', expand=True)

        # Frame de estadísticas
        stats_frame = ttk.Frame(results_frame)
        stats_frame.pack(fill='x', pady=(10,0))

        self.stats_label = ttk.Label(stats_frame, text="Listo para ejecutar", font=("Arial", 10))
        self.stats_label.pack(side='left')

        # Estado de licencia
        self.license_status = ttk.Label(stats_frame, text="Verificando licencia...", font=("Arial", 9))
        self.license_status.pack(side='right')

        def load_default_config(self):
        """Cargar configuración por defecto para testing"""
        base_dir = os.path.join(current_dir, "test_limpieza")

        if not os.path.exists(base_dir):
            try:
                os.makedirs(base_dir, exist_ok=True)

                # Crear carpetas para ubicaciones
                location_dirs = {}
                for location in self.ubicaciones.keys():
                    location_dir = os.path.join(base_dir, location.lower())
                    os.makedirs(location_dir, exist_ok=True)
                    location_dirs[location] = location_dir

                    # Crear archivos de prueba en cada ubicación
                    test_files = ["archivo1.txt", "documento2.pdf", "imagen3.jpg", "video4.mp4", "texto5.txt"]
                    for file in test_files:
                        with open(os.path.join(location_dir, file), 'w') as f:
                            f.write(f"Contenido de {file} en {location}")

                # Crear archivo de lista de prueba
                lista_path = os.path.join(base_dir, "lista_archivos.txt")
                with open(lista_path, 'w') as f:
                    files_to_delete = ["archivo1.txt", "documento2.pdf", "imagen3.jpg"]
                    for file in files_to_delete:
                        f.write(f"{file}\n")

                self.archivo_lista.set(lista_path)

                # Configurar rutas de ubicaciones
                for location, location_dir in location_dirs.items():
                    self.ubicaciones[location]["path"].set(location_dir)

                self.log_message("✅ Entorno de prueba creado")

            except Exception as e:
                self.log_message(f"❌ Error creando entorno: {e}")
        else:
            # Cargar configuración existente
            lista_path = os.path.join(base_dir, "lista_archivos.txt")
            if os.path.exists(lista_path):
                self.archivo_lista.set(lista_path)

            for location in self.ubicaciones.keys():
                location_dir = os.path.join(base_dir, location.lower())
                if os.path.exists(location_dir):
                    self.ubicaciones[location]["path"].set(location_dir)

        def log_message(self, message):
        """Agregar mensaje al log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()


        def preview_list(self):
        """Previsualizar archivos en la lista"""
        if not self.archivo_lista.get():
            messagebox.showwarning("Advertencia", "Debe seleccionar un archivo de lista")
            return

        if not os.path.exists(self.archivo_lista.get()):
            messagebox.showerror("Error", "El archivo de lista no existe")
            return

        try:
            with open(self.archivo_lista.get(), 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Limpiar líneas
            files = [line.strip() for line in lines if line.strip()]

            if not files:
                messagebox.showinfo("Lista Vacía", "El archivo de lista está vacío")
                return

            # Mostrar previsualización
            preview_window = tk.Toplevel(self.root)
            preview_window.title("Previsualización de Lista")
            preview_window.geometry("500x400")
            preview_window.resizable(True, True)

            frame = ttk.Frame(preview_window, padding="10")
            frame.pack(fill='both', expand=True)

            ttk.Label(frame, text=f"Archivos a eliminar ({len(files)} archivos):", 
                     font=("Arial", 12, "bold")).pack(pady=(0,10))

            # Lista de archivos
            listbox = tk.Listbox(frame, height=15)
            listbox.pack(fill='both', expand=True, pady=(0,10))

            for file in files:
                listbox.insert(tk.END, file)

            # Botón cerrar
            ttk.Button(frame, text="Cerrar", 
                      command=preview_window.destroy).pack()

        except Exception as e:
            messagebox.showerror("Error", f"Error leyendo archivo: {e}")

        def execute_cleanup(self):
        """Ejecutar limpieza por lista"""
        if self.running:
            return

        # Validaciones
        if not self.archivo_lista.get():
            messagebox.showerror("Error", "Debe seleccionar un archivo de lista")
            return

        if not os.path.exists(self.archivo_lista.get()):
            messagebox.showerror("Error", "El archivo de lista no existe")
            return

        # Verificar ubicaciones habilitadas
        enabled_locations = [loc for loc, vars_dict in self.ubicaciones.items() 
                           if vars_dict["enabled"].get()]

        if not enabled_locations:
            messagebox.showerror("Error", "Debe habilitar al menos una ubicación")
            return

        # Advertencia si no tiene licencia
        if not self.licensed:
            response = messagebox.askyesno("Sin Licencia", 
                "La aplicación no está licenciada. Puede continuar en modo demo.\n\n¿Continuar?")
            if not response:
                return

        # Confirmar operación
        if not self.dry_run.get():
            response = messagebox.askyesno("Confirmar", 
                f"¿Está seguro de eliminar archivos en {len(enabled_locations)} ubicaciones?\n\n"
                "Esta operación no se puede deshacer.")
            if not response:
                return

        # Iniciar ejecución
        self.running = True
        self.execute_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.progress.config(value=0)

        # Resetear progreso por ubicación
        for progress_bar in self.location_progress.values():
            progress_bar.config(value=0)

        thread = threading.Thread(target=self._execute_cleanup_thread)
        thread.daemon = True
        thread.start()

        def stop_cleanup(self):
        """Detener limpieza"""
        if self.running:
            self.running = False
            self.log_message("⏹ Deteniendo limpieza...")

        def _reset_ui(self):
        """Restaurar estado de la UI"""
        self.execute_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.progress.config(value=0)
        for progress_bar in self.location_progress.values():
            progress_bar.config(value=0)


        def _execute_cleanup_thread(self):
        """Hilo de ejecución de limpieza"""
        try:
            self.log_message("🗑️ Iniciando limpieza por lista...")
            self.log_message(f"📄 Archivo de lista: {self.archivo_lista.get()}")
            self.log_message(f"🔧 Modo: {'Simulación' if self.dry_run.get() else 'Real'}")

            # Leer archivo de lista
            with open(self.archivo_lista.get(), 'r', encoding='utf-8') as f:
                lines = f.readlines()

            files_to_delete = [line.strip() for line in lines if line.strip()]
            self.log_message(f"📋 Archivos en lista: {len(files_to_delete)}")

            if not files_to_delete:
                self.log_message("❌ Lista vacía")
                return

            # Ubicaciones habilitadas
            enabled_locations = [(loc, vars_dict) for loc, vars_dict in self.ubicaciones.items() 
                               if vars_dict["enabled"].get()]

            self.log_message(f"📁 Ubicaciones habilitadas: {len(enabled_locations)}")

            total_operations = len(files_to_delete) * len(enabled_locations)
            operations_completed = 0

            # Estadísticas
            stats = {
                "total_files": len(files_to_delete),
                "total_locations": len(enabled_locations),
                "files_found": 0,
                "files_deleted": 0,
                "errors": 0
            }

            # Procesar cada ubicación
            for location, vars_dict in enabled_locations:
                if not self.running:
                    break

                location_path = vars_dict["path"].get()
                self.log_message(f"\n📁 Procesando {location}: {location_path}")

                if not location_path or not os.path.exists(location_path):
                    self.log_message(f"❌ {location}: Ruta no válida")
                    stats["errors"] += len(files_to_delete)
                    operations_completed += len(files_to_delete)
                    continue

                location_found = 0
                location_deleted = 0

                # Procesar archivos en esta ubicación
                for i, file_name in enumerate(files_to_delete):
                    if not self.running:
                        break

                    file_path = os.path.join(location_path, file_name)

                    if os.path.exists(file_path):
                        location_found += 1
                        stats["files_found"] += 1

                        if self.dry_run.get():
                            self.log_message(f"💾 (SIMULADO) {location}: {file_name}")
                            location_deleted += 1
                            stats["files_deleted"] += 1
                        else:
                            try:
                                os.remove(file_path)
                                self.log_message(f"🗑️ {location}: {file_name} eliminado")
                                location_deleted += 1
                                stats["files_deleted"] += 1
                            except Exception as e:
                                self.log_message(f"❌ {location}: Error eliminando {file_name}: {e}")
                                stats["errors"] += 1
                    else:
                        self.log_message(f"⚪ {location}: {file_name} no encontrado")

                    operations_completed += 1

                    # Actualizar progreso
                    progress_percent = (operations_completed / total_operations) * 100
                    location_progress_percent = ((i + 1) / len(files_to_delete)) * 100

                    self.root.after(0, lambda p=progress_percent: self.progress.config(value=p))
                    self.root.after(0, lambda loc=location, lp=location_progress_percent: 
                                   self.location_progress[loc].config(value=lp))

                    # Actualizar estadísticas
                    stats_text = (f"Procesados: {operations_completed}/{total_operations} | "
                                f"Encontrados: {stats['files_found']} | "
                                f"Eliminados: {stats['files_deleted']} | "
                                f"Errores: {stats['errors']}")
                    self.root.after(0, lambda: self.stats_label.config(text=stats_text))

                    time.sleep(0.1)  # Simular procesamiento

                self.log_message(f"✅ {location}: {location_found} encontrados, {location_deleted} procesados")

            # Resultados finales
            if self.running:
                self.log_message("\n" + "=" * 60)
                self.log_message(f"✅ LIMPIEZA COMPLETADA")
                self.log_message(f"📊 Archivos en lista: {stats['total_files']}")
                self.log_message(f"📁 Ubicaciones procesadas: {stats['total_locations']}")
                self.log_message(f"🔍 Archivos encontrados: {stats['files_found']}")
                self.log_message(f"🗑️ Archivos eliminados: {stats['files_deleted']}")
                self.log_message(f"❌ Errores: {stats['errors']}")

                if self.dry_run.get():
                    self.log_message("💾 Modo simulación - No se realizaron cambios")

                self.log_message("=" * 60)

                # Mostrar resumen
                self.root.after(0, lambda: messagebox.showinfo("Completado", 
                    f"Limpieza completada\n\n"
                    f"Archivos en lista: {stats['total_files']}\n"
                    f"Ubicaciones: {stats['total_locations']}\n"
                    f"Encontrados: {stats['files_found']}\n"
                    f"Eliminados: {stats['files_deleted']}\n"
                    f"Errores: {stats['errors']}\n\n"
                    f"Modo: {'Simulación' if self.dry_run.get() else 'Real'}"))

        except Exception as e:
            self.log_message(f"❌ ERROR: {e}")
            self.logger.error(f"Cleanup error: {e}")

        finally:
            # Restaurar UI
            self.running = False
            self.root.after(0, self._reset_ui)

        def run(self):
        """Ejecutar aplicación"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.logger.info("Application stopped by user")
        except Exception as e:
            self.logger.error(f"Application error: {e}")

    # Aplicar métodos a la clase
    
    
    
    
    
    
    
    
    
    
    
    
    

    if __name__ == "__main__":
        print("🗑️ Gestor - Limpieza por Lista GUI")
        print("=" * 40)

        app = CleanupByListGUI()
        app.run()


