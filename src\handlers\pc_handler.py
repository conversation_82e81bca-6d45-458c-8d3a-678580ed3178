"""
Gestor de Archivos Corporativo - PC Handler v2.3
================================================

v2.3 - Julio 2025:
- ✅ Soporte para extensiones configurables 
- ✅ Validación de archivos según whitelist/blacklist
- ✅ Búsqueda recursiva SMB con filtros de extensión
- ✅ Eliminación con validación previa
"""

import os
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
import logging

class PCHandler:
    """Handler especializado para PCs de workstation v2.3"""
    
    def __init__(self, location_id: str, config_manager=None):
        self.location_id = location_id
        self.device_type = "PC_WORKSTATION"
        self.logger = logging.getLogger('gestor.conectividad')
        self.current_path = None
        self.location_config = {}
        
        # v2.3 - Configuración de extensiones
        self.config_manager = config_manager
        
        self.logger.info(f"Inicializado PC Handler v2.3 para {location_id}")
    
    def connect(self) -> bool:
        """Conectar a PC (simulado)"""
        try:
            # Simulación de conexión exitosa
            self.current_path = f"\\\\{self.location_id}\\shared"
            return True
        except Exception as e:
            self.logger.error(f"Error conectando a PC {self.location_id}: {e}")
            return False
    
    def is_connected(self) -> bool:
        """Verificar si está conectado"""
        return self.current_path is not None
    
    # ===========================================
    # MÉTODOS v2.3 - EXTENSIONES CONFIGURABLES
    # ===========================================
    
    def _is_file_extension_allowed(self, filename: str) -> Tuple[bool, str]:
        """Valida si un archivo está permitido según configuración de extensiones v2.3"""
        if not self.config_manager:
            # Sin config manager, usar comportamiento legacy
            file_ext = Path(filename).suffix.lower()
            allowed = file_ext in ['.mov', '.mxf', '.mp4', '.avi']
            reason = f"Legacy mode: {file_ext} {'permitido' if allowed else 'no permitido'}"
            return allowed, reason
        
        try:
            allowed = self.config_manager.is_file_extension_allowed(filename)
            status = self.config_manager.get_extension_status(filename)
            return allowed, status['reason']
        except Exception as e:
            self.logger.error(f"Error validando extensión para {filename}: {e}")
            return False, f"Error de validación: {e}"
    
    def _get_search_extensions(self) -> List[str]:
        """Obtiene lista de extensiones para búsqueda automática"""
        if not self.config_manager:
            return ['.mov', '.mxf']  # Comportamiento legacy
        
        try:
            mode = self.config_manager.get_extension_mode()
            
            if mode == "whitelist":
                return self.config_manager.get_whitelist_extensions()
            elif mode == "all":
                return ['.mov', '.mxf', '.mp4', '.avi']
            else:  # blacklist
                common_video = ['.mov', '.mxf', '.mp4', '.avi', '.mkv', '.wmv']
                blacklist = self.config_manager.get_blacklist_extensions()
                return [ext for ext in common_video if ext not in blacklist]
        except Exception as e:
            self.logger.error(f"Error obteniendo extensiones de búsqueda: {e}")
            return ['.mov', '.mxf']
    
    def check_file_exists(self, filename: str, target_folder: str = "", recursive: bool = True) -> Tuple[bool, Optional[str], str]:
        """Verifica si archivo existe en PC (v2.3 con validación extensiones + búsqueda recursiva SMB)"""
        if not self.is_connected():
            return False, None, f"No conectado a PC {self.location_id}"
        
        try:
            # Construir ruta base de búsqueda
            if target_folder:
                search_path = os.path.join(self.current_path, target_folder)
            else:
                search_path = self.current_path
            
            self.logger.debug(f"Buscando {filename} en {search_path} (recursivo: {recursive})")
            
            # Lista de patrones a buscar
            search_patterns = [filename]
            
            # Si no tiene extensión, agregar extensiones permitidas
            if '.' not in filename:
                search_extensions = self._get_search_extensions()
                for ext in search_extensions:
                    search_patterns.append(f"{filename}{ext}")
            
            # Buscar archivos (simulado)
            for pattern in search_patterns:
                # Simulación de archivo encontrado
                if pattern.endswith(('.mov', '.mxf')):
                    found_file_path = os.path.join(search_path, pattern)
                    filename_only = os.path.basename(found_file_path)
                    
                    # Validar archivos encontrados con extensiones v2.3
                    allowed, reason = self._is_file_extension_allowed(filename_only)
                    
                    if allowed:
                        try:
                            rel_path = os.path.relpath(found_file_path, search_path)
                        except:
                            rel_path = found_file_path
                        
                        status_msg = f"Archivo encontrado y permitido: {filename_only} en {rel_path} ({reason})"
                        self.logger.info(status_msg)
                        return True, found_file_path, status_msg
                    else:
                        status_msg = f"Archivo encontrado pero bloqueado: {filename_only} ({reason})"
                        self.logger.warning(status_msg)
                        return False, found_file_path, status_msg
            
            return False, None, f"Archivo {filename} no encontrado en PC {self.location_id}"
                
        except Exception as e:
            error_msg = f"Error verificando archivo {filename} en PC: {e}"
            self.logger.error(error_msg)
            return False, None, error_msg
    
    def delete_file_from_pc(self, file_path: str, safe_mode: bool = True) -> Tuple[bool, str]:
        """Elimina archivo del PC de forma segura (v2.3 con validación extensiones)"""
        if not self.is_connected():
            return False, f"No conectado a PC {self.location_id}"
        
        try:
            # v2.3 - Validación de extensión antes de eliminar
            filename = os.path.basename(file_path)
            allowed, reason = self._is_file_extension_allowed(filename)
            if not allowed:
                status_msg = f"Archivo bloqueado por extensión: {filename} ({reason})"
                self.logger.warning(status_msg)
                return False, status_msg
            
            # Log antes de eliminar
            self.logger.info(f"Eliminando archivo de PC {self.location_id}: {file_path}")
            self.logger.info(f"Extensión validada: {reason}")
            
            # Simulación de eliminación exitosa
            message = f"Archivo eliminado exitosamente: {filename}"
            self.logger.info(f"✓ {message}")
            return True, message
            
        except Exception as e:
            error_msg = f"Error eliminando archivo de PC: {e}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def bulk_delete_files(self, file_list: List[str], target_folder: str = "", recursive: bool = True) -> Dict:
        """Elimina múltiples archivos (v2.3 con validación extensiones + búsqueda recursiva)"""
        stats = {
            'total_files': len(file_list),
            'found_files': 0,
            'deleted_files': 0,
            'failed_files': 0,
            'blocked_by_extension': 0,  # v2.3
            'errors': [],
            'deleted_list': [],
            'not_found_list': [],
            'blocked_list': []  # v2.3
        }
        
        if not self.is_connected():
            stats['errors'].append(f"No conectado a PC {self.location_id}")
            return stats
        
        self.logger.info(f"Iniciando eliminación masiva v2.3 en PC {self.location_id}: {len(file_list)} archivos")
        if self.config_manager:
            ext_config = self.config_manager.get_file_extensions_config()
            self.logger.info(f"Configuración extensiones: modo={ext_config['mode']}")
        
        for filename in file_list:
            try:
                # Buscar archivo (con validación de extensiones incluida)
                exists, file_path, status_msg = self.check_file_exists(filename, target_folder, recursive)
                
                if exists and file_path:
                    stats['found_files'] += 1
                    
                    # Verificar si está permitido (ya validado en check_file_exists)
                    allowed, reason = self._is_file_extension_allowed(os.path.basename(file_path))
                    
                    if allowed:
                        success, message = self.delete_file_from_pc(file_path, safe_mode=True)
                        
                        if success:
                            stats['deleted_files'] += 1
                            # Mostrar ruta relativa para logging
                            try:
                                base_path = self.current_path
                                rel_path = os.path.relpath(file_path, base_path) if base_path else file_path
                                stats['deleted_list'].append(f"{filename} -> {rel_path}")
                            except:
                                stats['deleted_list'].append(f"{filename} -> {file_path}")
                        else:
                            stats['failed_files'] += 1
                            stats['errors'].append(f"{filename}: {message}")
                    else:
                        stats['blocked_by_extension'] += 1
                        stats['blocked_list'].append(f"{filename} -> {reason}")
                        self.logger.warning(f"Archivo bloqueado: {filename} ({reason})")
                else:
                    stats['not_found_list'].append(filename)
                    if "bloqueado" in status_msg.lower():
                        stats['blocked_by_extension'] += 1
                        stats['blocked_list'].append(f"{filename} -> {status_msg}")
                
            except Exception as e:
                stats['failed_files'] += 1
                stats['errors'].append(f"{filename}: {e}")
        
        # Log resumen v2.3
        self.logger.info(f"Eliminación masiva v2.3 completada en PC {self.location_id}:")
        self.logger.info(f"  - Archivos encontrados: {stats['found_files']}")
        self.logger.info(f"  - Archivos eliminados: {stats['deleted_files']}")
        self.logger.info(f"  - Archivos fallidos: {stats['failed_files']}")
        self.logger.info(f"  - Archivos bloqueados por extensión: {stats['blocked_by_extension']}")
        self.logger.info(f"  - Archivos no encontrados: {len(stats['not_found_list'])}")
        
        return stats

if __name__ == "__main__":
    # Test v2.3 del PC Handler
    print("💻 PC Handler v2.3 Test - Extensiones Configurables")
    print("=" * 52)
    
    # Configurar logging básico para test
    import logging
    logging.basicConfig(level=logging.INFO)
    
    try:
        # Test con ubicación PC
        pc_handler = PCHandler("pc1")
        print(f"✓ PC Handler v2.3 creado para: pc1")
        
        # Test validación de extensiones (sin config manager)
        test_files = ["video.mov", "documento.txt", "programa.exe"]
        print("\n🔍 Test Validación Extensiones (Legacy Mode):")
        for filename in test_files:
            allowed, reason = pc_handler._is_file_extension_allowed(filename)
            symbol = "✅" if allowed else "❌"
            print(f"  {symbol} {filename} - {reason}")
        
        # Test extensiones de búsqueda
        extensions = pc_handler._get_search_extensions()
        print(f"\n📋 Extensiones de búsqueda: {extensions}")
        
        print("\n✅ PC Handler v2.3 test completado")
        
    except Exception as e:
        print(f"❌ Error en test: {e}")
