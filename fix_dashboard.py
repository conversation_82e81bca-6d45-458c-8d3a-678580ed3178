#!/usr/bin/env python3
"""
fix_dashboard.py
Script para corregir errores comunes post-merge en dashboard.py

Uso:
    python fix_dashboard.py

Correcciones automáticas:
- Bloques try/except incompletos
- Indentación incorrecta 
- Imports duplicados restantes
- Líneas vacías excesivas

Autor: Igson
Fecha: Junio 2025
"""

import re
from pathlib import Path
from datetime import datetime

def main():
    """Función principal de corrección"""
    
    print("🔧 CORRECTOR AUTOMÁTICO - DASHBOARD")
    print("=" * 40)
    
    dashboard_file = Path("dashboard.py")
    
    if not dashboard_file.exists():
        print("❌ ERROR: dashboard.py no encontrado")
        print("   Ejecuta merge_dashboard.py primero")
        input("Presiona Enter para salir...")
        return False
    
    print(f"📁 Archivo encontrado: {dashboard_file}")
    print(f"📏 Tamaño original: {dashboard_file.stat().st_size / 1024:.1f} KB")
    
    # Hacer backup
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = Path(f"dashboard_antes_fix_{timestamp}.py")
    backup_file.write_text(dashboard_file.read_text(encoding='utf-8'), encoding='utf-8')
    print(f"💾 Backup creado: {backup_file.name}")
    
    # Leer contenido
    print(f"\n🔄 Leyendo archivo...")
    content = dashboard_file.read_text(encoding='utf-8')
    original_lines = len(content.splitlines())
    
    # Aplicar correcciones
    print(f"🔧 Aplicando correcciones...")
    
    # 1. Corregir bloques try incompletos
    print(f"  📝 Corrigiendo bloques try/except...")
    content = fix_try_blocks(content)
    
    # 2. Corregir indentación
    print(f"  📝 Corrigiendo indentación...")
    content = fix_indentation(content)
    
    # 3. Limpiar imports duplicados remanentes
    print(f"  📝 Limpiando imports...")
    content = clean_remaining_imports(content)
    
    # 4. Limpiar líneas vacías excesivas
    print(f"  📝 Limpiando líneas vacías...")
    content = clean_empty_lines(content)
    
    # 5. Corregir métodos huérfanos
    print(f"  📝 Corrigiendo métodos huérfanos...")
    content = fix_orphan_methods(content)
    
    # Escribir archivo corregido
    print(f"\n💾 Escribiendo archivo corregido...")
    dashboard_file.write_text(content, encoding='utf-8')
    
    # Estadísticas
    new_size = dashboard_file.stat().st_size / 1024
    new_lines = len(content.splitlines())
    
    print(f"\n🎉 ¡CORRECCIÓN COMPLETADA!")
    print(f"=" * 30)
    print(f"📏 Tamaño final: {new_size:.1f} KB")
    print(f"📊 Líneas finales: {new_lines:,}")
    print(f"🔄 Diferencia: {new_lines - original_lines:+,} líneas")
    
    # Verificación final
    print(f"\n🔍 Verificación final...")
    if verify_syntax(dashboard_file):
        print(f"  ✅ Sintaxis correcta")
        print(f"  ✅ Archivo listo para usar")
        
        print(f"\n🚀 SIGUIENTE PASO:")
        print(f"  python dashboard.py")
        
        return True
    else:
        print(f"  ⚠️  Aún hay problemas menores")
        print(f"  💡 Intenta ejecutar de todas formas")
        return False

def fix_try_blocks(content: str) -> str:
    """Corregir bloques try/except incompletos"""
    
    lines = content.splitlines()
    fixed_lines = []
    
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # Detectar try statement sin contenido
        if line.strip().endswith('try:'):
            fixed_lines.append(line)
            i += 1
            
            # Buscar la siguiente línea no vacía
            while i < len(lines) and not lines[i].strip():
                fixed_lines.append(lines[i])
                i += 1
            
            # Si la siguiente línea no está indentada correctamente
            if i < len(lines):
                next_line = lines[i]
                if next_line.strip() and not next_line.startswith('    '):
                    # Insertar un pass indentado
                    base_indent = len(line) - len(line.lstrip())
                    fixed_lines.append(' ' * (base_indent + 4) + 'pass')
                    fixed_lines.append(next_line)
                    i += 1
                else:
                    fixed_lines.append(next_line)
                    i += 1
            else:
                # Try al final del archivo
                base_indent = len(line) - len(line.lstrip())
                fixed_lines.append(' ' * (base_indent + 4) + 'pass')
                break
        else:
            fixed_lines.append(line)
            i += 1
    
    return '\n'.join(fixed_lines)

def fix_indentation(content: str) -> str:
    """Corregir problemas de indentación"""
    
    lines = content.splitlines()
    fixed_lines = []
    
    for i, line in enumerate(lines):
        # Detectar líneas que deberían estar indentadas pero no lo están
        if line.strip():
            prev_line = lines[i-1] if i > 0 else ""
            
            # Si la línea anterior termina en : y esta línea no está indentada
            if (prev_line.strip().endswith(':') and 
                line.strip() and 
                not line.startswith('    ') and
                not line.startswith('#') and
                not line.startswith('"""') and
                not line.strip().startswith('def ') and
                not line.strip().startswith('class ')):
                
                # Calcular indentación base de la línea anterior
                base_indent = len(prev_line) - len(prev_line.lstrip())
                fixed_line = ' ' * (base_indent + 4) + line.strip()
                fixed_lines.append(fixed_line)
            else:
                fixed_lines.append(line)
        else:
            fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)

def clean_remaining_imports(content: str) -> str:
    """Limpiar imports duplicados que pueden haber quedado"""
    
    lines = content.splitlines()
    imports_seen = set()
    fixed_lines = []
    
    in_import_section = True
    
    for line in lines:
        stripped = line.strip()
        
        # Detectar fin de sección de imports
        if stripped and not (stripped.startswith('import ') or 
                           stripped.startswith('from ') or
                           stripped.startswith('#') or
                           stripped.startswith('"""') or
                           stripped == ''):
            in_import_section = False
        
        # Si estamos en la sección de imports y es un import
        if in_import_section and (stripped.startswith('import ') or stripped.startswith('from ')):
            if stripped not in imports_seen:
                imports_seen.add(stripped)
                fixed_lines.append(line)
            # Si es duplicado, lo saltamos
        else:
            fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)

def clean_empty_lines(content: str) -> str:
    """Limpiar líneas vacías excesivas"""
    
    lines = content.splitlines()
    fixed_lines = []
    
    empty_count = 0
    
    for line in lines:
        if line.strip() == '':
            empty_count += 1
            if empty_count <= 2:  # Máximo 2 líneas vacías consecutivas
                fixed_lines.append(line)
        else:
            empty_count = 0
            fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)

def fix_orphan_methods(content: str) -> str:
    """Corregir métodos que pueden estar fuera de la clase"""
    
    lines = content.splitlines()
    fixed_lines = []
    
    class_found = False
    class_indent = 0
    
    for line in lines:
        stripped = line.strip()
        
        # Detectar clase principal
        if stripped.startswith('class GestorDashboard'):
            class_found = True
            class_indent = len(line) - len(line.lstrip())
            fixed_lines.append(line)
            continue
        
        # Si encontramos un método fuera de la clase
        if (class_found and 
            stripped.startswith('def ') and 
            not line.startswith(' ' * (class_indent + 4))):
            
            # Re-indentar el método para que esté dentro de la clase
            method_indent = class_indent + 4
            fixed_line = ' ' * method_indent + stripped
            fixed_lines.append(fixed_line)
        else:
            fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)

def verify_syntax(file_path: Path) -> bool:
    """Verificar que la sintaxis sea correcta"""
    try:
        content = file_path.read_text(encoding='utf-8')
        compile(content, str(file_path), 'exec')
        return True
    except SyntaxError as e:
        print(f"    ❌ Error de sintaxis restante: {e}")
        print(f"    📍 Línea {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"    ❌ Error verificando: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        input("\nPresiona Enter para continuar...")
        if not success:
            exit(1)
    except KeyboardInterrupt:
        print(f"\n\n🛑 Operación cancelada")
        exit(1)
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        input("Presiona Enter para salir...")
        exit(1)
