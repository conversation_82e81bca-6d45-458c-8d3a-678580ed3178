"""
Location Configuration Widget - Fixed Version with Real Connectivity Testing
=========================================================================

Widget que implementa validación REAL de conectividad
Soluciona el problema de falsos positivos en validación de conexiones
"""

import tkinter as tk
from tkinter import ttk
import os
import json
import threading
import time
import subprocess
import socket
from pathlib import Path
import platform

class LocationConfigWidget:
    """Widget con validación REAL de conectividad - Sin falsos positivos"""
    
    def __init__(self, parent, config_manager=None, logger=None):
        self.parent = parent
        self.logger = logger
        
        # Cargar config.json
        self.config_file = os.path.join(os.path.dirname(__file__), "config.json")
        self.locations_data = {}
        self.locations_config = {}
        
        self.init_location_vars()
        self.create_widget()
        self.load_config_from_json()
    
    def init_location_vars(self):
        """Inicializar variables"""
        for location_id in ['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4']:
            self.locations_config[location_id] = {
                'enabled': tk.BooleanVar(value=True),
                'network_path': tk.StringVar(),
                'hostname': tk.StringVar(),
                'share': tk.StringVar(),
                'username': tk.StringVar(),
                'subfolder': tk.StringVar(),
                'status': tk.StringVar(value="Sin configurar")
            }
    
    def create_widget(self):
        """Crear interfaz"""
        main_frame = ttk.LabelFrame(self.parent, text="Configuración de Ubicaciones", padding="10")
        main_frame.pack(fill='both', expand=True, pady=(10,0))
        
        # Info del config con estado
        info_text = f"📁 Config: {self.config_file}"
        if not os.path.exists(self.config_file):
            info_text += " (⚠️ NO ENCONTRADO - Usando valores por defecto)"
        ttk.Label(main_frame, text=info_text, font=('Arial', 8)).pack(anchor='w')
        
        # Grid de ubicaciones
        grid_frame = ttk.Frame(main_frame)
        grid_frame.pack(fill='both', expand=True, pady=(10,0))
        
        # Headers
        ttk.Label(grid_frame, text="Ubicación", font=("Arial", 10, "bold")).grid(row=0, column=0, padx=5, pady=2, sticky='w')
        ttk.Label(grid_frame, text="Estado", font=("Arial", 10, "bold")).grid(row=0, column=1, padx=20, pady=2, sticky='w')
        ttk.Label(grid_frame, text="Ruta/Host", font=("Arial", 10, "bold")).grid(row=0, column=2, padx=20, pady=2, sticky='w')
        ttk.Label(grid_frame, text="Habilitado", font=("Arial", 10, "bold")).grid(row=0, column=3, padx=20, pady=2, sticky='w')
        
        ttk.Separator(grid_frame, orient='horizontal').grid(row=1, column=0, columnspan=4, sticky='ew', pady=5)
        
        # Ubicaciones
        self.status_labels = {}
        self.path_labels = {}
        for i, location_id in enumerate(['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4']):
            row = i + 2
            
            ttk.Label(grid_frame, text=location_id.upper(), font=("Arial", 9, "bold")).grid(row=row, column=0, sticky='w', padx=5, pady=2)
            
            status_label = ttk.Label(grid_frame, textvariable=self.locations_config[location_id]['status'])
            status_label.grid(row=row, column=1, sticky='w', padx=20, pady=2)
            self.status_labels[location_id] = status_label
            
            path_label = ttk.Label(grid_frame, text="", font=("Arial", 8), foreground="blue")
            path_label.grid(row=row, column=2, sticky='w', padx=20, pady=2)
            self.path_labels[location_id] = path_label
            
            ttk.Checkbutton(grid_frame, variable=self.locations_config[location_id]['enabled']).grid(row=row, column=3, padx=20, pady=2)
        
        # Botones
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill='x', pady=(10,0))
        
        ttk.Button(button_frame, text="🔄 Actualizar Display", command=self.update_display).pack(side='left', padx=(0,10))
        ttk.Button(button_frame, text="🧪 Test General", command=self.test_all_connections).pack(side='left')
        
        # Info de estado
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill='x', pady=(10,0))
        
        ttk.Label(info_frame, text="ℹ️ Estados: Sin configurar | Testing... | ✅ Conectado | ❌ Error red | 🔐 Error credenciales", 
                 font=('Arial', 8), foreground='gray').pack(anchor='w')
    
    def load_config_from_json(self):
        """Cargar desde config.json con fallback a configuración por defecto"""
        try:
            # Intentar cargar config.json
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.locations_data = data.get('locations', {})
                
                for location_id, config_vars in self.locations_config.items():
                    if location_id in self.locations_data:
                        loc_config = self.locations_data[location_id]
                        
                        config_vars['enabled'].set(loc_config.get('enabled', True))
                        config_vars['network_path'].set(loc_config.get('network_path', ''))
                        config_vars['subfolder'].set(loc_config.get('subfolder', ''))
                        config_vars['hostname'].set(loc_config.get('hostname', ''))
                        config_vars['share'].set(loc_config.get('share', ''))
                        config_vars['username'].set(loc_config.get('username', ''))
                
                self.log_message(f"✅ Config cargado: {len(self.locations_data)} ubicaciones")
            else:
                # Config.json no existe - usar configuración por defecto
                self.log_message(f"⚠️ Config.json no encontrado: {self.config_file}")
                self.log_message("🛠️ Usando configuración por defecto (vacía)")
                self.locations_data = {}
                
                # Mantener configuración vacía pero funcional
                for location_id in ['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4']:
                    self.locations_config[location_id]['enabled'].set(True)
                    self.locations_config[location_id]['network_path'].set('')
                    self.locations_config[location_id]['hostname'].set('')
                    self.locations_config[location_id]['share'].set('')
                    self.locations_config[location_id]['username'].set('')
                    self.locations_config[location_id]['subfolder'].set('')
            
            self.update_display()
            
        except Exception as e:
            self.log_message(f"❌ Error cargando config: {e}")
            # En caso de error, usar configuración vacía pero funcional
            self.locations_data = {}
            self.update_display()
    
    def update_display(self):
        """Actualizar información mostrada sin test de conectividad"""
        for location_id in ['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4']:
            if not self.locations_config[location_id]['enabled'].get():
                self.locations_config[location_id]['status'].set("⚪ Deshabilitado")
                self.path_labels[location_id].config(text="Deshabilitado")
                continue
            
            if location_id.startswith('qnap'):
                path = self.locations_config[location_id]['network_path'].get()
                if path:
                    self.path_labels[location_id].config(text=path)
                    # Solo mostrar "Configurado" si está configurado, no "Conectado"
                    self.locations_config[location_id]['status'].set("⚙️ Configurado")
                else:
                    self.path_labels[location_id].config(text="Sin configurar")
                    self.locations_config[location_id]['status'].set("❓ Sin configurar")
            else:
                hostname = self.locations_config[location_id]['hostname'].get()
                share = self.locations_config[location_id]['share'].get()
                
                if hostname and share:
                    self.path_labels[location_id].config(text=f"{hostname}/{share}")
                    # CAMBIO CRÍTICO: Solo "Configurado", NO "Conectado" hasta hacer test real
                    self.locations_config[location_id]['status'].set("⚙️ Configurado")
                else:
                    self.path_labels[location_id].config(text="Sin configurar")
                    self.locations_config[location_id]['status'].set("❓ Sin configurar")
    
    def ping_host(self, hostname, timeout=3):
        """Test de ping con timeout corto"""
        try:
            if platform.system().lower() == "windows":
                # Windows
                cmd = f"ping -n 1 -w {timeout*1000} {hostname}"
            else:
                # Linux/Mac
                cmd = f"ping -c 1 -W {timeout} {hostname}"
            
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout+1)
            return result.returncode == 0
            
        except (subprocess.TimeoutExpired, Exception) as e:
            self.log_message(f"❌ Ping timeout/error para {hostname}: {e}")
            return False
    
    def test_smb_connection(self, hostname, share, username="", timeout=3):
        """Test de conexión SMB básica"""
        try:
            # Test 1: Puerto 445 (SMB)
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((hostname, 445))
            sock.close()
            
            if result == 0:
                self.log_message(f"✅ Puerto SMB 445 abierto en {hostname}")
                return True
            else:
                self.log_message(f"❌ Puerto SMB 445 cerrado en {hostname}")
                return False
                
        except Exception as e:
            self.log_message(f"❌ Error test SMB {hostname}: {e}")
            return False
    
    def test_network_path(self, network_path, timeout=3):
        """Test de acceso a ruta de red"""
        try:
            if not network_path or not network_path.startswith('\\\\'):
                return False
            
            # Extraer hostname de la ruta de red
            parts = network_path.replace('\\\\', '').split('\\')
            if len(parts) < 2:
                return False
            
            hostname = parts[0]
            share = parts[1]
            
            # Test ping primero
            if not self.ping_host(hostname, timeout):
                return False
            
            # Test SMB
            return self.test_smb_connection(hostname, share, timeout=timeout)
            
        except Exception as e:
            self.log_message(f"❌ Error test ruta de red {network_path}: {e}")
            return False
    
    def test_location_connectivity(self, location_id):
        """Test de conectividad para una ubicación específica"""
        try:
            if not self.locations_config[location_id]['enabled'].get():
                return "⚪ Deshabilitado"
            
            if location_id.startswith('qnap'):
                # QNAP: Test ruta de red
                network_path = self.locations_config[location_id]['network_path'].get()
                
                if not network_path:
                    return "❓ Sin configurar"
                
                self.log_message(f"🧪 Testing QNAP {location_id}: {network_path}")
                
                if self.test_network_path(network_path, timeout=3):
                    return "✅ Conectado"
                else:
                    return "❌ Error red"
            else:
                # PC: Test SMB
                hostname = self.locations_config[location_id]['hostname'].get()
                share = self.locations_config[location_id]['share'].get()
                username = self.locations_config[location_id]['username'].get()
                
                if not hostname or not share:
                    return "❓ Sin configurar"
                
                self.log_message(f"🧪 Testing PC {location_id}: {hostname}/{share}")
                
                # Test 1: Ping
                if not self.ping_host(hostname, timeout=3):
                    return "❌ Error red"
                
                # Test 2: SMB
                if self.test_smb_connection(hostname, share, username, timeout=3):
                    return "✅ Conectado"
                else:
                    return "🔐 Error credenciales"
        
        except Exception as e:
            self.log_message(f"❌ Error testing {location_id}: {e}")
            return "❌ Error"
    
    def test_all_connections(self):
        """Test REAL de conectividad - SIN FALSOS POSITIVOS"""
        def test_thread():
            try:
                self.log_message("🧪 INICIANDO TEST REAL DE CONECTIVIDAD...")
                self.log_message("⚠️ Se realizarán pings y tests SMB reales")
                
                for location_id in ['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4']:
                    # Mostrar estado "Testing..."
                    self.locations_config[location_id]['status'].set("🔄 Testing...")
                    
                    # Test real de conectividad
                    status = self.test_location_connectivity(location_id)
                    
                    # Actualizar estado final
                    self.locations_config[location_id]['status'].set(status)
                    
                    # Log detallado
                    if status == "✅ Conectado":
                        self.log_message(f"✅ {location_id.upper()}: CONECTADO exitosamente")
                    elif status == "❌ Error red":
                        self.log_message(f"❌ {location_id.upper()}: No se puede acceder (ping/puerto)")
                    elif status == "🔐 Error credenciales":
                        self.log_message(f"🔐 {location_id.upper()}: Credenciales incorrectas o permisos")
                    elif status == "❓ Sin configurar":
                        self.log_message(f"❓ {location_id.upper()}: Faltan datos de configuración")
                    
                    # Pequeña pausa entre tests
                    time.sleep(0.3)
                
                # Resumen final
                conectados = sum(1 for loc_id in ['qnap1', 'qnap2', 'pc1', 'pc2', 'pc3', 'pc4'] 
                               if self.locations_config[loc_id]['status'].get() == "✅ Conectado")
                
                self.log_message("=" * 50)
                self.log_message(f"🏁 TEST COMPLETADO: {conectados}/6 ubicaciones conectadas")
                self.log_message("=" * 50)
                
            except Exception as e:
                self.log_message(f"❌ Error en test de conectividad: {e}")
        
        thread = threading.Thread(target=test_thread)
        thread.daemon = True
        thread.start()
    

    
    def get_enabled_locations(self):
        """Obtener ubicaciones habilitadas"""
        enabled = {}
        
        for location_id, config_vars in self.locations_config.items():
            if config_vars['enabled'].get():
                enabled[location_id] = {
                    'path': config_vars['network_path'].get(),
                    'subfolder': config_vars['subfolder'].get(),
                    'hostname': config_vars['hostname'].get(),
                    'share': config_vars['share'].get(),
                    'username': config_vars['username'].get(),
                    'password': '',  # No almacenamos passwords
                    'domain': ''
                }
        
        return enabled
    
    def log_message(self, message):
        """Log mejorado con timestamp"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        print(f"[LocationConfig] {formatted_message}")
        
        # Si hay un logger externo, usarlo también
        if self.logger:
            self.logger.info(formatted_message)

if __name__ == "__main__":
    # Test standalone
    root = tk.Tk()
    root.title("Test Widget - Connectivity Fixed")
    root.geometry("700x500")
    
    widget = LocationConfigWidget(root)
    
    # Botón de test rápido
    test_frame = ttk.Frame(root)
    test_frame.pack(fill='x', padx=10, pady=5)
    
    ttk.Label(test_frame, text="Test rápido:", font=("Arial", 10, "bold")).pack(side='left')
    ttk.Button(test_frame, text="🧪 Test General", command=widget.test_all_connections).pack(side='left', padx=(10,0))
    
    root.mainloop()
